#!/bin/bash

# Verificar se o nome da VM foi passado como parâmetro
if [ -z "$1" ]; then
  echo "Erro: Nome da VM não foi fornecido."
  echo "Uso: $0 <nome_da_vm>"
  exit 1
fi

# Atribuir o parâmetro de entrada à variável vm_name
vm_name=$1

# Determinar o caminho de acordo com o nome da VM
case "$vm_name" in
  "api-gateway-auth-2024"|"api-gateway-auth2-2024"|"api-leads-2024"|"api-partner-portal-2024"|"api-student-portal-ava-2024"|"crons-runner-2024"|"piaget-public-api-2024"|"piaget-public-api-2-2024")
    echo "/opt"
    ;;
  "services-erp-1-2024"|"services-erp-2-2024-2")
    echo "/opt/services"
    ;;
  "services-erp-3-2024")
    echo "/opt"
    ;;
  *)
    echo "Erro: Nome da VM desconhecido: $vm_name"
    exit 1
    ;;
esac
