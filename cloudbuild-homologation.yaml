steps:
  # Criar pasta caso nao existir
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'create-tmp-folder'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo rm -rf /tmp/${_FOLDER} && mkdir -p /tmp/${_FOLDER}',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Transferir arquivos diretamente para a VM usando gcloud compute scp
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'copy-files'
    entrypoint: 'gcloud'
    args: [
      'compute', 'scp',
      '--recurse',
      '--compress',
      '--quiet',
      '/workspace',
      'services@${_VM}:/tmp/${_FOLDER}',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Preparar package.json
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'prepare-package-json'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && export CMD="bash build_scripts/prepare_package_json.sh" && if [ "${_ROOT}" = "true" ]; then sudo $$CMD; else $$CMD; fi',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Instalar dependências
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-dependencies'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && bash build_scripts/npm.sh ${_NODE_VERSION} i',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Instalar rsync (se nao houver)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-rsync'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'sudo apt-get install -y rsync',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Mover arquivos para o diretório final usando um script remoto
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'move-files'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'if [ "${_ROOT}" = "true" ]; then sudo rsync -av --remove-source-files /tmp/${_FOLDER}/workspace/* ${_LOCATION}/${_FOLDER}; else rsync -av --remove-source-files /tmp/${_FOLDER}/workspace; fi',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Notificar sucesso no Discord
  - name: 'curlimages/curl'
    id: 'notify-success'
    entrypoint: 'curl'
    args: [
      '-X', 'POST', '$_DISCORD_WEBHOOK_URL',
      '-H', 'Content-Type: application/json',
      '--data', '{"content": ":white_check_mark: Build concluído com sucesso! Build: $TRIGGER_NAME ($REF_NAME). Veja os logs: https://console.cloud.google.com/cloud-build/builds;region=$LOCATION/$BUILD_ID?project=$PROJECT_ID"}'
    ]
    waitFor: [ 'move-files' ]

timeout: '1200s'

options:
  logging: 'CLOUD_LOGGING_ONLY'
  pool:
    name: projects/erp-prominas/locations/us-west1/workerPools/builds

# Variaveis de substituição
substitutions:
  _VM: 'homologacao'
  _ROOT: 'true'
  _NODE_VERSION: '20.16.0'
  _LOCATION: '/opt/piaget/backend'
  _FOLDER: 'piaget_models_20'
  _DISCORD_WEBHOOK_URL: 'https://discord.com/api/webhooks/1279076153616896080/nkdczu_0_sNdwRnxGyFLMrXawXOWysbWazcH7Kdf01YgxkwgK-wt2yoCM7pr3k5tL60V'
