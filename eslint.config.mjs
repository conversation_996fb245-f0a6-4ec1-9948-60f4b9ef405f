import globals from "globals";
import pluginJs from "@eslint/js";

export default [
  {
    // Apply these rules to all JavaScript files
    files: ["**/*.js"],
    ignores: ["src/tests", "node_modules", "dist"],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: "module",
      globals: {
        ...globals.node
      },
    },
    rules: {
      // Error prevention
      "no-unused-vars": "warn",
      "no-undef": "error",
      "no-console": "off",

      // Style consistency
      "semi": ["error", "always"],
      "quotes": ["error", "single"],
      "indent": ["error", 2],
      "comma-dangle": ["error", "always-multiline"],

      // Best practices
      "eqeqeq": ["error", "always"],
      "no-var": "error",
      "prefer-const": "error",
    },
  },
  {
    // Special rules for configuration files
    files: ["*.config.js"],
    rules: {
      // Allow CommonJS syntax in config files
      "no-undef": "off",
    },
  },
  pluginJs.configs.recommended,
];
