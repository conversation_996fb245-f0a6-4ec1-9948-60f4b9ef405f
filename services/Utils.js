
// ATENÇÃO! Chamar a função diretamente não funciona, é precisa injetar o contexto da função que chama dentro dela usando "call".
// Ex.: _getModels.call(this, 'Students');
const request = require("request");

function _getModels(modelName) {
  let model = null;

  if (this.constructor && ['function'].includes(typeof this.constructor) && this.constructor.modelName && this.constructor.modelName === modelName) {
      model = this.constructor;
  } else if (this && ['function'].includes(typeof this) && this.modelName && this.modelName === modelName) {
      model = this;
  } else if (this && ['object'].includes(typeof this) && this.model && ['function'].includes(typeof this.model) && this.model.modelName && this.model.modelName === modelName) {
      model = this.model;
  }

  if (!model) throw new Error('cannot-reach-model-reference');

  const connection = model.$parent;

  if (!connection) throw new Error('cannot-reach-connection-reference');

  const models = connection.models;

  if (!models) throw new Error('cannot-reach-models-reference');

  return models;
};

const sendDiscord = (channel, content) => {
  let uri;

  switch (channel) {
    case 'alerta-pagamentos':
      uri = 'https://discord.com/api/webhooks/1354196328850133405/HJKNRMRnCirpUu7JTcv1oGOkDJkq5KwFOPXFsda8_3V7BzUx0C4asYBDRpM6ADf5Qbhb';
      break;
    case 'alerta-trigger-documentos':
      uri = 'https://discord.com/api/webhooks/1382459913866969231/ucZquz8UizsO868q4RX8bACwTsC6EpIDqMzxLPUElAag7RC5Wsv2h-73qExuV0ukZqFn';
      break;
    case 'alerta-propagate-fileds':
      uri = 'https://discord.com/api/webhooks/1382695769772920833/XNqaHVCYtzodm9_iuKVLN-w3eY7wA_igvguN2-eEteW9x8rebjrE8Z2DBO2s7blThuT9';
    break;
    case 'alerta-required-documents-by-genre':
      uri = 'https://discord.com/api/webhooks/1382696262033473656/w_KIKXC7wL0PW1f91XD7MYvZ2J57V3IL_10ES8Iro8dNB4fPFQ4marf1fgTlKyi2Obua';
    break;
    case 'alerta-piaget-models-triggers':
      uri = 'https://discord.com/api/webhooks/1382707793534128239/v03O4vLN3IvIsR1Fs2AYH_TCjM8t8Vf-DuJfvMej7Wv8BQdbjfl5r2B6h9M4aM04_7Fu'
    break;
  }

  request({
    method: 'POST',
    json: true,
    uri,
    body: {
      username: 'Promineiro',
      avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
      content,
    }
  });
}

module.exports = { _getModels, sendDiscord };
