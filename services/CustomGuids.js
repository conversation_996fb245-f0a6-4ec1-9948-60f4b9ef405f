class CustomGuids {
    constructor() {
    }

    // mode = 'alphaNum', 'onlyNumbers', 'onlyLetters'
    makeid(length, mode = 'alphaNum') {
        let result = '';
        let characters = '';

        switch (mode) {
            case 'onlyNumbers':
                characters = '0123456789';
                break;
            case 'onlyLetters':
                characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                break;
            default:
                characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                break;
        }

        const charactersLength = characters.length;

        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }

        return result;
    }

    hexGuid() {
        return this._makeGuid(16);
    }

    decimalGuid() {
        return this._makeGuid(10, 0x199999);
    }

    _makeGuid(base, calculate = 0x19999) {
        // Actual moment date
        const dateNow = new Date();

        // Create parts off guid
        const part = () => {
            return Math
                .floor((1 + Math.random() + (dateNow.getTime() * 0x19999).toString()) * calculate)
                .toString(base)
                .substring(1);
        };

        // Return
        return part() + part() + dateNow.getFullYear()
            .toString()
            .substr(1, 3);

    }
}

module.exports = new CustomGuids();