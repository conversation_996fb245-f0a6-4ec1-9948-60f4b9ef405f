# Carrega nvm se necessário
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Verifica se nvm está disponível após carregamento
if ! command -v nvm >/dev/null 2>&1; then
  echo "❌ Erro: nvm é necessário mas não está instalado."
  echo "Por favor, instale o nvm primeiro: https://github.com/nvm-sh/nvm"
  exit 1
fi

# Verifica se Node.js 20 está instalado no nvm
if ! nvm list | grep -q "v20"; then
  echo "Node.js 20 não encontrado. Instalando..."
  nvm install 20
fi

# Usa Node.js 20
nvm use 20

npx --no-install commitlint --edit
