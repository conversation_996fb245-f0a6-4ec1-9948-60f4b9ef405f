# Garante que a branch atual está atualizada com a master
git fetch origin master
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if ! git log --oneline --graph master..$CURRENT_BRANCH > /dev/null; then
  echo "Sua branch está desatualizada em relação à master.  <PERSON><PERSON> commits na master que não estão na sua branch."
  exit 1  # Falha o comando
fi

# Executa eslint nos arquivos alterados
CHANGED_JS_FILES=$(git diff --name-only master..$CURRENT_BRANCH | grep -E '\.(js|jsx|ts|tsx)$' | tr '\n' ' ')
if [ -n "$CHANGED_JS_FILES" ]; then
  echo "Executando eslint nos arquivos alterados: $CHANGED_JS_FILES"
  npx eslint $CHANGED_JS_FILES --fix
else
  echo "Nenhum arquivo JavaScript alterado para análise do eslint."
fi

# Obtém o nome do repositório automaticamente
REPO_NAME=$(basename $(pwd))
echo "Nome do repositório: $REPO_NAME"

# Obtém a lista de arquivos alterados em relação à branch master
CHANGED_FILES=$(git diff --name-only master..$CURRENT_BRANCH | tr '\n' ',' | sed 's/,$//')
echo "Arquivos alterados em relação à master: $CHANGED_FILES"

# Executa análise do SonarQube apenas nos arquivos alterados
echo "Executando análise do SonarQube apenas nos arquivos alterados..."
if [ -n "$CHANGED_FILES" ]; then
  npx sonarqube-scanner -Dsonar.inclusions="$CHANGED_FILES"
else
  echo "Nenhum arquivo alterado para análise."
  npx sonarqube-scanner
fi

# Configura variáveis de ambiente para o SonarQube
export SONAR_TOKEN="sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05"
export SONAR_HOST_URL="https://sonar.institutoprominas.com.br"

# Verifica o status do Quality Gate usando o novo script
echo "Verificando o status do Quality Gate..."
METADATA_FILE=".scannerwork/report-task.txt"
POLLING_TIMEOUT=300  # 5 minutos de timeout

# Executa o script de verificação do Quality Gate
"$(dirname "$0")/../bash_scripts/sonar-quality-check.sh" "$METADATA_FILE" "$POLLING_TIMEOUT"
