{"name": "piaget_models", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install", "backup-precommit": "cp .husky/pre-commit .husky/pre-commit.bak", "restore-precommit": "cp .husky/pre-commit.bak .husky/pre-commit && rm .husky/pre-commit.bak", "postinstall": "npm run backup-precommit && npx husky init && npm run restore-precommit", "sonar": "npx sonarqube-scanner -Dsonar.projectKey=$(basename $(pwd)) -Dsonar.token=sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05", "sonar:changed": "CHANGED_FILES=$(git diff --cached --name-only | tr '\\n' ',' | sed 's/,$//' ) && echo \"Analyzing files: $CHANGED_FILES\" && npx sonarqube-scanner -Dsonar.projectKey=$(basename $(pwd)) -Dsonar.token=sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05 -Dsonar.inclusions=\"$CHANGED_FILES\"", "sonar:check": "curl -s -u \"sqa_a8e606f6ea58bc9aafc226fad5dbba4b78ee1f05:\" \"https://sonar.institutoprominas.com.br/api/qualitygates/project_status?projectKey=$(basename $(pwd))\" | grep -o '\\\"status\\\":\\\"[^\\\"]*\\\"' | cut -d'\\\"' -f4"}, "repository": {"type": "git", "url": "git+https://github.com/lytex-dev/piaget_models.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/lytex-dev/piaget_models/issues"}, "homepage": "https://github.com/lytex-dev/piaget_models#readme", "dependencies": {"bcryptjs": "^2.4.3", "express-http-context": "^1.2.4", "husky": "^9.1.4", "lodash": "^4.17.21", "mathjs": "^13.0.3", "moment": "^2.30.1", "mongoose": "^8.7.0", "mongoose-beautiful-unique-validation": "^7.1.1", "request": "^2.88.2", "request-promise": "^4.2.6", "slugify": "^1.6.6"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.22.0", "@types/mongoose": "^5.11.96", "eslint": "^9.29.0", "globals": "^16.0.0", "husky": "^7.0.4"}, "husky": {"skipCI": true, "hooks": {"pre-commit": "npx lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix"}}