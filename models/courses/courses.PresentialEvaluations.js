const mongoose = require("mongoose");

let PresentialEvaluations = {
  database: {
    connection: 'database_piaget',
    collection: 'PresentialEvaluations',
    fields: {
      allowedCertifiers: {
        type: [String],
        // required: false,
        default: []
      },
      allowedTypes: {
        type: [String],
        // required: false,
        default: []
      },
      allowedCourses: {
        type: [mongoose.SchemaTypes.ObjectId],
        // required: false,
        default: []
      },
      allowedClasses: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
      },
      allowedGroupings: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
      },
      allowedDisciplines: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
      },
      allowedActivities: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
      },
      startDate: {
        type: Date,
        required: true,
      },
      endDate: {
        type: Date,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
      isActive: {
        type: <PERSON>olean,
        // required: false,
        default: true
      },
      questions      : [
        {
          _id         : false,
          _questionId : {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          alternatives: {
            type    : [
              {
                number: {
                  type: Number,
                  required: true
                },
                _alternativeId: {
                  type: mongoose.SchemaTypes.ObjectId,
                  required: true
                }
              }
            ],
            required: true
          },
          _correct    : {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          }
        }
      ],
    },
  }
};

module.exports = PresentialEvaluations;
