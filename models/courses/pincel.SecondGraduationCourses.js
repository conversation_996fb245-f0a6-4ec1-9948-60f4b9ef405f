let PincelSecondGraduationCourses = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PincelSecondGraduationCourses',
        fields    : {
            name       : {
                type     : String,
                uppercase: true,
                required : true
            },
            acronym    : {
                type    : String,
                required: true,
                unique  : true
            },
            certifier  : {
                type    : String,
                required: true
            },
            level      : String,
            duration   : String,
            workload   : {
                type    : Number,
                required: true
            },
            disciplines: [
                {
                    _id     : false,
                    module  : {
                        type    : Number,
                        required: true
                    },
                    name    : {
                        type     : String,
                        uppercase: true,
                        required : true
                    },
                    workload: {
                        type    : Number,
                        required: true
                    }
                }
            ],
            paymentPlan: {
                boleto:  [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                creditCard:  [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                debitCard:  [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ]
            }
        },
        indexes   : [
            {
                fields: {
                    name: 'text'
                }
            }
        ]
    }
};

module.exports = PincelSecondGraduationCourses;
