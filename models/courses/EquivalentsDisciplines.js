const mongoose = require('mongoose');

let EquivalentsDisciplines = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'EquivalentsDisciplines',
        fields    : {
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            discId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                default: null
            },
            _name: {
                type: String,
                required: true
            },
            equivalents: {
                discId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false,
                    default: null
                },
                _name: {
                    type: String,
                    required: true
                },
                course: {
                    _id: {type: mongoose.SchemaTypes.ObjectId},
                    course: String
                },
                workload: Number,
                curriculumMatrixOriginal: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false,
                        default: null
                    },
                    name: String,
                    code: String
                }
            },
            curriculumMatrix: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false,
                    default: null
                },
                _idDiscMatrix: mongoose.SchemaTypes.ObjectId,
                name: String,
                code: String
            }
        }
    }
};

module.exports = EquivalentsDisciplines;