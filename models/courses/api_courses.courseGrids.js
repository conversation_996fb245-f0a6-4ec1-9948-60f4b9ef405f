const mongoose = require('mongoose');

let CourseGrids = {
    database: {
        collection: 'CourseGrids',
        connection: 'database_piaget',
        fields    : {
            _courseAlias      : {
                type    : String,
                required: true,
                index   : true
            },
            _courseTypeAlias  : {
                type    : String,
                required: true,
                index   : true
            },
            _certifierAlias   : {
                type    : String,
                required: true,
                index   : true
            },
            _learningAreaAlias: {
                type    : String,
                required: true,
                index   : true
            },
            _areasAlias       : {
                type    : [String],
                required: true,
                index   : true
            },
            _categoryAlias    : {
                type    : String,
                required: true,
                index   : true
            },
            minPeriod         : {
                type    : Number,
                required: true
            },
            scoreBase         : {
                type    : Number,
                required: true,
                enum    : [
                    10,
                    100
                ]
            },
            linkEMec          : {
                type     : String,
                required : true,
                lowercase: true
            },
            approvalPercentage: {
                type    : Number,
                required: true
            },
            minimalFrequency  : {
                type    : Number,
                required: true
            },
            description       : {
                type    : String,
                required: true
            },
            periodType        : {
                type    : String,
                enum    : [
                    'monthly',
                    'bimonthly',
                    'quarterly',
                    'semiannual',
                    'annual'
                ],
                default : 'semiannual'
            },
            _pillars          : {
                type    : [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            hasTCC            : {
                type    : Boolean,
                required: true
            },
            active            : {
                type    : Boolean,
                default : false
            },
            inCreation        : {
                type    : Boolean,
                default : true
            }
        },
        indexes   : [
            {
                fields : {
                    _courseAlias      : 1,
                    _courseTypeAlias  : 1,
                    _certifierAlias   : 1,
                    _learningAreaAlias: 1,
                    _categoryAlias    : 1,
                    periodType        : 1,
                    hasTCC            : 1
                },
                options: {
                    unique: 'Já existe uma grade com estas configurações',
                    name  : 'cclca'
                }
            }
        ]
    }
};

module.exports = CourseGrids;
