const {_getModels} = require("../../services/Utils");
let CourseAreas = {
    functions: {
        createTag: function(doc) {
            const { name: tag } = doc;
            const models = _getModels.call(this, 'Enrolments');
            if (!models) return ;
            const Tags = {models};
            if (!Tags) return ;

            Tags.create({
                content: tag,
                group: 'Áreas',
            }).catch(err => console.error(err));
        },
    },
    database: {
        connection: 'database_piaget',
        collection: 'CourseAreas',
        fields    : {
            name     : {
                type     : String,
                required : true,
                maxlength: 255,
                unique   : 'course_area_already_exists'
            },
            icon     : {
                type     : String,
                maxlength: 255,
                lowercase: true
            },
            isEnabled: {
                type    : Boolean,
                default : false
            },
            description: {
                type: String,
            },
            image: {
                type: String,
            },
            miniature: {
                type: String,
            },
            arguments: [
                {
                    _id: false,
                    image: {
                        type: String,
                    },
                    description: {
                        type: String,
                    }
                }
            ]
        },
        indexes   : [
            {
                fields : {
                    name: 'text'
                },
                options: {}
            }
        ]
    }
};

CourseAreas.database.post = {
    save: [
        CourseAreas.functions.createTag,
    ],
};

module.exports = CourseAreas;
