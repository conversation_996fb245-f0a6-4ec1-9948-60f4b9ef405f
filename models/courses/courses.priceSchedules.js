const mongoose = require("mongoose");
const {coursePaymentPartnerSchema} = require("./Courses");
const moment = require("moment");
const {SchemaTypes} = require("mongoose");
const modelName = 'PriceSchedules';

const newPriceSchema = {
    paymentPartner: coursePaymentPartnerSchema
};

let PriceSchedule = {
    functions: {
        parseCourseId: function (next) {
            try {
                if (this.courses && Array.isArray(this.courses) && this.courses.length) {
                    this.courses = this.courses.map(id => new mongoose.Types.ObjectId(id));
                } else if (this._coursesId && !Array.isArray(this.courses)) {
                    this.courses = new mongoose.Types.ObjectId(this.courses);
                } else {
                    this.courses = []
                }

                if (this.exceptionCourses && Array.isArray(this.exceptionCourses) && this.exceptionCourses.length) {
                    this.exceptionCourses = this.exceptionCourses.map(id => new mongoose.Types.ObjectId(id));
                } else if (this.exceptionCourses && !Array.isArray(this.exceptionCourses)) {
                    this.exceptionCourses = new mongoose.Types.ObjectId(this.exceptionCourses);
                } else {
                    this.exceptionCourses = []
                }

                if (this.weekDays && typeof this.weekDays === "string")
                    this.weekDays = JSON.parse(this.weekDays);

                if(Array.isArray(this.weekDays) && Array.isArray(this.weekDays[0]))
                    this.weekDays = this.weekDays[0];

                next();
            } catch (e) {
                console.error(e);
                next(e);
            }
        }
    },
    database: {
        collection: modelName,
        fields: {
            name: {
                type: String,
                required: false,
            },
            description: {
                type: String,
                required: false,
            },
            status: {
                type: String,
                required: false,
                default: function () {
                    const startDate = moment(this.startDate);
                    const endDate = moment(this.endDate);
                    const now = moment();

                    return now.isBetween(startDate, endDate) ? 'waitingWeekStartDay' : 'waitingStartDate';
                },
            },
            startDate: {
                type: Date,
                required: true,
            },
            endDate: {
                type: Date,
                required: true,
            },
            startHour: {
                type: Number,
                required: true,
            },
            endHour: {
                type: Number,
                required: true,
            },
            certifier: {
                type: String,
                required: true,
            },
            weekDays: {
                type: SchemaTypes.Mixed,
                required: true,
            },
            courseType: {
                type: String,
                required: true,
            },
            updateMode: {
                type: String,
                enum: ['enabled', 'disabled', 'all'],
                required: true,
            },
            workload: {
                type: Number,
                required: false,
            },
            priceFilter: {
                type: Number,
                required: false,
            },
            courses: {
                type: mongoose.SchemaTypes.Mixed
            },
            exceptionCourses: {
                type: mongoose.SchemaTypes.Mixed
            },
            newPrice: {
                type: newPriceSchema,
                required: true,
            },
            rollbackToOriginal: {
                type: Boolean,
                required: true,
            },
            rollbackPrice: {
                type: newPriceSchema,
                required: function () {
                    return !this.rollbackToOriginal
                },
            },
            _userId: {type: mongoose.SchemaTypes.ObjectId},
            _userType: {type: String},
            _userName: {type: String},
            isActive: {
                type: Boolean,
                required: false,
                default: true,
            }
        }
    }
};

PriceSchedule.database.pre = {
    save: [
        PriceSchedule.functions.parseCourseId
    ],
    findOneAndUpdate: [
        PriceSchedule.functions.parseCourseId
    ],
    updateOne: [
        PriceSchedule.functions.parseCourseId
    ],
    updateMany: [
        PriceSchedule.functions.parseCourseId
    ]
}

module.exports = PriceSchedule;
