const mongoose = require('mongoose');
const modelName = 'CourseTypes';
const CourseTypes = {
  functions: {
    createTag: async function (doc) {

      /*const {name: tag} = doc;

      httpContext.get('models').Tags.create({
          content: tag,
          group  : 'Tipos de curso'
      }).catch(null);*/

      return doc;
    },
    setNumber: async function (r) {
      let model = null;
      if (this.constructor && ['function'].includes(typeof this.constructor) && this.constructor.modelName && this.constructor.modelName === modelName) {
        model = this.constructor;
      } else if (this && ['function'].includes(typeof this) && this.modelName && this.modelName === modelName) {
        model = this;
      } else if (this && ['object'].includes(typeof this) && this.model && ['function'].includes(typeof this.model) && this.model.modelName && this.model.modelName === modelName) {
        model = this.model;
      }
      if (model) {
        let find;
        let arrObj;
        if (this && ['function', 'object'].includes(typeof this) && 'getQuery' in this && this.getQuery()) {
          find = this.getQuery();
          arrObj = await model.find(find);
        } else {
          arrObj = Array.isArray(r) ? r : [r];
        }
        if (arrObj && Array.isArray(arrObj) && arrObj.length) {
          for (let i = 0; i < arrObj.length; i++) {
            if (arrObj[i] && typeof arrObj[i] === 'object' && arrObj[i]._id) {
              if (!arrObj[i].number) {
                let number = 1;
                const lastObj = await model.findOne({number: {$ne: null}}).sort({number: -1});
                if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                  if ('number' in lastObj && !isNaN(lastObj.number)) {
                    number = parseInt(lastObj.number.toString()) + 1;
                  }
                }
                while (true) {
                  arrObj[i].number = number;
                  const newObj = await arrObj[i].save({validateBeforeSave: false}).then(c => c).catch(() => null);
                  if (newObj && typeof newObj === 'object' && newObj._id && newObj.number) {
                    break;
                  } else {
                    number++;
                  }
                }
              }
            }
          }
        }
      }
      return r;
    },
  },
  database: {
    collection: modelName,
    connection: 'database_piaget',
    fields: {
      name: {
        type: String,
        required: true,
        maxlength: 150,
        unique: 'course_type_already_exists',
      },
      number: {
        type: Number,
        // required: false,
        allowNull: true,
        default: null,
      },
      alias: {
        type: String,
        required: true,
      },
      workloadMin: {
        type: Number,
        // required: false,
        default: 0,
      },
      workloadMax: {
        type: Number,
        // required: false,
        default: 0,
      },
      blockEvaluation: {
        type: Boolean,
        // required: false,
        default: false,
      },
      blockDismissal: {
        type: Boolean,
        // required: false,
        default: false,
      },
      joinSelectionProcess: {
        type: Boolean,
        // required: false,
        default: false,
      },
      needClass: {
        type: Boolean,
        // required: false,
        default: false,
      },
      allowPolo: {
        type: Boolean,
        // required: false,
        default: false,
      },
      enableCertificateCredits: {
        type: Boolean,
        // required: false,
        default: false,
      },
      certificateFee: {
        type: {
          digital: {
            type: Number,
            required: true,
          },
          printed: {
            type: Number,
            required: true,
          },
        },
        // required: false
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false
      },
      blockEnrolment: {
        type: {
          blockType: {
            type: String,
            enum: [
              'disabled', // Desabilitado
              'days', // Dias
              'months', // Meses
              'years', // Anos
            ],
            // required: false,
            default: 'disabled',
            allowNull: true,
          },
          unity: {
            type: Number,
            // required: false,
            default: null,
            allowNull: true,
          },
          verifyAfter: {
            type: Date,
            // required: false,
            default: null,
            allowNull: true,
          },
        },
        // required: false,
        default: null,
        allowNull: true,
      },
      isEnabled: {
        type: Boolean,
        // required: false,
        default: false,
      },
      searchesWord: {
        type: [String],
      },
      conclusionDocumentType: {
        type: String,
      },
      pillars: {
        type: [String],
        //// required: false,
        default: [],
      },
      requiredSchoolEducations: {
        type: [String],
        //// required: false,
        default: [],
      },
      dependecyConfig: {
        type: {
          useDependency: Boolean,
          quantityOnlineEvaluations: Number,
          maxGradeOnlineEvaluation: Number,
          quantityRecuperation: Number,
          maxGradeRecuperationEvaluation: Number,
          maxTime: Number,
          dependencyRateAmount: Number,
        },
      },
      useFinancialResponsible: {
        type: Boolean,
        default: false,
      },
      discountByDispensationSolicitations: {
        type: Boolean,
        default: false,
      },
      hasInternship: {
        type: String,
        default: false,
      },
      hasAdditionalWorkload: {
        type: Boolean,
        default: false,
      },
      hasOptionalDisciplines: {
        type: Boolean,
        default: false,
      },
      chatEnabled: {
        type: Boolean,
        default: false,
      },
    },
    indexes: [
      {
        fields: {
          number: 1,
        },
        options: {
          unique: 'number_already_exists',
          name: 'number_1',
          partialFilterExpression: {
            number: {
              $type: 'number',
            },
          },
        },
      },
    ],
  },
};
CourseTypes.database.post = {
  save: [
    CourseTypes.functions.setNumber,
  ],
  insertMany: [
    CourseTypes.functions.setNumber,
  ],
  findOneAndUpdate: [
    CourseTypes.functions.setNumber,
  ],
  findOneAndReplace: [
    CourseTypes.functions.setNumber,
  ],
  updateOne: [
    CourseTypes.functions.setNumber,
  ],
  updateMany: [
    CourseTypes.functions.setNumber,
  ],
};
module.exports = CourseTypes;
