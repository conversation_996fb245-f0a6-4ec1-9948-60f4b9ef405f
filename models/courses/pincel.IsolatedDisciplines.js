let PincelIsolatedDisciplines = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PincelIsolatedDisciplines',
        fields    : {
            name       : {
                type     : String,
                uppercase: true,
                required : true
            },
            acronym    : {
                type    : String,
                required: true,
                unique  : true
            },
            duration   : String,
            certifier  : {
                type    : String,
                required: true
            },
            workload   : {
                type    : Number,
                required: true
            },
            paymentPlan: [
                {
                    _id        : false,
                    installment: {
                        type    : Number,
                        required: true
                    },
                    value      : {
                        type    : Number,
                        required: true
                    }
                }
            ]
        },
        indexes   : [
            {
                fields: {
                    name: 'text'
                }
            }
        ]
    }
};

module.exports = PincelIsolatedDisciplines;
