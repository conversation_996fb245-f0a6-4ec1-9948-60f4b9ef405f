const { SchemaTypes } = require('mongoose');

let ContentReports = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'ContentReports',
    fields: {
      _enrolmentId: {
        type: SchemaTypes.ObjectId,
        required: true,
      },
      _disciplineId: {
        type: SchemaTypes.ObjectId,
        required: true,
      },
      disciplineName: {
        type: String,
        required: true
      },
      certifierName: {
        type: String,
        required: true
      },
      courseTypeName: {
        type: String,
        required: true
      },
      courseName: {
        type: String,
        required: true
      },
      studentName: {
        type: String,
        required: true
      },
      cpf: {
        type: String,
        required: true
      },
      type: {
        type: String,
        enum: ['courseware', 'extraContent', 'directedStudy', 'forum', 'videoLessons', 'pathOfKnowledge'],
        required: true
      },
      description: {
        type: String,
        required: true
      },
      status: {
        type: String,
        enum: [
          'pending',
          'open',
          'closed',
        ],
        default: 'pending',
      },
      answer: {
        type: String,
        required: function () {
          return this.status === 'closed';
        }
      },
      history: [{
        _userId: {
          type: SchemaTypes.ObjectId,
          required: true
        },
        _userName: {
          type: String,
          required: true
        },
        _userType: {
          type: String,
          enum: ['employer'],
          required: true
        },
        launchedAt: {
          type: Date,
          required: true,
          default: new Date()
        },
        status: {
          type: String,
          required: true
        },
        answer: {
          type: String
        },
      }],
      observations: [{
        observation: {
          type: String,
          required: true,
        },
        _userId: {
          type: SchemaTypes.ObjectId,
          required: true,
        },
        _userName: {
          type: String,
          required: true,
        },
        createdAt: {
          type: Date,
          required: true,
          default: new Date(),
        },
      }],
      contentReported: SchemaTypes.Mixed,
      employer: {
        type: {
          _id: {
            type: SchemaTypes.ObjectId,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
          _departmentAlias: {
            type: String,
            required: true,
          },
        },
      }
    },
    indexes: [
      {
        fields: {
          _enrolmentId: 1,
          _disciplineId: 1,
          _coursewareId: 1,
          type: 1
        },
        options: {
          unique: 'report_already_exists',
          name: 'report_unique',
        }
      }
    ]
  }
};

module.exports = ContentReports;
