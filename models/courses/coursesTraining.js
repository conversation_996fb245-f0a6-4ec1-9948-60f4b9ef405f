const mongoose = require('mongoose');

let CoursesTraining = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'CoursesTraining',
        fields    : {
            _name               : {
                type     : String,
                uppercase: true,
                required : true
            },
            certifier   :  {
                type    : [String],
                // required: false,
                default : []
            },
             _categoryId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
             },
            streaming:[
                    {
                        title: {
                            type: String,
                            required: true
                        },
                        link: {
                            type: String,
                            required: true
                        },
                        code: {
                            type: Number,
                            required: true
                        },
                        cover: {//capa
                            type: String,
                            required: true
                        },
                        linkCourseware: {
                            type: String,
                            // required: false
                        },
                        isSpotlight: {
                          type: String,
                          required: true,
                          enum: [
                            'Sim',
                            'Não',
                          ]
                        },
                        createdAt :{
                            type: Date,
                            required: true
                        },
                        displayOn: {
                            type: [String],
                            // required: false,
                            default: ['PARTNER']
                        },
                    }
            ],
            isEnabled           : {
                type    : Boolean,
                // required: false,
                default : false
            }
        },
        indexes   : [
            {
                fields : {
                    _name         : 1
                },
                options: {
                    unique: 'course_already_exists',
                    name  : 'dup_course'
                }
            },
            {
                fields : {
                    _name: 'text'
                },
                options: {}
            }
        ]
    }
};

module.exports = CoursesTraining;
