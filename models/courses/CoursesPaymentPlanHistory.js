const mongoose = require('mongoose');

let CoursesPaymentPlanHistory = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'CoursesPaymentPlanHistory',
        fields    : {
            author     : {
                _userid    : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _username  : {
                    type    : String,
                    required: true
                },
                _usertype  : {
                    type    : String,
                    required: true
                },
                _department: {
                    type    : String,
                    required: true
                }
            },
            certifiers : {
                type    : [String],
                // required: false,
                default : null
            },
            types      : {
                type    : [String],
                // required: false,
                default : null
            },
            areas      : {
                type    : [String],
                // required: false,
                default : null
            },
            workload   : {
                type    : Number,
                // required: false,
                default : null
            },
            search     : {
                type    : String,
                // required: false,
                default : null
            },
            paymentPlan: {
                creditCard: [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                debitCard : [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                boleto    : [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ]
            },
            paymentPartner: {
                paidInCash: {
                    type: Number,
                    required: true
                },
                paidInInstalment: {
                    type: Number,
                    required: true
                }
            }
        }
    }
};

module.exports = CoursesPaymentPlanHistory;