const mongoose = require('mongoose');
const modelName = 'CourseTypesBKPAllowPolo';

// fixme collections temporarias
let CourseTypes = {
    functions: {
        createTag: async function(doc) {

            /*const {name: tag} = doc;

            httpContext.get('models').Tags.create({
                content: tag,
                group  : 'Tipos de curso'
            }).catch(null);*/

            return doc;
        },
        setNumber: async function(r) {
            let model = null;
            if (this.constructor && ['function'].includes(typeof this.constructor) && this.constructor.modelName && this.constructor.modelName === modelName) {
                model = this.constructor;
            } else if (this && ['function'].includes(typeof this) && this.modelName && this.modelName === modelName) {
                model = this;
            } else if (this && ['object'].includes(typeof this) && this.model && ['function'].includes(typeof this.model) && this.model.modelName && this.model.modelName === modelName) {
                model = this.model;
            }
            if (model) {
                let find;
                let arrObj;
                if (this && ['function', 'object'].includes(typeof this) && 'getQuery' in this && this.getQuery()) {
                    find = this.getQuery();
                    arrObj = await model.find(find);
                } else {
                    arrObj = Array.isArray(r) ? r : [r];
                }
                if (arrObj && Array.isArray(arrObj) && arrObj.length) {
                    for (let i = 0; i < arrObj.length; i++) {
                        if (arrObj[i] && typeof arrObj[i] === 'object' && arrObj[i]._id) {
                            if (!arrObj[i].number) {
                                let number = 1;
                                const lastObj = await model.findOne({number: {$ne: null}}).sort({number: -1});
                                if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                                    if ('number' in lastObj && !isNaN(lastObj.number)) {
                                        number = parseInt(lastObj.number.toString()) + 1;
                                    }
                                }
                                while (1) {
                                    arrObj[i].number = number;
                                    const newObj = await arrObj[i].save({validateBeforeSave: false}).then(c => c).catch(() => null);
                                    if (newObj && typeof newObj === 'object' && newObj._id && newObj.number) {
                                        break;
                                    } else {
                                        number++;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return r;
        }
    },
    database: {
        collection: modelName,
        connection: 'database_piaget',
        fields    : {
            name     : {
                type     : String,
                required : true,
                maxlength: 150,
                unique   : 'course_type_already_exists'
            },
            number: {
                type: Number,
                allowNull: true,
                default: null
            },
            alias    : {
                type    : String,
                required: true
            },
            workloadMin    : {
                type    : Number,
                default: 0
            },
            workloadMax    : {
                type    : Number,
                default: 0
            },
            blockEvaluation    : {
                type    : Boolean,
                default : false
            },
            blockDismissal    : {
                type    : Boolean,
                default : false
            },
            joinSelectionProcess    : {
                type    : Boolean,
                default : false
            },
            needClass    : {
                type    : Boolean,
                default : false
            },
            allowPolo: {
                type    : Boolean,
                default : false
            },
            enableCertificateCredits: {
                type    : Boolean,
                default : false
            },
            certificateFee: {
                type: {
                    digital: {
                        type: Number,
                        required: true
                    },
                    printed: {
                        type: Number,
                        required: true
                    }
                },
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
            },
            blockEnrolment: {
                type: {
                    blockType: {
                        type: String,
                        enum: [
                            'disabled', // Desabilitado
                            'days', // Dias
                            'months', // Meses
                            'years' // Anos
                        ],
                        default: 'disabled',
                        allowNull: true
                    },
                    unity: {
                        type: Number,
                        default: null,
                        allowNull: true
                    },
                    verifyAfter: {
                        type: Date,
                        default: null,
                        allowNull: true
                    }
                },
                default: null,
                allowNull: true
            },
            isEnabled: {
                type    : Boolean,
                default : false
            },
            searchesWord: {
                type: [String]
            },
            hasDegree: {
                type: [String],
            },
            pillars: {
              type: [String],
              default: []
            }
        },
        indexes: [
            {
                fields: {
                    number: 1
                },
                options: {
                    unique: 'number_already_exists',
                    name: 'number_1',
                    partialFilterExpression: {
                        number: {
                            $type: "number"
                        }
                    }
                }
            }
        ]
    }
};
CourseTypes.database.post = {
    save: [
        CourseTypes.functions.setNumber
    ],
    insertMany: [
        CourseTypes.functions.setNumber
    ],
    findOneAndUpdate: [
        CourseTypes.functions.setNumber
    ],
    findOneAndReplace: [
        CourseTypes.functions.setNumber
    ],
    updateOne: [
        CourseTypes.functions.setNumber
    ],
    updateMany: [
        CourseTypes.functions.setNumber
    ]
};
module.exports = CourseTypes;
