const mongoose = require('mongoose');
const slugify = require('slugify');
const modelName = 'CoursesBkpAllowPolo';
// fixme collections temporarias
const paymentPlanSchema = {
  creditCard: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  debitCard: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  boleto: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  cardRecurrence: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  pix: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
};
const reducedPaymentPlanSchema = {
  installment: {
    type: Number,
  },
  value: {
    type: Number,
  },
};

const Courses = {
  functions: {
    sortPaymentPlans: async function (course) {
      const sortFn = (a, b) => a.installment === b.installment ? 0 : +(a.installment > b.installment) || -1;

      course.paymentPlan.boleto = course.paymentPlan.boleto.sort(sortFn);
      course.paymentPlan.creditCard = course.paymentPlan.creditCard.sort(sortFn);
      course.paymentPlan.debitCard = course.paymentPlan.debitCard.sort(sortFn);
      course.paymentPlan.cardRecurrence = course.paymentPlan.cardRecurrence.sort(sortFn);
      course.paymentPlan.pix = course.paymentPlan.pix.sort(sortFn);

      if (!course.searchName) {
        course.searchName = (slugify(course._name).toLowerCase().replace(/-/gm, ' ')) + ' ' + course.workload;
      }

      return await course.save();
    },
    setSearchName: function (next) {
      this.searchName = slugify(this._name).toLowerCase().replace(/-/gm, ' ');
      next();
    },
    setNumber: async function (r) {
      let model = null;
      if (this.constructor && ['function'].includes(typeof this.constructor) && this.constructor.modelName && this.constructor.modelName === modelName) {
        model = this.constructor;
      } else if (this && ['function'].includes(typeof this) && this.modelName && this.modelName === modelName) {
        model = this;
      } else if (this && ['object'].includes(typeof this) && this.model && ['function'].includes(typeof this.model) && this.model.modelName && this.model.modelName === modelName) {
        model = this.model;
      }
      if (model) {
        let find;
        let arrObj;
        if (this && ['function', 'object'].includes(typeof this) && 'getQuery' in this && this.getQuery()) {
          find = this.getQuery();
          arrObj = await model.find(find);
        } else {
          arrObj = Array.isArray(r) ? r : [r];
        }
        if (arrObj && Array.isArray(arrObj) && arrObj.length) {
          for (let i = 0; i < arrObj.length; i++) {
            if (arrObj[i] && typeof arrObj[i] === 'object' && arrObj[i]._id) {
              if (!arrObj[i].number) {
                let number = 1;
                const lastObj = await model.findOne({number: {$ne: null}}).sort({number: -1});
                if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                  if ('number' in lastObj && !isNaN(lastObj.number)) {
                    number = parseInt(lastObj.number.toString()) + 1;
                  }
                }
                while (1) {
                  arrObj[i].number = number;
                  const newObj = await arrObj[i].save({validateBeforeSave: false}).then(c => c).catch(() => null);
                  if (newObj && typeof newObj === 'object' && newObj._id && newObj.number) {
                    break;
                  } else {
                    number++;
                  }
                }
              }
            }
          }
        }
      }
      return r;
    },
  },
  database: {
    collection: modelName,
    fields: {
      _name: {
        type: String,
        uppercase: true,
        required: true,
      },
      number: {
        type: Number,
        allowNull: true,
        default: null,
      },
      searchName: {
        type: String,
        lowercase: true,
      },
      _typeName: {
        type: String,
        required: true,
      },
      _certifierName: {
        type: String,
        required: true,
      },
      _knowledgeAreaName: {
        type: String,
        required: true,
      },
      _categoryName: {
        type: String,
        default: 'Lato Sensu',
      },
      _subCategory: {
        type: String,
        default: 'Premium',
      },
      _areaNames: {
        type: [String],
        required: true,
      },
      evaluationMethod: {
        type: String,
        enum: [
          'average',    // média
          'modular_sum', // soma modular
        ],
        required: true,
        default: 'average',
      },
      videoIntroduction: {
        type: String,
        default: null,
      },
      marketingVideoTeaser: {
        type: String,
        default: null,
      },
      marketingVideoBody: {
        type: String,
        default: null,
      },
      image: {
        type: String,
      },
      faculty: {
        type: String,
        default: null,
      },
      methodology: {
        type: String,
        default: null,
      },
      objective: {
        type: String,
        default: null,
      },
      program: {
        type: String,
        default: null,
      },
      completeOrdinance: {
        type: String,
        allowNull: true,
        default: null,
      },
      ordinance: {
        type: String,
        allowNull: true,
        default: null,
      },
      ordinanceDate: {
        type: Date,
        allowNull: true,
        default: null,
      },
      douDate: {
        type: Date,
        allowNull: true,
        default: null,
      },
      targetPublic: {
        type: String,
        default: null,
      },
      siteTitle: {
        type: String,
      },
      depositions: [
        {
          _id: false,
          name: {
            type: String,
          },
          photo: {
            type: String,
            // required: false
          },
          description: {
            type: String,
            // required: false
          },
        },
      ],
      photo: {
        type: String,
        // required: false
      },
      photo_miniature: {
        type: String,
        // required: false
      },
      score: {
        type: String,
        // required: false
      },
      tags: {
        type: Array,
        // required: false
      },
      internshipRequiredWorkload: {
        type: Number,
        // required: false
      },
      disciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            default: null,
          },
          period: {
            type: Number,
          },
          _name: {
            type: String,
            required: true,
          },
          sagahDiscipline: {
            type: String,
            // required: false
          },
          teacher: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          type: {
            type: String,
            enum: [
              'required',
              'optional',
            ],
            required: true,
          },
          description: {
            type: String,
            required: true,
          },
          skills: {
            type: String,
            // required: false
          },
          habilities: {
            type: String,
            // required: false
          },
          gradeSystem: {
            type: String,
            // required: false
          },
          methodology: {
            type: String,
            // required: false
          },
          teacherOrientations: {
            type: String,
            // required: false
          },
          mainThemes: {
            type: String,
            // required: false
          },
          extraThemes: {
            type: String,
            // required: false
          },
          programContent: {
            type: String,
            // required: false
          },
          objective: {
            type: String,
            // required: false,
            default: null,
          },
          program: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            // required: false
          },
          tags: {
            type: Array,
            // required: false
          },
          activities: [
            {
              type: {
                type: String,
                enum: [
                  'regular',
                  'recuperation',
                ],
                required: true,
              },
              modality: {
                type: String,
                enum: [
                  'online',
                  'presential',
                ],
                required: true,
              },
              maxDuration: {
                type: Number,
                // required: false
              },
              chapter: {
                type: [{
                  _id: false,
                  coursewareId: mongoose.SchemaTypes.ObjectId,
                  coursewareName: String,
                  number: Number,
                }],
              },
              model: {
                type: String,
                enum: [
                  'form',
                  'evaluation',
                  'upload',
                  'participation',
                  'sagah',
                  'forum',
                ],
                required: true,
              },
              isFinalTest: {
                type: Boolean,
                required: true,
              },
              modelMeta: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                },
                numQuestions: {
                  type: Number,
                  // required: false
                },
                enunciation: {
                  type: String,
                  // required: false
                },
                ltiUrl: {
                  type: String,
                  // required: false
                },
                ltiTitle: {
                  type: String,
                  // required: false
                },
              },
              maxGrade: {
                type: Number,
                required: true,
                default: 10,
              },
            },
          ],
          chapter: {
            type: [{
              _id: false,
              coursewareId: mongoose.SchemaTypes.ObjectId,
              coursewareName: String,
              number: Number,
            }],
          },
          _forums: {
            type: [
              {
                forumId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          directedStudies: {
            type: [
              {
                directedStudyId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          coursewareType: {
            type: String,
          },
          _coursewares: {
            type: [mongoose.SchemaTypes.ObjectId],
            // required: false
          },
          supportFile: {
            type: String,
            // required: false
          },
        },
      ],
      predefinedGrids: [
        {
          name: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            required: true,
          },
          _disciplines: {
            type: [mongoose.SchemaTypes.ObjectId],
            required: true,
          },
        },
      ],
      acronym: {
        type: String,
        required: true,
        unique: 'Sigla já existe',
      },
      workload: {
        type: Number,
        // required: false
      },
      minMonthsToComplete: {
        type: Number,
        // required: false
      },
      maxMonthsToComplete: {
        type: Number,
        // required: false
      },
      linkEMec: {
        type: String,
        required: true,
      },
      paymentPlan: paymentPlanSchema,
      onEnrolmentPaymentPlan: reducedPaymentPlanSchema,
      paymentPartner: {
        type: {
          paidInCash: {
            type: Number,
            required: true,
          },
          paidInInstalment: {
            type: Number,
          },
          commissionMedium: {
            type: Number,
          },
        },
        // required: false,
        allowNull: true,
      },
      approvalPercentage: {
        type: Number,
        required: true,
      },
      minimalFrequency: {
        type: Number,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      isTccRequired: {
        type: Boolean,
        required: true,
      },
      isInternshipRequired: {
        type: Boolean,
        required: true,
      },
      isEnabled: {
        type: Boolean,
        // required: false,
        default: false,
      },

      technologicalAxis: {
        type: String,
        // required: false
      },
      ownership: {
        type: String,
        // required: false
      },
      hoursComplementaryActivity: {
        type: Number,
        // required: false
      },
      hoursComplementaryActivityRequired: {
        type: Number,
        // required: false
      },
      isRequiredHoursComplementaryActivity: {
        type: Boolean,
        // required: false
      },
      mandatoryDocumentsType: {
        type: [
          {
            typeId: {
              // Id do documento
              type: mongoose.SchemaTypes.ObjectId,
              // required: false
            },
            typeName: {
              // Nome do documento
              type: String,
              // required: false
            },

          },
        ],
        // required: false,
        default: undefined,
      },
      responsible: {
        type: String,
        // required: false
      },
      functionName: {
        type: String,
        // required: false
      },
      schoolSecretary: {
        type: String,
        // required: false
      },
      authorizationSecretary: {
        type: String,
        // required: false
      },
      mecCode: {
        type: Number,
        // required: false
      },
      resolution: {
        type: String,
        // required: false
      },
      amountPeriodicity: {
        type: Number,
        // required: false
      },
      periodicity: {
        type: String,
        // required: false
      },
      reEnrolment: {
        type: {
          typeBreak: {
            type: String,
            // required: false
          },
          typeValidity: {
            type: String,
            // required: false
          },
          validityReEnrolment: {
            type: Number,
            // required: false
          },
          acceptanceTerm: {
            type: Boolean,
            // required: false
          },
          generateBilling: {
            type: Boolean,
            // required: false
          },
          blockDefault: {
            type: Boolean,
            // required: false
          },
          havePaymentPlan: {
            type: Boolean,
            // required: false
          },
          paymentPlan: reducedPaymentPlanSchema,
          termReEnrolment: {
            type: String,
            // required: false
          },
          applyMonetaryCorrection: {
            type: Boolean,
            default: false,
          },
        },
      },
      releaseDisciplines: {
        type: {
          releaseType: String,
          isAutomatic: Boolean,
        },
      },
      certificateCredits: {
        type: Number,
        required: true,
        default: 0,
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false
      },
      codeContedtech: {
        type: Number,
        // required: false
      },
      partnersComissions: {
        // required: false,
        type: [
          {
            _id: false,
            name: {
              type: String,
              required: true,
            },
            cpf: {
              type: String,
              required: true,
            },
            commissionMonthly: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionEnrolment: {
              type: Number,
              // required: false,
              default: 0,
            },
          },
        ],
      },
      actuationArea: {
        type: String,
        // required: false
      },
      campaignImage: {
        type: String,
        // required: false
      },
      optionalDisciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId,
            default: null,
          },
          period: {
            type: Number,
          },
          _name: {
            type: String,
            required: true,
          },
          sagahDiscipline: {
            type: String,
          },
          teacher: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
          },
          type: {
            type: String,
            enum: [
              'required',
              'optional',
            ],
            required: true,
          },
          description: {
            type: String,
            required: true,
          },
          skills: {
            type: String,
          },
          habilities: {
            type: String,
          },
          gradeSystem: {
            type: String,
          },
          methodology: {
            type: String,
          },
          teacherOrientations: {
            type: String,
          },
          mainThemes: {
            type: String,
          },
          extraThemes: {
            type: String,
          },
          programContent: {
            type: String,
            // required: false
          },
          objective: {
            type: String,
            default: null,
          },
          program: {
            type: String,
            default: null,
          },
          workload: {
            type: Number,
            required: true,
          },
          tags: {
            type: Array,
          },
          activities: [
            {
              type: {
                type: String,
                enum: [
                  'regular',
                  'recuperation',
                ],
                required: true,
              },
              modality: {
                type: String,
                enum: [
                  'online',
                  'presential',
                ],
                required: true,
              },
              chapter: {
                type: [{
                  _id: false,
                  coursewareId: mongoose.SchemaTypes.ObjectId,
                  coursewareName: String,
                  number: Number,
                }],
              },
              maxDuration: {
                type: Number,
              },
              model: {
                type: String,
                enum: [
                  'form',
                  'evaluation',
                  'upload',
                  'participation',
                  'sagah',
                  'forum',
                ],
                required: true,
              },
              isFinalTest: {
                type: Boolean,
                required: true,
              },
              modelMeta: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                },
                numQuestions: {
                  type: Number,
                },
                enunciation: {
                  type: String,
                },
                ltiUrl: {
                  type: String,
                },
                ltiTitle: {
                  type: String,
                },
              },
              maxGrade: {
                type: Number,
                required: true,
                default: 10,
              },
            },
          ],
          chapter: {
            type: [{
              _id: false,
              coursewareId: mongoose.SchemaTypes.ObjectId,
              coursewareName: String,
              number: Number,
            }],
          },
          _forums: {
            type: [
              {
                forumId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            default: undefined,
          },
          directedStudies: {
            type: [
              {
                directedStudyId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            default: undefined,
          },
          coursewareType: {
            type: String,
          },
          _coursewares: {
            type: [mongoose.SchemaTypes.ObjectId],
          },
          supportFile: {
            type: String,
          },
        },
      ],
      curriculumMatrix: {
        _id: mongoose.SchemaTypes.ObjectId,
        code: String,
        hoursComplementaryActivity: Number,
        internshipWorkload: Number,
        quantityOptionalDisciplines: Number,
        workloadOptionalDisciplines: Number,
        internships: [
          {
            schedulingSent: {
              type: Boolean,
            },
            shipmentType: {
              type: String,
            },
            shipmentTypeQuantity: {
              type: mongoose.SchemaTypes.Mixed,
            },
            shipmentQuantity: {
              type: Number,
            },
            workload: {
              type: Number,
            },
            helpFile: {
              type: String,
            },
            fileModelsInternshipRequired: {
              type: [{
                url: String,
                name: String,
                typeDocument: String,
              }],
            },
            stepsInternshipRequired: {
              type: [{
                isRequired: Boolean,
                typeInternship: String,
                title: String,
                action: String,
                description: String,
                nextStatus: String,
                reviewBy: String,
                visibleIn: String,
                originalFile: String,
                order: Number,
                internalAnalisy: Boolean,
                typeLimitCorrect: String,
                dateLimit: Date,
                limitCorrect: Number,
                multiFile: Boolean,
              }],
            },
            area: {
              type: [{
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  required: true,
                },
                name: {
                  type: String,
                  required: true,
                },
              }],
            },
            tutor: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  required: true,
                },
                name: {
                  type: String,
                  required: true,
                },
              },
            },
            title: String,
          },
        ],
      },
      hasCurriculumMatrix: {
        type: Boolean,
        default: false,
      },
      modality: {
        type: String,
      },
      coordinator: {
        type: {
          name: String,
          _id: String,
        },
      },
      authorization: {
        type: {
          inProcess: {
            type: Boolean,
          },
          // type: {
          //     type: String,
          //     // required: false,
          //     allowNull: true,
          //     default: null
          // },
          typeInMec: {
            type: String,
            allowNull: true,
            default: null,
          },
          number: {
            type: String,
            allowNull: true,
            default: null,
          },
          date: {
            type: Date,
            allowNull: true,
            default: null,
          },
          publisher: {
            type: String,
            allowNull: true,
            default: null,
          },
          datePublication: {
            type: Date,
            allowNull: true,
            default: null,
          },
          sessionPublication: {
            type: String,
            allowNull: true,
            default: null,
          },
          pagePublication: {
            type: String,
            allowNull: true,
            default: null,
          },
          diaryNumber: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationNumber: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationType: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationDateRegister: {
            type: Date,
            allowNull: true,
            default: null,
          },
          tramitationDateProtocol: {
            type: Date,
            allowNull: true,
            default: null,
          },
        },
      },
      recognition: {
        type: {
          inProcess: {
            type: Boolean,
          },
          // type: {
          //     type: String,
          //     // required: false,
          //     allowNull: true,
          //     default: null
          // },
          typeInMec: {
            type: String,
            allowNull: true,
            default: null,
          },
          number: {
            type: String,
            allowNull: true,
            default: null,
          },
          date: {
            type: Date,
            allowNull: true,
            default: null,
          },
          publisher: {
            type: String,
            allowNull: true,
            default: null,
          },
          datePublication: {
            type: Date,
            allowNull: true,
            default: null,
          },
          sessionPublication: {
            type: String,
            allowNull: true,
            default: null,
          },
          pagePublication: {
            type: String,
            allowNull: true,
            default: null,
          },
          diaryNumber: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationNumber: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationType: {
            type: String,
            allowNull: true,
            default: null,
          },
          tramitationDateRegister: {
            type: Date,
            allowNull: true,
            default: null,
          },
          tramitationDateProtocol: {
            type: Date,
            allowNull: true,
            default: null,
          },
        },
      },
      quantityOptionalDisciplines: {
        type: Number,
      },
      gradeInMec: {
        type: {
          course: Number,
          certifier: Number,
        },
      },
    },
    /*post: {
                    findOneAndUpdate: [sortPaymentPlans]
                },*/
    indexes: [
      {
        fields: {
          _name: 1,
          _typeName: 1,
          _certifierName: 1,
          workload: 1,
          acronym: 1,
        },
        options: {
          unique: 'course_already_exists',
          name: 'dup_course',
        },
      },
      {
        fields: {
          _name: 'text',
        },
        options: {},
      },
      {
        fields: {
          number: 1,
        },
        options: {
          unique: 'number_already_exists',
          name: 'number_1',
          partialFilterExpression: {
            number: {
              $type: 'number',
            },
          },
        },
      },
    ],
  },
};
Courses.database.post = {
  save: [
    Courses.functions.setNumber,
  ],
  insertMany: [
    Courses.functions.setNumber,
  ],
  findOneAndUpdate: [
    Courses.functions.setNumber,
  ],
  findOneAndReplace: [
    Courses.functions.setNumber,
  ],
  updateOne: [
    Courses.functions.setNumber,
  ],
  updateMany: [
    Courses.functions.setNumber,
  ],
};
module.exports = Courses;
