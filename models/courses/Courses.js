const mongoose = require('mongoose');
const slugify = require('slugify');
const modelName = 'Courses';

const coursePaymentPartnerSchema = {
  type: {
    paidInCash: {
      type: Number,
      required: true,
    },
    paidInInstalment: {
      type: Number,
    },
    commissionMedium: {
      type: Number,
    },
    commissionMaximum: {
      type: Number,
    },
  },
  // required: false,
  allowNull: true,
};

const paymentPlanSchema = {
  creditCard: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  debitCard: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  boleto: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  cardRecurrence: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
  pix: [
    {
      _id: false,
      installment: {
        type: Number,
      },
      value: {
        type: Number,
      },
    },
  ],
};
const reducedPaymentPlanSchema = {
  installment: {
    type: Number,
  },
  value: {
    type: Number,
  },
};

const extraCoursewaresSchema = {
  _id: {
    type: mongoose.SchemaTypes.ObjectId,
    required: true,
  },
  title: {
    type: String,
    required: true,
    index: true,
  },
  alias: {
    type: String,
    required: true,
    index: true,
  },
  type: {
    type: String,
    // required: false,
    default: 'pdf',
  },
  file: {
    type: String,
    // required: false,
    allowNull: true,
    default: null,
    index: true,
  },
  content: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  description: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  contentPortal: {
    type: String,
    // required: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  chapter: {
    type: Number,
  },
  page: {
    type: Number,
  },
  teacher: {
    type: String,
  },
};

const Courses = {
  functions: {
    sortPaymentPlans: async function (course) {
      const sortFn = (a, b) =>
        a.installment === b.installment
          ? 0
          : +(a.installment > b.installment) || -1;

      course.paymentPlan.boleto = course.paymentPlan.boleto.sort(sortFn);
      course.paymentPlan.creditCard =
        course.paymentPlan.creditCard.sort(sortFn);
      course.paymentPlan.debitCard = course.paymentPlan.debitCard.sort(sortFn);
      course.paymentPlan.cardRecurrence =
        course.paymentPlan.cardRecurrence.sort(sortFn);
      course.paymentPlan.pix = course.paymentPlan.pix.sort(sortFn);

      if (!course.searchName) {
        course.searchName =
          slugify(course._name).toLowerCase().replace(/-/gm, ' ') +
          ' ' +
          course.workload;
      }

      return await course.save();
    },
    setSearchName: function (next) {
      this.searchName = slugify(this._name).toLowerCase().replace(/-/gm, ' ');
      next();
    },
    setNumber: async function (r) {
      let model = null;
      if (
        this.constructor &&
        ['function'].includes(typeof this.constructor) &&
        this.constructor.modelName &&
        this.constructor.modelName === modelName
      ) {
        model = this.constructor;
      } else if (
        this &&
        ['function'].includes(typeof this) &&
        this.modelName &&
        this.modelName === modelName
      ) {
        model = this;
      } else if (
        this &&
        ['object'].includes(typeof this) &&
        this.model &&
        ['function'].includes(typeof this.model) &&
        this.model.modelName &&
        this.model.modelName === modelName
      ) {
        model = this.model;
      }
      if (model) {
        let find;
        let arrObj;
        if (
          this &&
          ['function', 'object'].includes(typeof this) &&
          'getQuery' in this &&
          this.getQuery()
        ) {
          find = this.getQuery();
          arrObj = await model.find(find);
        } else {
          arrObj = Array.isArray(r) ? r : [r];
        }
        if (arrObj && Array.isArray(arrObj) && arrObj.length) {
          for (let i = 0; i < arrObj.length; i++) {
            if (arrObj[i] && typeof arrObj[i] === 'object' && arrObj[i]._id) {
              if (!arrObj[i].number) {
                let number = 1;
                const lastObj = await model
                  .findOne({number: {$ne: null}})
                  .sort({number: -1});
                if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                  if ('number' in lastObj && !isNaN(lastObj.number)) {
                    number = parseInt(lastObj.number.toString()) + 1;
                  }
                }
                let searchNumber = true;
                while (searchNumber) {
                  arrObj[i].number = number;
                  const newObj = await arrObj[i]
                    .save({validateBeforeSave: false})
                    .then((c) => c)
                    .catch(() => null);
                  if (
                    newObj &&
                    typeof newObj === 'object' &&
                    newObj._id &&
                    newObj.number
                  ) {
                    searchNumber = false;
                    break;
                  } else {
                    number++;
                  }
                }
              }
            }
          }
        }
      }
      return r;
    },
    verifyPaymentPartnerValues: function (next) {
      if (this.paymentPartner && typeof this.paymentPartner === 'object' && this.paymentPartner.paidInInstalment !== undefined) {
        this.paymentPartner = {
          ...this.paymentPartner, ...{
            commissionMedium: this.paymentPartner?.paidInInstalment || 0,
            commissionMaximum: this.paymentPartner?.paidInInstalment || 0,
          },
        };
      }
      return next();
    },
  },
  database: {
    collection: modelName,
    fields: {
      _name: {
        type: String,
        uppercase: true,
        required: true,
      },
      number: {
        type: Number,
        allowNull: true,
        default: null,
      },
      searchName: {
        type: String,
        lowercase: true,
      },
      _typeName: {
        type: String,
        required: true,
      },
      _certifierName: {
        type: String,
        required: true,
      },
      _knowledgeAreaName: {
        type: String,
        required: true,
      },
      _categoryName: {
        type: String,
        default: 'Lato Sensu',
      },
      _subCategory: {
        type: String,
        default: 'Premium',
      },
      _areaNames: {
        type: [String],
        required: true,
      },
      evaluationMethod: {
        type: String,
        enum: [
          'average', // média
          'modular_sum', // soma modular
        ],
        required: true,
        default: 'average',
      },
      videoIntroduction: {
        type: String,
        default: null,
      },
      marketingVideoTeaser: {
        type: String,
        default: null,
      },
      marketingVideoBody: {
        type: String,
        default: null,
      },
      image: {
        type: String,
        // required: false
      },
      faculty: {
        type: String,
        // required: false,
        default: null,
      },
      methodology: {
        type: String,
        // required: false,
        default: null,
      },
      objective: {
        type: String,
        // required: false,
        default: null,
      },
      program: {
        type: String,
        // required: false,
        default: null,
      },
      completeOrdinance: {
        type: String,
        // required: false,
        allowNull: true,
        default: null,
      },
      ordinance: {
        type: String,
        // required: false,
        allowNull: true,
        default: null,
      },
      ordinanceDate: {
        type: Date,
        // required: false,
        allowNull: true,
        default: null,
      },
      douDate: {
        type: Date,
        // required: false,
        allowNull: true,
        default: null,
      },
      targetPublic: {
        type: String,
        // required: false,
        default: null,
      },
      siteTitle: {
        type: String,
        // required: false
      },
      depositions: [
        {
          _id: false,
          name: {
            type: String,
            // required: false
          },
          photo: {
            type: String,
            // required: false
          },
          description: {
            type: String,
            // required: false
          },
        },
      ],
      photo: {
        type: String,
        // required: false
      },
      photo_miniature: {
        type: String,
        // required: false
      },
      score: {
        type: String,
        // required: false
      },
      tags: {
        type: Array,
        // required: false
      },
      internshipRequiredWorkload: {
        type: Number,
        // required: false
      },
      periodNames: [
        {
          period: {
            type: Number,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
        },
      ],
      disciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            default: null,
          },
          period: {
            type: Number,
          },
          _name: {
            type: String,
            required: true,
          },
          sagahDiscipline: {
            type: String,
            // required: false
          },
          teacher: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          type: {
            type: String,
            enum: ['required', 'optional'],
            required: true,
          },
          description: {
            type: String
          },
          skills: {
            type: String,
            // required: false
          },
          habilities: {
            type: String,
            // required: false
          },
          gradeSystem: {
            type: String,
            // required: false
          },
          methodology: {
            type: String,
            // required: false
          },
          teacherOrientations: {
            type: String,
            // required: false
          },
          mainThemes: {
            type: String,
            // required: false
          },
          extraThemes: {
            type: String,
            // required: false
          },
          programContent: {
            type: String,
            // required: false
          },
          objective: {
            type: String,
            // required: false,
            default: null,
          },
          program: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            // required: false
          },
          theoreticalWorkload: {
            type: Number,
            // required: false
          },
          practicalWorkload: {
            type: Number,
            // required: false
          },
          extensionWorkload: {
            type: Number,
            // required: false
          },
          ignoreOffersCalendar: {
            type: Boolean,
            default: false,
          },
          tags: {
            type: Array,
            // required: false
          },
          activities: [
            {
              type: {
                type: String,
                enum: ['regular', 'recuperation'],
                required: true,
              },
              modality: {
                type: String,
                enum: ['online', 'presential'],
                required: true,
              },
              maxDuration: {
                type: Number,
                // required: false
              },
              chapter: {
                type: [
                  {
                    _id: false,
                    coursewareId: mongoose.SchemaTypes.ObjectId,
                    coursewareName: String,
                    number: Number,
                  },
                ],
              },
              model: {
                type: String,
                enum: [
                  'form',
                  'evaluation',
                  'upload',
                  'participation',
                  'sagah',
                  'forum',
                ],
                required: true,
              },
              isFinalTest: {
                type: Boolean,
                required: true,
              },
              modelMeta: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                },
                numQuestions: {
                  type: Number,
                  // required: false
                },
                enunciation: {
                  type: String,
                  // required: false
                },
                ltiUrl: {
                  type: String,
                  // required: false
                },
                ltiTitle: {
                  type: String,
                  // required: false
                },
              },
              maxGrade: {
                type: Number,
                required: true,
                default: 10,
              },
            },
          ],
          chapter: {
            type: [
              {
                _id: false,
                coursewareId: mongoose.SchemaTypes.ObjectId,
                coursewareName: String,
                number: Number,
              },
            ],
          },
          _forums: {
            type: [
              {
                forumId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                sendArchiveStudent: {
                  // Estudante podem enviar documentos
                  type: Boolean,
                  default: false,
                },
                manyPostsPerStudent: {
                  // Mais de Uma Postagem Por Aluno
                  type: Boolean,
                  default: false,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          directedStudies: {
            type: [
              {
                directedStudyId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                sendArchiveStudent: {
                  // Estudante podem enviar documentos
                  type: Boolean,
                  default: true,
                },
                commentStudent: {
                  // Estudante podem enviar comentários
                  type: Boolean,
                  required: true,
                  default: true,
                },
                manyPostsPerStudent: {
                  // Mais de Uma Postagem Por Aluno
                  type: Boolean,
                  default: false,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          coursewareType: {
            type: String,
          },
          _coursewares: {
            type: [mongoose.SchemaTypes.ObjectId],
            // required: false
          },
          supportFile: {
            type: String,
            // required: false
          },
          pathknowledges: {
            type: [
              {
                _id: mongoose.SchemaTypes.ObjectId,
                name: String,
                courseware: {
                  type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    title: String,
                  },
                },
                pieces: [
                  {
                    useGrade: Boolean,
                    grade: Number,
                    name: String,
                    _pieceId: mongoose.SchemaTypes.ObjectId,
                  },
                ],
                grade: Number,
                useGrade: Boolean,
                dateStart: Date,
                dateEnd: Date,
              },
            ],
          },
          dependecyConfig: {
            type: {
              useDependecy: Boolean,
              activities: [
                {
                  type: {
                    type: String,
                    enum: ['regular', 'recuperation'],
                    // required: false
                  },
                  modality: {
                    type: String,
                    enum: ['online', 'presential'],
                    // required: false
                  },
                  maxDuration: {
                    type: Number,
                    // required: false
                  },
                  model: {
                    type: String,
                    enum: [
                      'form',
                      'evaluation',
                      'upload',
                      'participation',
                      'sagah',
                      'forum',
                    ],
                    // required: false
                  },
                  isFinalTest: {
                    type: Boolean,
                    // required: false
                  },
                  chapter: {
                    type: [
                      {
                        _id: false,
                        coursewareId: mongoose.SchemaTypes.ObjectId,
                        coursewareName: String,
                        number: Number,
                      },
                    ],
                  },
                  modelMeta: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                    },
                    numQuestions: {
                      type: Number,
                      // required: false
                    },
                    enunciation: {
                      type: String,
                      // required: false
                    },
                    ltiUrl: {
                      type: String,
                      // required: false
                    },
                    ltiTitle: {
                      type: String,
                      // required: false
                    },
                  },
                  maxGrade: {
                    type: Number,
                    // required: false,
                    default: 10,
                  },
                },
              ],
              useRate: Boolean,
              rateAmount: Number,
              period: Number,
            },
          },
          courseGenerated: {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
          },
          extraCoursewares: {
            type: [extraCoursewaresSchema],
            default: [],
          },
        },
      ],
      predefinedGrids: [
        {
          name: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            required: true,
          },
          _disciplines: {
            type: [mongoose.SchemaTypes.ObjectId],
            required: true,
          },
        },
      ],
      acronym: {
        type: String,
        required: true,
        unique: 'Sigla já existe',
      },
      workload: {
        type: Number,
        // required: false
      },
      theoreticalWorkload: {
        type: Number,
        // required: false
      },
      practicalWorkload: {
        type: Number,
        // required: false
      },
      extensionWorkload: {
        type: Number,
        // required: false
      },
      minMonthsToComplete: {
        type: Number,
        // required: false
      },
      maxMonthsToComplete: {
        type: Number,
        // required: false
      },
      linkEMec: {
        type: String,
        required: true,
      },
      paymentPlan: paymentPlanSchema,
      onEnrolmentPaymentPlan: reducedPaymentPlanSchema,
      paymentPartner: coursePaymentPartnerSchema,
      approvalPercentage: {
        type: Number,
        required: true,
      },
      minimalFrequency: {
        type: Number,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      isTccRequired: {
        type: Boolean,
        required: true,
      },
      isInternshipRequired: {
        type: Boolean,
        required: true,
      },
      isEnabled: {
        type: Boolean,
        // required: false,
        default: false,
      },

      technologicalAxis: {
        type: String,
        // required: false
      },
      ownership: {
        type: String,
        // required: false
      },
      hoursComplementaryActivity: {
        type: Number,
        // required: false
      },
      hoursComplementaryActivityRequired: {
        type: Number,
        // required: false
      },
      isRequiredHoursComplementaryActivity: {
        type: Boolean,
        // required: false
      },
      mandatoryDocumentsType: {
        type: [
          {
            typeId: {
              // Id do documento
              type: mongoose.SchemaTypes.ObjectId,
              // required: false
            },
            typeName: {
              // Nome do documento
              type: String,
              // required: false
            },
          },
        ],
        // required: false,
        default: undefined,
      },
      responsible: {
        type: String,
        // required: false
      },
      functionName: {
        type: String,
        // required: false
      },
      schoolSecretary: {
        type: String,
        // required: false
      },
      authorizationSecretary: {
        type: String,
        // required: false
      },
      mecCode: {
        type: Number,
        // required: false
      },
      resolution: {
        type: String,
        // required: false
      },
      amountPeriodicity: {
        type: Number,
        // required: false
      },
      periodicity: {
        type: String,
        // required: false
      },
      reEnrolment: {
        type: {
          typeBreak: {
            type: String,
            // required: false
          },
          typeValidity: {
            type: String,
            // required: false
          },
          validityReEnrolment: {
            type: Number,
            // required: false
          },
          acceptanceTerm: {
            type: Boolean,
            // required: false
          },
          generateBilling: {
            type: Boolean,
            // required: false
          },
          blockDefault: {
            type: Boolean,
            // required: false
          },
          havePaymentPlan: {
            type: Boolean,
            // required: false
          },
          paymentPlan: reducedPaymentPlanSchema,
          termReEnrolment: {
            type: String,
            // required: false
          },
          applyMonetaryCorrection: {
            type: Boolean,
            default: false,
          },
        },
      },
      releaseDisciplines: {
        type: {
          releaseType: String,
          isAutomatic: Boolean,
        },
      },
      certificateCredits: {
        type: Number,
        required: true,
        default: 0,
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false
      },
      codeContedtech: {
        type: Number,
        // required: false
      },
      partnersComissions: {
        // required: false,
        type: [
          {
            _id: false,
            name: {
              type: String,
              required: true,
            },
            cpf: {
              type: String,
              required: true,
            },
            commissionMonthly: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionEnrolment: {
              type: Number,
              // required: false,
              default: 0,
            },
          },
        ],
      },
      actuationArea: {
        type: String,
        // required: false
      },
      campaignImage: {
        type: String,
        // required: false
      },
      optionalDisciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            default: null,
          },
          period: {
            type: Number,
          },
          _name: {
            type: String,
            required: true,
          },
          sagahDiscipline: {
            type: String,
            // required: false
          },
          teacher: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          type: {
            type: String,
            enum: ['required', 'optional'],
            required: true,
          },
          description: {
            type: String
          },
          skills: {
            type: String,
            // required: false
          },
          habilities: {
            type: String,
            // required: false
          },
          gradeSystem: {
            type: String,
            // required: false
          },
          methodology: {
            type: String,
            // required: false
          },
          teacherOrientations: {
            type: String,
            // required: false
          },
          mainThemes: {
            type: String,
            // required: false
          },
          extraThemes: {
            type: String,
            // required: false
          },
          programContent: {
            type: String,
            // required: false
          },
          objective: {
            type: String,
            // required: false,
            default: null,
          },
          program: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            required: true,
          },
          tags: {
            type: Array,
            // required: false
          },
          activities: [
            {
              type: {
                type: String,
                enum: ['regular', 'recuperation'],
                required: true,
              },
              modality: {
                type: String,
                enum: ['online', 'presential'],
                required: true,
              },
              chapter: {
                type: [
                  {
                    _id: false,
                    coursewareId: mongoose.SchemaTypes.ObjectId,
                    coursewareName: String,
                    number: Number,
                  },
                ],
              },
              maxDuration: {
                type: Number,
                // required: false
              },
              model: {
                type: String,
                enum: [
                  'form',
                  'evaluation',
                  'upload',
                  'participation',
                  'sagah',
                  'forum',
                ],
                required: true,
              },
              isFinalTest: {
                type: Boolean,
                required: true,
              },
              modelMeta: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                },
                numQuestions: {
                  type: Number,
                  // required: false
                },
                enunciation: {
                  type: String,
                  // required: false
                },
                ltiUrl: {
                  type: String,
                  // required: false
                },
                ltiTitle: {
                  type: String,
                  // required: false
                },
              },
              maxGrade: {
                type: Number,
                required: true,
                default: 10,
              },
            },
          ],
          chapter: {
            type: [
              {
                _id: false,
                coursewareId: mongoose.SchemaTypes.ObjectId,
                coursewareName: String,
                number: Number,
              },
            ],
          },
          _forums: {
            type: [
              {
                forumId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          directedStudies: {
            type: [
              {
                directedStudyId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          coursewareType: {
            type: String,
          },
          _coursewares: {
            type: [mongoose.SchemaTypes.ObjectId],
            // required: false
          },
          supportFile: {
            type: String,
            // required: false
          },
          pathknowledges: {
            type: [
              {
                _id: mongoose.SchemaTypes.ObjectId,
                name: String,
                courseware: {
                  type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    title: String,
                  },
                },
                pieces: [
                  {
                    useGrade: Boolean,
                    grade: Number,
                    name: String,
                    _pieceId: mongoose.SchemaTypes.ObjectId,
                  },
                ],
                grade: Number,
                useGrade: Boolean,
                dateStart: Date,
                dateEnd: Date,
              },
            ],
          },
          dependecyConfig: {
            type: {
              useDependecy: Boolean,
              activities: [
                {
                  type: {
                    type: String,
                    enum: ['regular', 'recuperation'],
                    // required: false
                  },
                  modality: {
                    type: String,
                    enum: ['online', 'presential'],
                    // required: false
                  },
                  maxDuration: {
                    type: Number,
                    // required: false
                  },
                  model: {
                    type: String,
                    enum: [
                      'form',
                      'evaluation',
                      'upload',
                      'participation',
                      'sagah',
                      'forum',
                    ],
                    // required: false
                  },
                  isFinalTest: {
                    type: Boolean,
                    // required: false
                  },
                  chapter: {
                    type: [
                      {
                        _id: false,
                        coursewareId: mongoose.SchemaTypes.ObjectId,
                        coursewareName: String,
                        number: Number,
                      },
                    ],
                  },
                  modelMeta: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                    },
                    numQuestions: {
                      type: Number,
                      // required: false
                    },
                    enunciation: {
                      type: String,
                      // required: false
                    },
                    ltiUrl: {
                      type: String,
                      // required: false
                    },
                    ltiTitle: {
                      type: String,
                      // required: false
                    },
                  },
                  maxGrade: {
                    type: Number,
                    // required: false,
                    default: 10,
                  },
                },
              ],
              useRate: Boolean,
              rateAmount: Number,
              period: Number,
            },
          },
        },
      ],
      curriculumMatrix: {
        _id: mongoose.SchemaTypes.ObjectId,
        code: String,
        hoursComplementaryActivity: Number,
        internshipWorkload: Number,
        quantityOptionalDisciplines: Number,
        workloadOptionalDisciplines: Number,
        internships: [
          {
            schedulingSent: {
              type: Boolean,
            },
            shipmentType: {
              type: String,
            },
            shipmentTypeQuantity: {
              type: mongoose.SchemaTypes.Mixed,
            },
            shipmentQuantity: {
              type: Number,
            },
            workload: {
              type: Number,
            },
            helpFile: {
              type: String,
            },
            fileModelsInternshipRequired: {
              type: [
                {
                  url: String,
                  name: String,
                  typeDocument: String,
                },
              ],
              // required: false
            },
            stepsInternshipRequired: {
              type: [
                {
                  isRequired: Boolean,
                  typeInternship: String,
                  title: String,
                  action: String,
                  description: String,
                  nextStatus: String,
                  reviewBy: String,
                  visibleIn: String,
                  originalFile: String,
                  order: Number,
                  internalAnalisy: Boolean,
                  typeLimitCorrect: String,
                  dateLimit: Date,
                  limitCorrect: Number,
                  multiFile: Boolean,
                },
              ],
              // required: false
            },
            area: {
              type: [
                {
                  _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                  },
                  name: {
                    type: String,
                    required: true,
                  },
                },
              ],
              // required: false
            },
            tutor: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  required: true,
                },
                name: {
                  type: String,
                  required: true,
                },
              },
              // required: false
            },
            title: String,
          },
        ],
      },
      hasCurriculumMatrix: {
        type: Boolean,
        default: false,
      },
      modality: {
        type: String,
      },
      coordinator: {
        type: {
          name: String,
          _id: String,
        },
      },
      authorization: {
        type: {
          inProcess: {
            type: Boolean,
            // required: false,
          },
          // type: {
          //     type: String,
          //     // required: false,
          //     allowNull: true,
          //     default: null
          // },
          typeInMec: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          number: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          date: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          publisher: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          datePublication: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          sessionPublication: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          pagePublication: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          diaryNumber: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationNumber: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationType: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationDateRegister: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationDateProtocol: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
        },
        // required: false
      },
      recognition: {
        type: {
          inProcess: {
            type: Boolean,
            // required: false,
          },
          // type: {
          //     type: String,
          //     // required: false,
          //     allowNull: true,
          //     default: null
          // },
          typeInMec: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          number: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          date: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          publisher: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          datePublication: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          sessionPublication: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          pagePublication: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          diaryNumber: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationNumber: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationType: {
            type: String,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationDateRegister: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
          tramitationDateProtocol: {
            type: Date,
            // required: false,
            allowNull: true,
            default: null,
          },
        },
        // required: false
      },
      quantityOptionalDisciplines: {
        type: Number,
        // required: false
      },
      gradeInMec: {
        type: {
          course: Number,
          certifier: Number,
        },
      },
      documents: {
        type: [
          {
            _id: false,
            _documentTypeId: {
              type: mongoose.SchemaTypes.ObjectId,
              required: true,
            },
            name: {
              type: String,
              required: true,
            },
            isRequired: {
              type: Boolean,
              required: true,
            },
            genreRequired: {
              type: [String],
              required: true,
            },
          },
        ],
        // required: true, // TODO: MUDAR PRA REQUIRED E HABILITAR VALIDATE POSTERIORMENTE DPS QUE ESTIVER TUDO OK
        // validate: [
        //     function(val) {
        //         return val.length > 0;
        //     },
        //     '{PATH} min length is 1'
        // ]
      },
      courseNameEMec: {
        type: String,
      },
      useCademi: {
        type: Boolean,
        default: false,
      },
      codCademi: {
        type: Number,
        default: null,
      },
      backupPrices: coursePaymentPartnerSchema,
      originalCourse: {
        _id: mongoose.SchemaTypes.ObjectId,
        name: String,
      },
      code: String,
      needHealthAreaPolo: {
        type: Boolean,
        default: false,
      },
    },
    /*post: {
                findOneAndUpdate: [sortPaymentPlans]
            },*/
    indexes: [
      {
        fields: {
          _name: 1,
          _typeName: 1,
          _certifierName: 1,
          workload: 1,
          acronym: 1,
        },
        options: {
          unique: 'course_already_exists',
          name: 'dup_course',
        },
      },
      {
        fields: {
          _name: 'text',
        },
        options: {},
      },
      {
        fields: {
          number: 1,
        },
        options: {
          unique: 'number_already_exists',
          name: 'number_1',
          partialFilterExpression: {
            number: {
              $type: 'number',
            },
          },
        },
      },
    ],
  },
};
Courses.database.pre = {
  save: [Courses.functions.verifyPaymentPartnerValues],
  insertMany: [Courses.functions.verifyPaymentPartnerValues],
  updateOne: [Courses.functions.verifyPaymentPartnerValues],
  updateMany: [Courses.functions.verifyPaymentPartnerValues],
};
Courses.database.post = {
  save: [Courses.functions.setNumber],
  insertMany: [Courses.functions.setNumber],
  findOneAndUpdate: [Courses.functions.setNumber],
  findOneAndReplace: [Courses.functions.setNumber],
  updateOne: [Courses.functions.setNumber],
  updateMany: [Courses.functions.setNumber],
};
module.exports = Courses;
module.exports.coursePaymentPartnerSchema = coursePaymentPartnerSchema;
