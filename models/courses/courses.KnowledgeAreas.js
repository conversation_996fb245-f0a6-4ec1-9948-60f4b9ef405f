let KnowledgeAreas = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'KnowledgeAreas',
        fields    : {
            name     : {
                type     : String,
                required : true,
                maxlength: 255,
                unique   : 'knowledge_area_already_exits'
            },
            isEnabled: {
                type    : Boolean,
                // required: false,
                default : false
            }
        },
        indexes   : [
            {
                fields : {
                    name: 'text'
                },
                options: {}
            }
        ]
    }
};

module.exports = KnowledgeAreas;
