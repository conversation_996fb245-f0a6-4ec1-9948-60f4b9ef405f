const mongoose = require('mongoose');

let CourseHistory = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'CourseHistory',
        fields    : {
            _courseId: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            event    : {
                type    : String,
                required: true
            },
            metadata : {
                type    : mongoose.SchemaTypes.Mixed,
            },
            author   : {
                _userid    : {
                    type    : mongoose.SchemaTypes.ObjectId,
                },
                _username  : {
                    type    : String,
                },
                _usertype  : {
                    type    : String,
                },
                _department: {
                    type    : String,
                }
            }
        }
    }
};

module.exports = CourseHistory;
