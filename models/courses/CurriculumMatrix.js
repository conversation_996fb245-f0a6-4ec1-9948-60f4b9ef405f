const mongoose = require('mongoose');
const modelName = 'CurriculumMatrix';

const extraCoursewaresSchema = {
  _id: {
    type: mongoose.SchemaTypes.ObjectId,
    required: true,
  },
  title: {
    type: String,
    required: true,
    index: true,
  },
  alias: {
    type: String,
    required: true,
    index: true,
  },
  type: {
    type: String,
    // required: false,
    default: 'pdf',
  },
  file: {
    type: String,
    // required: false,
    allowNull: true,
    default: null,
    index: true,
  },
  content: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  description: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  contentPortal: {
    type: String,
    // required: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  chapter: {
    type: Number,
  },
  page: {
    type: Number,
  },
  teacher: {
    type: String,
  },
};

const workloadSchema = {
  theoreticalWorkload: {
    type: Number,
    default: 0,
  },
  practicalWorkload: {
    type: Number,
    default: 0,
  },
  extensionWorkload: {
    type: Number,
    default: 0,
  },
};

const CurriculumMatrix = {
  functions: {},
  database: {
    collection: modelName,
    connection: 'database_piaget',
    functions: {
      sortDisciplines: function (next) {
        this.disciplines.sort((a, b) => a.period - b.period);

        return next();
      },
    },
    fields: {
      dataCourse: {
        type: {
          _id: mongoose.SchemaTypes.ObjectId || null,
          certifier: String,
          typeName: String,
          course: String,
        },
      },
      isActive: {
        type: Boolean,
        default: true,
      },
      isVisible: {
        type: Boolean,
        default: true,
      },
      code: {
        type: String,
        required: true,
        default: 0,
      },
      quantityOptionalDisciplines: {
        type: Number,
      },
      hoursComplementaryActivity: {
        type: Number,
        // required: false,
        default: 0,
      },
      internshipWorkload: {
        type: Number,
        // required: false,
        default: 0,
      },
      requiredDisciplinesWorkload: {
        type: workloadSchema,
        required: true,
      },
      optionalDisciplinesWorkload: {
        type: workloadSchema,
        required: true,
      },
      workloadOptionalDisciplines: {
        type: Number,
        default: 0,
      },
      workload: {
        type: Number,
        required: true,
        default: 0,
      },
      periodNames: [
        {
          period: {
            type: Number,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
        },
      ],
      disciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            default: null,
          },
          period: {
            type: Number,
            // required: false,
            default: null,
          },
          _name: {
            type: String,
            required: true,
          },
          sagahDiscipline: {
            type: String,
            // required: false
          },
          teacher: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: function () {
                  // Se o 'type' do schema pai for 'required', então '_id' deve existir
                  return this.parent()?.type === 'required';
                },
              },
              name: {
                type: String,
                required: false,
              },
            },
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          type: {
            type: String,
            enum: [
              'required',
              'optional',
            ],
            required: true,
          },
          description: {
            type: String,
            required: true,
          },
          skills: {
            type: String,
            // required: false
          },
          habilities: {
            type: String,
            // required: false
          },
          gradeSystem: {
            type: String,
            // required: false
          },
          methodology: {
            type: String,
            // required: false
          },
          teacherOrientations: {
            type: String,
            // required: false
          },
          mainThemes: {
            type: String,
            // required: false
          },
          extraThemes: {
            type: String,
            // required: false
          },
          programContent: {
            type: String,
            // required: false
          },
          objective: {
            type: String,
            // required: false,
            default: null,
          },
          program: {
            type: String,
            // required: false,
            default: null,
          },
          workload: {
            type: Number,
            required: true,
          },
          theoreticalWorkload: { 
            type: Number, 
            // required: false 
          },
          practicalWorkload: {
            type: Number,
            // required: false 
          },
          extensionWorkload: { 
            type: Number, 
            // required: false 
          },
          ignoreOffersCalendar: {
            type: Boolean,
            default: false,
          },
          tags: {
            type: Array,
            // required: false
          },
          activities: [
            {
              type: {
                type: String,
                enum: [
                  'regular',
                  'recuperation',
                ],
                required: true,
              },
              modality: {
                type: String,
                enum: [
                  'online',
                  'presential',
                ],
                required: true,
              },
              maxDuration: {
                type: Number,
                // required: false
              },
              model: {
                type: String,
                enum: [
                  'form',
                  'evaluation',
                  'upload',
                  'participation',
                  'sagah',
                  'forum',
                ],
                required: true,
              },
              chapter: {
                type: [{
                  _id: false,
                  coursewareId: mongoose.SchemaTypes.ObjectId,
                  coursewareName: String,
                  number: Number,
                }],
              },
              isFinalTest: {
                type: Boolean,
                required: true,
              },
              modelMeta: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                },
                numQuestions: {
                  type: Number,
                  // required: false
                },
                enunciation: {
                  type: String,
                  // required: false
                },
                ltiUrl: {
                  type: String,
                  // required: false
                },
                ltiTitle: {
                  type: String,
                  // required: false
                },
              },
              maxGrade: {
                type: Number,
                required: true,
                default: 10,
              },
            },
          ],
          chapter: {
            type: [{
              _id: false,
              coursewareId: mongoose.SchemaTypes.ObjectId,
              coursewareName: String,
              number: Number,
            }],
          },
          _forums: {
            type: [
              {
                forumId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          directedStudies: {
            type: [
              {
                directedStudyId: mongoose.SchemaTypes.ObjectId,
                title: {
                  type: String,
                  required: true,
                },
                description: {
                  type: String,
                  default: '',
                },
                files: [
                  {
                    url: {
                      type: String,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                    type: {
                      type: String,
                      required: true,
                    },
                  },
                ],
                allPostVisibleToStudent: {
                  // Estudante poderam ver mensagens de outros estudantes
                  type: Boolean,
                  required: true,
                },
                type: {
                  // Informativo ou avaliativo
                  type: String,
                  required: true,
                },
                grade: {
                  // Nota máxima que pode ser alcançada
                  type: Number,
                  default: null,
                },
              },
            ],
            // required: false,
            default: undefined,
          },
          coursewareType: {
            type: String,
          },
          _coursewares: {
            type: [mongoose.SchemaTypes.ObjectId],
            // required: false
          },
          supportFile: {
            type: String,
            // required: false
          },
          pathknowledges: {
            type: [{
              _id: mongoose.SchemaTypes.ObjectId,
              name: String,
              courseware: {
                _id: mongoose.SchemaTypes.ObjectId,
                title: String,
              },
              pieces: [{
                useGrade: Boolean,
                grade: Number,
                name: String,
                _pieceId: mongoose.SchemaTypes.ObjectId,
              }],
              grade: Number,
              useGrade: Boolean,
              dateStart: Date,
              dateEnd: Date,
            }],
          },
          dependecyConfig: {
            type: {
              useDependecy: Boolean,
              activities: [
                {
                  type: {
                    type: String,
                    enum: [
                      'regular',
                      'recuperation',
                    ],
                    // required: false
                  },
                  modality: {
                    type: String,
                    enum: [
                      'online',
                      'presential',
                    ],
                    // required: false
                  },
                  maxDuration: {
                    type: Number,
                    // required: false
                  },
                  model: {
                    type: String,
                    enum: [
                      'form',
                      'evaluation',
                      'upload',
                      'participation',
                      'sagah',
                      'forum',
                    ],
                    // required: false
                  },
                  isFinalTest: {
                    type: Boolean,
                    // required: false
                  },
                  chapter: {
                    type: [{
                      _id: false,
                      coursewareId: mongoose.SchemaTypes.ObjectId,
                      coursewareName: String,
                      number: Number,
                    }],
                  },
                  modelMeta: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                    },
                    numQuestions: {
                      type: Number,
                      // required: false
                    },
                    enunciation: {
                      type: String,
                      // required: false
                    },
                    ltiUrl: {
                      type: String,
                      // required: false
                    },
                    ltiTitle: {
                      type: String,
                      // required: false
                    },
                  },
                  maxGrade: {
                    type: Number,
                    // required: false,
                    default: 10,
                  },
                },
              ],
              useRate: Boolean,
              rateAmount: Number,
              period: Number,
            },
          },
          extraCoursewares: {
            type: [extraCoursewaresSchema],
            default: [],
          },
        },
      ],
      optionalDisciplines: {
        type: [
          {
            discId: {
              type: mongoose.SchemaTypes.ObjectId,
              // required: false,
              default: null,
            },
            _name: {
              type: String,
              // required: false
            },
            sagahDiscipline: {
              type: String,
              // required: false
            },
            teacher: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  // required: false
                },
                name: {
                  type: String,
                  // required: false
                },
              },
              // required: false
            },
            tutor: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  // required: false
                },
                name: {
                  type: String,
                  // required: false
                },
              },
              // required: false
            },
            type: {
              type: String,
              enum: [
                'required',
                'optional',
              ],
              // required: false
            },
            description: {
              type: String,
              // required: false
            },
            skills: {
              type: String,
              // required: false
            },
            habilities: {
              type: String,
              // required: false
            },
            gradeSystem: {
              type: String,
              // required: false
            },
            methodology: {
              type: String,
              // required: false
            },
            teacherOrientations: {
              type: String,
              // required: false
            },
            mainThemes: {
              type: String,
              // required: false
            },
            extraThemes: {
              type: String,
              // required: false
            },
            programContent: {
              type: String,
              // required: false
            },
            period: {
              type: Number,
              // required: false,
              default: null,
            },
            workload: {
              type: Number,
              // required: false
            },
            theoreticalWorkload: { 
              type: Number, 
              // required: false 
            },
            practicalWorkload: {
              type: Number,
              // required: false 
            },
            extensionWorkload: { 
              type: Number, 
              // required: false 
            },
            tags: {
              type: Array,
              // required: false
            },
            activities: [
              {
                type: {
                  type: String,
                  enum: [
                    'regular',
                    'recuperation',
                  ],
                  // required: false
                },
                modality: {
                  type: String,
                  enum: [
                    'online',
                    'presential',
                  ],
                  // required: false
                },
                maxDuration: {
                  type: Number,
                  // required: false
                },
                model: {
                  type: String,
                  enum: [
                    'form',
                    'evaluation',
                    'upload',
                    'participation',
                    'sagah',
                    'forum',
                  ],
                  // required: false
                },
                isFinalTest: {
                  type: Boolean,
                  // required: false
                },
                chapter: {
                  type: [{
                    _id: false,
                    coursewareId: mongoose.SchemaTypes.ObjectId,
                    coursewareName: String,
                    number: Number,
                  }],
                },
                modelMeta: {
                  _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                  },
                  numQuestions: {
                    type: Number,
                    // required: false
                  },
                  enunciation: {
                    type: String,
                    // required: false
                  },
                  ltiUrl: {
                    type: String,
                    // required: false
                  },
                  ltiTitle: {
                    type: String,
                    // required: false
                  },
                },
                maxGrade: {
                  type: Number,
                  // required: false,
                  default: 10,
                },
              },
            ],
            chapter: {
              type: [{
                _id: false,
                coursewareId: mongoose.SchemaTypes.ObjectId,
                coursewareName: String,
                number: Number,
              }],
            },
            _forums: {
              type: [
                {
                  forumId: mongoose.SchemaTypes.ObjectId,
                  title: {
                    type: String,
                    // required: false
                  },
                  description: {
                    type: String,
                    default: '',
                  },
                  files: [
                    {
                      url: {
                        type: String,
                        // required: false
                      },
                      name: {
                        type: String,
                        // required: false
                      },
                      type: {
                        type: String,
                        // required: false
                      },
                    },
                  ],
                  allPostVisibleToStudent: {
                    // Estudante poderam ver mensagens de outros estudantes
                    type: Boolean,
                    // required: false
                  },
                  type: {
                    // Informativo ou avaliativo
                    type: String,
                    // required: false
                  },
                  grade: {
                    // Nota máxima que pode ser alcançada
                    type: Number,
                    default: null,
                  },
                },
              ],
              // required: false,
              default: undefined,
            },
            directedStudies: {
              type: [
                {
                  directedStudyId: mongoose.SchemaTypes.ObjectId,
                  title: {
                    type: String,
                    required: true,
                  },
                  description: {
                    type: String,
                    default: '',
                  },
                  files: [
                    {
                      url: {
                        type: String,
                        required: true,
                      },
                      name: {
                        type: String,
                        required: true,
                      },
                      type: {
                        type: String,
                        required: true,
                      },
                    },
                  ],
                  allPostVisibleToStudent: {
                    // Estudante poderam ver mensagens de outros estudantes
                    type: Boolean,
                    required: true,
                  },
                  type: {
                    // Informativo ou avaliativo
                    type: String,
                    required: true,
                  },
                  grade: {
                    // Nota máxima que pode ser alcançada
                    type: Number,
                    default: null,
                  },
                },
              ],
              // required: false,
              default: undefined,
            },
            coursewareType: {
              type: String,
            },
            _coursewares: {
              type: [mongoose.SchemaTypes.ObjectId],
              // required: false
            },
            supportFile: {
              type: String,
            },
            pathknowledges: {
              type: [{
                _id: mongoose.SchemaTypes.ObjectId,
                name: String,
                courseware: {
                  _id: mongoose.SchemaTypes.ObjectId,
                  title: String,
                },
                pieces: [{
                  useGrade: Boolean,
                  grade: Number,
                  name: String,
                  _pieceId: mongoose.SchemaTypes.ObjectId,
                }],
                grade: Number,
                useGrade: Boolean,
                dateStart: Date,
                dateEnd: Date,
              }],
            },
            dependecyConfig: {
              type: {
                useDependecy: Boolean,
                activities: [
                  {
                    type: {
                      type: String,
                      enum: [
                        'regular',
                        'recuperation',
                      ],
                      // required: false
                    },
                    modality: {
                      type: String,
                      enum: [
                        'online',
                        'presential',
                      ],
                      // required: false
                    },
                    maxDuration: {
                      type: Number,
                      // required: false
                    },
                    model: {
                      type: String,
                      enum: [
                        'form',
                        'evaluation',
                        'upload',
                        'participation',
                        'sagah',
                        'forum',
                      ],
                      // required: false
                    },
                    isFinalTest: {
                      type: Boolean,
                      // required: false
                    },
                    chapter: {
                      type: [{
                        _id: false,
                        coursewareId: mongoose.SchemaTypes.ObjectId,
                        coursewareName: String,
                        number: Number,
                      }],
                    },
                    modelMeta: {
                      _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                      },
                      numQuestions: {
                        type: Number,
                        // required: false
                      },
                      enunciation: {
                        type: String,
                        // required: false
                      },
                      ltiUrl: {
                        type: String,
                        // required: false
                      },
                      ltiTitle: {
                        type: String,
                        // required: false
                      },
                    },
                    maxGrade: {
                      type: Number,
                      // required: false,
                      default: 10,
                    },
                  },
                ],
                useRate: Boolean,
                rateAmount: Number,
                period: Number,
              },
            },
          },
        ],
        // required: false
      },
      internships: [
        {
          schedulingSent: {
            type: Boolean,
          },
          shipmentType: {
            type: String,
          },
          shipmentTypeQuantity: {
            type: mongoose.SchemaTypes.Mixed,
          },
          shipmentQuantity: {
            type: Number,
          },
          workload: {
            type: Number,
          },
          helpFile: {
            type: String,
          },
          fileModelsInternshipRequired: {
            type: [{
              url: String,
              name: String,
              typeDocument: String,
            }],
          },
          stepsInternshipRequired: {
            type: [{
              isRequired: Boolean,
              typeInternship: String,
              title: String,
              action: String,
              description: String,
              nextStatus: String,
              reviewBy: String,
              visibleIn: String,
              originalFile: String,
              order: Number,
              internalAnalisy: Boolean,
              typeLimitCorrect: String,
              dateLimit: Date,
              limitCorrect: Number,
              multiFile: Boolean,
            }],
          },
          area: {
            type: [{
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            }],
          },
          tutor: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
          title: String,
        },
      ],
      isReferenceMatrix: {
        type: Boolean,
        default: false,
      },
    },
  },
};

CurriculumMatrix.pre = {
  save: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  insert: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  insertMany: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  findOneAndUpdate: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  findOneAndReplace: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  updateOne: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
  updateMany: [
    CurriculumMatrix.functions.sortDisciplines,
  ],
};

module.exports = CurriculumMatrix;
