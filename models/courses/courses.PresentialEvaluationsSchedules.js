const {SchemaTypes} = require("mongoose");

let PresentialEvaluationsSchedules = {
  database: {
    connection: 'database_piaget',
    collection: 'PresentialEvaluationsSchedules',
    fields: {
      student: {
        name: {
          type: String,
          required: true
        },
        cpf: {
          type: String,
          required: true
        }
      },
      _presentialEvaluationId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      _enrolmentId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      _certifierName: {
        type: String,
        required: true
      },
      _classId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      _groupingId: {
        type: SchemaTypes.ObjectId,
        // required: false
      },
      _disciplineId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      _disciplineName: {
        type: String,
        required: true
      },
      _courseId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      _activityId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      polo: {
        name: {
          type: String,
          required: true
        },
        _poloId: {
          type: SchemaTypes.ObjectId,
          required: true
        },
        _partnerCpf: {
          type: String,
          required: true
        },
        _partnerId: {
          type: SchemaTypes.ObjectId,
          required: true
        }
      },
      date: {
        type: Date,
        required: true
      },
      hour: {
        type: String,
        required: true
      },
      status: {
        type: String,
        // required: false,
        default: 'active'
      },
      history: [{
        action: {
          type: String,
          required: true
        },
        _userId: {
          type: SchemaTypes.ObjectId,
          required: true
        },
        userName: {
          type: String,
          required: true
        }
      }],
      presenceList: {
        type: String,
        // required: false
      },
      scanCorrectionTries: {
        type: Number,
        // required: false,
        default: 0
      },
      humanCorrection: {
        type: Boolean,
        // required: false
      },
      correction: {
        type: SchemaTypes.Mixed,
        // required: false
      }
    },
  }
};

module.exports = PresentialEvaluationsSchedules;
