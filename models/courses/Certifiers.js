const mongoose = require('mongoose');
const modelName = 'Certifiers';
let Certifiers = {
    functions: {
        setNumber: async function(r) {
            let model = null;
            if (this.constructor && ['function'].includes(typeof this.constructor) && this.constructor.modelName && this.constructor.modelName === modelName) {
                model = this.constructor;
            } else if (this && ['function'].includes(typeof this) && this.modelName && this.modelName === modelName) {
                model = this;
            } else if (this && ['object'].includes(typeof this) && this.model && ['function'].includes(typeof this.model) && this.model.modelName && this.model.modelName === modelName) {
                model = this.model;
            }
            if (model) {
                let find;
                let arrObj;
                if (this && ['function', 'object'].includes(typeof this) && 'getQuery' in this && this.getQuery()) {
                    find = this.getQuery();
                    arrObj = await model.find(find);
                } else {
                    arrObj = Array.isArray(r) ? r : [r];
                }
                if (arrObj && Array.isArray(arrObj) && arrObj.length) {
                    for (let i = 0; i < arrObj.length; i++) {
                        if (arrObj[i] && typeof arrObj[i] === 'object' && arrObj[i]._id) {
                            if (!arrObj[i].number) {
                                let number = 1;
                                const lastObj = await model.findOne({number: {$ne: null}}).sort({number: -1});
                                if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                                    if ('number' in lastObj && !isNaN(lastObj.number)) {
                                        number = parseInt(lastObj.number.toString()) + 1;
                                    }
                                }
                                while (1) {
                                    arrObj[i].number = number;
                                    const newObj = await arrObj[i].save({validateBeforeSave: false}).then(c => c).catch(() => null);
                                    if (newObj && typeof newObj === 'object' && newObj._id && newObj.number) {
                                        break;
                                    } else {
                                        number++;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return r;
        }
    },
    database: {
        collection: modelName,
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true,
                maxlength: 150,
                unique: 'certifier_already_exists'
            },
            reasonSocial: {
                type: String
            },
            maintaining: {
                type: {
                    name: {
                        type: String,
                        required: true,
                    },
                    _cnpj: {
                        length: 14,
                        required: true,
                        type: String,
                    },
                    address: {
                        street: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        number: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        complement: {
                            type: String,
                            allowNull: true
                        },
                        zone: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        zip: {
                            type: Number,
                            required: true,
                            allowNull: false
                        },
                        city: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        uf: {
                            type: String,
                            required: true,
                            allowNull: false
                        }
                    }
                },
                required: true,
            },
            number: {
                type: Number,
                allowNull: true,
                default: null
            },
            _cnpj: {
                length: 14,
                required: true,
                type: String,
            },
            logo: {
                url: {
                    type: String,
                    required: true
                },
                brand: {
                    type: String,
                    required: true
                },
                icon: {
                    type: String,
                    required: true
                },
                useInLogin: {
                    type: Boolean,
                    default: true
                }
            },
            nickName: {
                type: String,
            },
            address: {
                street: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                number: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                complement: {
                    type: String,
                    allowNull: true
                },
                zone: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                zip: {
                    type: Number,
                    required: true,
                    allowNull: false
                },
                city: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                uf: {
                    type: String,
                    required: true,
                    allowNull: false
                }
            },
            alias: {
                type: String,
                required: true
            },
            contacts : {
                phone      : {
                    type    : String,
                    required: true
                },
                secondPhone: {
                    type: String
                },
                internshipEmail: {
                    type: String
                }
            },
            isEnabled: {
                type: Boolean,
                default: false
            },
            useRateEnrolment: {
                type: Boolean,
                default: true
            },
            declaration: {
                type: {
                    useDeclarationFree: Boolean,
                    useDeclarationPersonalized: Boolean
                }
            },
            authenticator: {
                type: String
            },
            signature: {
                type: {
                    cpf: {
                        type: String,
                    },
                    ip: {
                        type: String,
                    },
                    location: {
                        type: String,
                    },
                    passphrase: {
                        type: String,
                    },
                    pfx: {
                        type: String,
                    }
                },
            },
            letterhead: {
                type: String,
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
            },
            searchesWord: {
                type: [String]
            },
            link: {
                type: String
            },
            email: {
                type: String
            },
            linkBoleto: {
                type: String
            },
            emec: {
                type: String
            },
            authorizations: {
                type: [{
                    modality: {
                        type: String,
                        enum: ['ead', 'presential'],
                        required: true
                    },
                    authorizationInProcess: {
                        type: Boolean
                    },
                    authorizationType: {
                        type: String
                    },
                    authorizationTypeInMec:  {
                        type: String
                    },
                    number: {
                        type: String
                    },
                    date: {
                        type: Date
                    },
                    publisher: {
                        type: String
                    },
                    datePublication: {
                        type: Date
                    },
                    sessionPublication: {
                        type: String
                    },
                    pagePublication: {
                        type: String
                    },
                    diaryNumber: {
                        type: String
                    },
                    tramitationNumber: {
                        type: String
                    },
                    tramitationType: {
                        type: String
                    },
                    tramitationDateRegister: {
                        type: Date
                    },
                    tramitationDateProtocol: {
                        type: Date
                    }
                }],
                default: [],
            },
        },
        indexes: [
            {
                fields: {
                    name: 'text'
                },
                options: {}
            },
            {
                fields: {
                    number: 1
                },
                options: {
                    unique: 'number_already_exists',
                    name: 'number_1',
                    partialFilterExpression: {
                        number: {
                            $type: "number"
                        }
                    }
                }
            }
        ]
    }
};
Certifiers.database.post = {
    save: [
        Certifiers.functions.setNumber
    ],
    insertMany: [
        Certifiers.functions.setNumber
    ],
    findOneAndUpdate: [
        Certifiers.functions.setNumber
    ],
    findOneAndReplace: [
        Certifiers.functions.setNumber
    ],
    updateOne: [
        Certifiers.functions.setNumber
    ],
    updateMany: [
        Certifiers.functions.setNumber
    ]
};
module.exports = Certifiers;
