const mongoose = require('mongoose');

let VideoLessons = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'VideoLessons',
        fields    : {
            coursewareId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            title       : {
                type    : String,
                unique  : 'Já existe um vídeo com esse título',
                required: true,
                index   : true
            },
            chapter: Number,
            teacher: String,
            content     : {
                type    : String,
                index   : true
            },
            contentPortal: {
                type    : String,
                index   : true
            },
            isActive    : {
                type   : Boolean,
                default: true
            },
            image       : {
                type    : String,
                required: false
            },
            page        : {
                type    : Number,
                required: false
            },
        }
    }
};

module.exports = VideoLessons;
