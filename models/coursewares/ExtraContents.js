const mongoose = require('mongoose');

let ExtraContents = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ExtraContents',
        fields: {
            coursewareId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _aliasTypeExtraContent: {
              type: String,
              required: true
            },
            title: {
                type: String,
                required: true
            },
            archive: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = ExtraContents;
