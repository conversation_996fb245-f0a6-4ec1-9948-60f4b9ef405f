const mongoose = require('mongoose');

let PathKnowledgePieces = {
    database: {
        collection: 'PathKnowledgePieces',
        fields: {
            name: {
                type: String,
                required: true,
                index: true
            },
            description: {
                type: String,
                // required: false,
            },
            linkVideoLesson: {
                type: String,
                // required: false,
            },
            displayType: {
                type: String,
                enum: [
                    'text',
                    'live',
                    'slide'
                ],
                default: 'text'
            },
            slide: [
                {
                    name: {
                        type: String,
                        // required: false,
                    },
                    text: {
                        type: String,
                        // required: false,
                    },
                    file: {
                        url: {
                            type: String,
                            // required: false,
                        },
                        name: {
                            type: String,
                            // required: false,
                        },
                        type: {
                            type: String,
                            // required: false,
                        },
                    }
                }
            ],
            files: [
                {
                    url: {
                        type: String,
                        // required: false,
                    },
                    name: {
                        type: String,
                        // required: false,
                    },
                    type: {
                        type: String,
                        // required: false,
                    },
                }
            ],
            questions: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId
                    },
                    numberQuestions: {
                        type: Number,
                        required: true,
                        default: 1
                    },
                    chapter: {
                        type: Number,
                        // required: false,
                        allowNull: true,
                        default: null
                    },
                    attempts: {
                        type: Number,
                        required: true,
                        default: 1
                    },
                },
                // required: false,
                allowNull: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            useGrade: {
                type: Boolean,
                default: false
            },
            grade: {
                type: Number,
                // required: false
            }
        },
        options: {
            timestamps: true
        },
        pre: {},
        post: {},
        indexes: [
            {
                fields: {},
                options: {}
            }
        ]
    }
};
module.exports = PathKnowledgePieces;
