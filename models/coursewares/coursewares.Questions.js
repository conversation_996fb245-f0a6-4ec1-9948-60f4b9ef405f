const mongoose = require('mongoose');

let Questions = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Questions',
        fields    : {
            coursewareId : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            enunciation  : {
                type    : String,
                required: true
            },
            alternatives : [
                {
                    content : {
                        type    : String,
                        required: true
                    },
                    correct : {
                        type   : Boolean,
                        default: false
                    },
                    isActive: {
                        type   : Boolean,
                        default: true
                    }
                }
            ],
            type         : {
                type    : [String],
                required: true
            },
            level        : {
                type    : String,
                required: true,
                enum    : [
                    'easy',
                    'normal',
                    'difficult'
                ]
            },
            // capitulo do livro
            chapter: {
                type: Number,
                // required: false,
            },
            pageApostille: {
                type    : Number,
                //// required: false,
                default: 0,
            },
            resolution   : String,
            isActive     : {
                type   : Boolean,
                default: true
            },
            //informação adicionada para evitar duplicidade
             groupQuestionsId : {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            // Fala se a questão é do tipo de selecionar ou texto
            typeOfAnswer: {
                type: String,
                enum: [
                    'text',
                    'select'
                ],
                default: 'select'
            },
        }
    }
};

module.exports = Questions;

