
let Authors = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'Authors',
    fields: {
      isActive: {
        type: Boolean,
        default: true,
      },
      name: {
        type    : String,
        // unique  : 'J<PERSON> existe um autor com esse nome',
        required: true,
        index   : true,
      },
      cpf: {
        type: String,
        required: true,
        index: true,
        unique: 'Já existe um autor com esse cpf',
      },
      rg: {
        type: String,
        required: true,
      },
      email: {
        type: String,
        required: true,
      },
      phone: {
        type: String,
        required: true,
      },
      company: {
        type: {
          cnpj: {
            type: String,
            required: true,
          },
          socialName: {
            type: String,
            required: true,
          },
        },
        // required: false,
      },
      address: {
        street: {
          type: String,
          required: true,
        },
        number: {
          type: String,
          required: true,
        },
        complement: {
          type: String,
          // required: false,
        },
        zone: {
          type: String,
          required: true,
        },
        zip: {
          type: String,
          required: true,
        },
        city: {
          type: String,
          required: true,
        },
        uf: {
          type: String,
          required: true,
        },
      },
      observations: {
        type: String,
        // required: false,
      },
      titulations: {
        type: [{
          titulation: {
            type: String,
            required: true,
          },
          project: {
            type: String,
            required: true,
          },
        }],
        required: true,
      },
      formations: {
        type: [{
          type: {
            type: String,
            required: true,
          },
          formation: {
            type: String,
            required: true,
          },
        }],
        required: true,
      },
      attachments: {
        type: [{
          file: {
            type: String,
            required: true,
          },
          description: {
            type: String,
            required: true,
          },
          fileType: {
            type: String,
            required: true,
          }
        }],
        // required: false,
      },
    }
  }
};

module.exports = Authors;
