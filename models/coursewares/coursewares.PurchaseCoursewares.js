const mongoose = require('mongoose');

let PurchaseCourseware = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PurchaseCourseware',
        fields: {
            _cpf: {
                type: String,
                required: true
            },
            _chargeId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _coursewares: {
                type: [
                    {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required : true,
                            allowNull: false
                        },
                        title: {
                            type: String,
                            required : true,
                            allowNull: false
                        },
                        numPages: {
                            type: Number,
                            required : true,
                            allowNull: false
                        },
                        amount: {
                            type: Number,
                            required : true,
                            allowNull: false
                        }
                    }
                ],
                required: true
            },
            _certifier: {
                type: String,
                required: true
            },
            orderNumber: {
                type    : String,
                required: true
            },
            shippingType: {
                type    : String,
                // required: false
            },
            shippingAmount: {
                type    : Number,
                // required: false
            },
            trackingCode: {
                type    : String,
                // required: false
            },
            address              : {
                street    : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                number    : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                complement: {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                zone      : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                zip       : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                city      : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                uf        : {
                    type     : String,
                    required : true,
                    allowNull: false
                }
            },
            status          : {
                type    : String,
                required: true,
                enum    : [
                    'waiting',
                    'waiting_confection',
                    'made',
                    'sent_student',
                    'canceled',
                    'returned'
                ],
                default : 'waiting'
            },
            history: [
                {
                    status          : {
                        type    : String,
                        enum    : [
                            'waiting',
                            'waiting_confection',
                            'made',
                            'sent_student',
                            'canceled',
                            'returned'
                        ],
                        default : 'waiting'
                    },
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    },
                    launchedAt: {
                        type: Date,
                        required: true
                    },
                    description: {
                        type: String
                    }
                }
            ]
        }
    }
};

module.exports = PurchaseCourseware;
