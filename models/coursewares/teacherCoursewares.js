const mongoose =  require('mongoose');
let TeacherCoursewares = {
    database: {
        collection: 'TeacherCoursewares',
        connection: 'database_piaget',
        fields: {
            title: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            description: {
                type: String,
                // required: false
            },
            file: [{
                    url: {type: String},
                    type: {type: String},
                    name: {type: String}
            }],
            date: {
                type: Date
            },
            userId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            userName: {
                type    : String,
                required: true
            },
            teacherId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            teacherName: {
                type: String,
                required: true
            },
            discipline: {
                type    : {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                    discId: mongoose.SchemaTypes.ObjectId
                }
            },
            classes: {
                type    : {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                }
            },
            group: {
              type    : {
                _id: mongoose.SchemaTypes.ObjectId,
                name: String,
              }
            }
        }
    }
};

module.exports = TeacherCoursewares;
