let ExtraContentsGeneral = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'ExtraContentsGeneral',
    fields: {
      certifiers: {
        type: [String],
        required: true,
      },
      courseTypes: {
        type: [String],
        required: true,
      },
      _aliasTypeExtraContent: {
        type: String,
        required: true
      },
      title: {
        type: String,
        required: true
      },
      archive: {
        type: String,
        required: true
      },
      isActive: {
        type: Boolean,
        default: true
      }
    }
  }
};

module.exports = ExtraContentsGeneral;
