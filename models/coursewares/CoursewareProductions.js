const mongoose = require('mongoose');

let CoursewareProductions = {
	functions: {},
	database: {
		connection: 'database_piaget',
		collection: 'CoursewareProductions',
		fields: {
			status: {
				type: String,
				enum: [
					'in-production',
					'in-verification',
					'rejected',
					'canceled',
					'returned',
					'change-author',
					'awaiting-signature',
					'contract-signed',
					'finished',
				],
				default: 'in_production',
				required: true,
			},
			certifier: {
				type: String,
				// required: true,
			},
			courseType: {
				type: String,
				required: true,
			},
			course: {
				type: {
					_id: {
						type: mongoose.SchemaTypes.ObjectId,
						required: true,
					},
					name: {
						type: String,
						required: true,
					}
				},
				// required: false
			},
			observations: {
				type: String,
				// required: false,
			},
			bookFirstDeliveryDate: {
				type: Date,
				// required: false,
			},
			bookSecondDeliveryDate: {
				type: Date,
				// required: false,
			},
			bookContractDate: {
				type: Date,
				// required: false,
			},
			verificationDate: {
				type: Date,
				// required: false,
			},
			bookPublication: {
				type: Boolean,
				// required: false,
			},
			bookIsbnCode: {
				type: String,
			},
			bookAuthor: {
				type: {
					_id: {
						type: mongoose.SchemaTypes.ObjectId,
						required: true,
					},
					name: {
						type: String,
						required: true,
					}
				},
			},
			bookAuthorTitulation: {
				type: String,
			},
			bookFiles: {
				type: [{
					file: {
						type: String,
						required: true,
					},
					description: {
						type: String,
						required: true,
					},
				}],
				// required: false,
			},
			bookCoursewareFiles: {
				type: [{
					file: {
						type: String,
						required: true,
					},
					title: {
						type: String,
						required: true,
					},
					chapter: {
						type: Number,
						required: true,
					},
				}],
			},

			curationContractDate: {
				type: Date,
				// required: false,
			},
			curationFirstDeliveryDate: {
				type: Date,
				// required: false,
			},
			curationSecondDeliveryDate: {
				type: Date,
				// required: false,
			},
			curationPublication: {
				type: Boolean,
				// required: false,
			},
			curationIsbnCode: {
				type: String,
				// required: false,
			},
			curationVersion: {
				type: String,
				// required: false,
			},
			curationFiles: {
				type: [{
					file: {
						type: String,
						required: true,
					},
					description: {
						type: String,
						required: true,
					},
				}],
				// required: false,
			},
			curationCoursewareFiles: {
				type: [{
					file: {
						type: String,
						required: true,
					},
					title: {
						type: String,
						required: true,
					},
					chapter: {
						type: Number,
						required: true,
					},
				}],
			},
			curationAuthor: {
				type: {
					_id: {
						type: mongoose.SchemaTypes.ObjectId,
						required: true,
					},
					name: {
						type: String,
						required: true,
					}
				},
				// required: false
			},

			supplementaryMaterialAuthor: {
				type: {
					_id: {
						type: mongoose.SchemaTypes.ObjectId,
						required: true,
					},
					name: {
						type: String,
						required: true,
					}
				},
				required: false
			},
			supplementaryMaterialContractDate: {
				type: Date,
				// required: false,
			},
			supplementaryMaterialFirstDeliveryDate: {
				type: Date,
				// required: false,
			},
			supplementaryMaterialSecondDeliveryDate: {
				type: Date,
				// required: false,
			},
			supplementaryMaterialFiles: {
				type: [{
					file: {
						type: String,
						required: true,
					},
					description: {
						type: String,
						required: true,
					},
				}],
			},
			supplementaryMaterialCoursewareFiles: {
				type: [{
					type: {
						type: String,
						enum: ['forum', 'directed_studies', 'evaluative_activity', 'video', 'others'],
						required: true,
					},
					otherType: {
						type: String,
						required: false,
					},
					contents: [{
						content: {
							type: String,
							required: true,
						},
						title: {
							type: String,
							required: true,
						},
						chapter: {
							type: Number,
							required: true,
						},
					}],
				}],
			},
			registerDate: {
				type: Date,
				// required: false,
			},
			bookPrice: {
				type: Number,
				// required: false,
			},
			materialInfo:{
				type: {
					category: {
						type: String,
						enum: [
							"book", 
							"supplementaryMaterial",
						],
						required: true,
					},
					materialType: {
						type: String,
						required: true,
					}
				},
				required: false
			},
			discipline:{
				type: {
					_id: {
						type: mongoose.SchemaTypes.ObjectId,
						required: true,
					},
					name: {
						type: String,
						required: true,
					}
				},
			},
		}
	}
};

module.exports = CoursewareProductions;
