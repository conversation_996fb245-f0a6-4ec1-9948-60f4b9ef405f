let ExtraCoursewares = {
    database: {
        collection: 'ExtraCoursewares',
        fields: {
            title: {
                type: String,
                required: true,
                index: true
            },
            alias: {
                type: String,
                required: true,
                index: true
            },
            type: {
                type: String,
                // required: false,
                default: 'pdf'
            },
            file: {
                type: String,
                // required: false,
                allowNull: true,
                default: null,
                index: true
            },
            content: {
                type: String,
                allowNull: true,
                default: null,
                // required: false
            },
            description: {
                type: String,
                allowNull: true,
                default: null,
                // required: false
            },
            contentPortal: {
                type    : String,
                // required: false,
                index   : true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            chapter: {
                type: Number
            },
            page: {
                type: Number
            },
            teacher: {
                type: String
            }
        }
    }
};
module.exports = ExtraCoursewares;
