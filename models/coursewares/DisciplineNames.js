const mongoose = require('mongoose');

const forumDataSchema = {
  forumId: mongoose.SchemaTypes.ObjectId,
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: '',
  },
  files: [
    {
      url: {
        type: String,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
    },
  ],
  allPostVisibleToStudent: {
    // Estudante poderam ver mensagens de outros estudantes
    type: Boolean,
    required: true,
  },
  type: {
    // Informativo ou avaliativo
    type: String,
    required: true,
  },
  grade: {
    // Nota máxima que pode ser alcançada
    type: Number,
    default: null,
  },
};

const directedStudiesDataSchema = {
  directedStudyId: mongoose.SchemaTypes.ObjectId,
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: '',
  },
  files: [
    {
      url: {
        type: String,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
    },
  ],
  allPostVisibleToStudent: {
    // Estudante poderam ver mensagens de outros estudantes
    type: Boolean,
    required: true,
  },
  type: {
    // Informativo ou avaliativo
    type: String,
    required: true,
  },
  grade: {
    // Nota máxima que pode ser alcançada
    type: Number,
    default: null,
  },
};

const generalContentsSchema = {
  courseTypes: {
    type: [String],
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  skills: {
    type: String,
  },
  habilities: {
    type: String,
  },
  gradeSystem: {
    type: String,
  },
  methodology: {
    type: String,
  },
  teacherOrientations: {
    type: String,
  },
  mainThemes: {
    type: String,
  },
  extraThemes: {
    type: String,
  },
  programContent: {
    type: String,
  },
};

const extraCoursewaresSchema = {
  _id: mongoose.SchemaTypes.ObjectId,
  title: {
    type: String,
    required: true,
    index: true,
  },
  alias: {
    type: String,
    required: true,
    index: true,
  },
  type: {
    type: String,
    // required: false,
    default: 'pdf',
  },
  file: {
    type: String,
    // required: false,
    allowNull: true,
    default: null,
    index: true,
  },
  content: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  description: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  contentPortal: {
    type: String,
    // required: false,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  chapter: {
    type: Number,
  },
  page: {
    type: Number,
  },
  teacher: {
    type: String,
  },
};

const disciplineNamesSchema = {
  name: {
    type: String,
    required: true,
    maxlength: 255,
    // unique: 'discipline_name_already_exists'
  },
  sagahDiscipline: {
    type: String,
    // required: false
  },
  _teacherName: {
    type: String,
    maxlength: 255,
  },
  _teacherId: {
    type: mongoose.SchemaTypes.ObjectId,
  },
  _tutorName: {
    type: String,
    required: false,
    maxlength: 255,
  },
  _tutorId: {
    type: mongoose.SchemaTypes.ObjectId,
    // required: false
  },
  isEnabled: {
    type: Boolean,
    // required: false,
    default: false,
  },
  objective: {
    type: String,
    // required: false,
    default: null,
  },
  program: {
    type: String,
    // required: false,
    default: null,
  },
  workload: {
    type: Number,
    // required: false
  },
  tags: {
    type: Array,
    // required: false
  },
  activities: [
    {
      type: {
        type: String,
        enum: ['regular', 'recuperation'],
        required: true,
      },
      modality: {
        type: String,
        enum: ['online', 'presential'],
        required: true,
      },
      maxDuration: {
        type: Number,
        // required: false
      },
      maxGrade: {
        type: Number,
      },
      chapter: {
        type: [
          {
            _id: false,
            coursewareId: mongoose.SchemaTypes.ObjectId,
            coursewareName: String,
            number: Number,
          },
        ],
      },
      model: {
        type: String,
        enum: ['form', 'evaluation', 'upload', 'participation', 'sagah'],
        required: true,
      },
      isFinalTest: {
        type: Boolean,
        // required: false
      },
      modelMeta: {
        numQuestions: {
          type: Number,
          // required: false
        },
        enunciation: {
          type: String,
          // required: false
        },
        ltiUrl: {
          type: String,
          // required: false
        },
        ltiTitle: {
          type: String,
          // required: false
        },
      },
      courseTypes: {
        type: [String],
        required: true,
      },
    },
  ],
  _forums: {
    type: [
      {
        forumData: forumDataSchema,
        courseTypes: {
          type: [String],
          required: true,
        },
        modalities: {
          type: [String],
          required: true,
        },
      },
    ],
    default: undefined,
  },
  _directedStudies: {
    type: [
      {
        directedStudiesData: directedStudiesDataSchema,
        courseTypes: {
          type: [String],
          required: true,
        },
        modalities: {
          type: [String],
          required: true,
        },
      },
    ],
    default: undefined,
  },
  coursewareType: {
    type: String,
  },
  _coursewares: {
    type: [mongoose.SchemaTypes.ObjectId],
    // required: false
  },
  courseTypes: {
    type: [String],
    required: true,
  },
  period: {
    type: Number,
  },
  chapter: {
    type: [
      {
        _id: false,
        coursewareId: mongoose.SchemaTypes.ObjectId,
        coursewareName: String,
        number: Number,
      },
    ],
  },
  generalContents: {
    type: [generalContentsSchema],
    required: true,
  },
  extraCoursewares: {
    type: [extraCoursewaresSchema],
    default: [],
  },
  theoreticalWorkload: {
    type: Number,
    // required: false
  },
  practicalWorkload: {
    type: Number,
    // required: false
  },
  extensionWorkload: {
    type: Number,
    // required: false
  },
};

const DisciplineNames = {
  functions: {
    sortCourseTypes: function (next) {
      console.log('rodou???');
      this.courseTypes.sort((a, b) => a.localeCompare(b));

      next();
    },
  },
  database: {
    collection: 'DisciplineNames',
    connection: 'database_piaget',
    fields: disciplineNamesSchema,
    indexes: [
      {
        fields: {
          name: 1,
          courseTypes: 1,
        },
        options: {
          unique: 'discipline_name_already_exists',
          name: 'dup_disc_name',
        },
      },
      {
        fields: {
          name: 'text',
        },
        options: {},
      },
    ],
    pre: {},
  },
};

DisciplineNames.database.pre = {
  save: [DisciplineNames.functions.sortCourseTypes],
};

module.exports = DisciplineNames;
module.exports.disciplineNamesSchema = disciplineNamesSchema;
