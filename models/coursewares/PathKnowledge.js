const mongoose = require('mongoose');

let PathKnowledge = {
    database: {
        collection: 'PathKnowledge',
        fields: {
            name: {
                type: String,
                required: true,
            },
            description: {
                type: String,
                required: true,
            },
            pieces: [
                {
                    _pieceId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    name: {
                        type: String,
                        required: true,
                        index: true
                    },
                    description: {
                        type: String,
                        // required: false,
                    },
                    linkVideoLesson: {
                        type: String,
                        // required: false,
                    },
                    displayType: {
                        type: String,
                        enum: [
                            'text',
                            'live',
                            'slide'
                        ],
                        default: 'text'
                    },
                    slide: [
                        {
                            name: {
                                type: String,
                                // required: false,
                            },
                            text: {
                                type: String,
                                // required: false,
                            },
                            file: {
                                url: {
                                    type: String,
                                    // required: false,
                                },
                                name: {
                                    type: String,
                                    // required: false,
                                },
                                type: {
                                    type: String,
                                    // required: false,
                                },
                            },
                            type: {
                                type: String,
                                // required: false
                            }
                        }
                    ],
                    files: [
                        {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                // required: false,
                            },
                            url: {
                                type: String,
                                // required: false,
                            },
                            name: {
                                type: String,
                                // required: false,
                            },
                            type: {
                                type: String,
                                // required: false,
                            },
                        }
                    ],
                    questions: {
                        type: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                allowNull: true,
                                default: null
                            },
                            numberQuestions: {
                                type: Number,
                                required: true,
                                default: 1
                            },
                            chapter: {
                                type: Number,
                                // required: false,
                                allowNull: true,
                                default: null
                            },
                            attempts: {
                                type: Number,
                                required: true,
                                default: 1
                            },
                        },
                        // required: false,
                        allowNull: true
                    },
                    useGrade: {
                        type: Boolean,
                        default: false
                    },
                    grade: {
                        type: Number,
                        // required: false
                    }
                }
            ],
            isActive: {
                type: Boolean,
                default: true
            },
            useGrade: {
                type: Boolean,
                default: false
            },
            grade: {
                type: Number,
                // required: false
            }
        },
        options: {
            timestamps: true
        },
        pre: {},
        post: {},
        indexes: [
            {
                fields: {},
                options: {}
            }
        ]
    }
};
module.exports = PathKnowledge;
