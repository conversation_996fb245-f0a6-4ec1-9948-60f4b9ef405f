const mongoose = require('mongoose');

let ReportErrors = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ReportErrors',
        fields    : {
            type       : {
                type    : String,
                required: true,
                enum    : [
                    'form',
                    'upload',
                    'question',
                    'alternative',
                    'courseware',
                    'video'
                ]
            },
            referenceId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            description: {
                type    : String,
                // required: false
            },
            comment: {
                type    : String,
                // required: false
            },
            _cpf : {
                type    : String,
                required: true
            },
            isSolved   : {
                type   : Boolean,
                default: false
            },
            defer   : {
                type   : Boolean,
                default: false
            },
            activityId : {
                type   : mongoose.SchemaTypes.ObjectId
            },
            enrolmentId : {
                type   : mongoose.SchemaTypes.ObjectId
            },
            rejoinderOf : {
                type   : mongoose.SchemaTypes.ObjectId
            }
        }
    }
};

module.exports = ReportErrors;
