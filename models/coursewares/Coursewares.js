const mongoose =  require('mongoose');
let Coursewares = {
    database: {
        collection: 'Coursewares',
        fields: {
            title: {
                type: String,
                required: true,
                index: true
            },
            ementaTitle: {
                type: String,
            },
            alias: {
                type: String,
                required: true,
                index: true
            },
            type: {
                type: String,
                // required: false,
                default: 'internal'
            },
            externalId: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            sagahDiscipline: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            authorId: {
                type: [mongoose.SchemaTypes.ObjectId],
                // required: false,
                allowNull: true,
                default: null
            },
            categoryId: {
                type: [mongoose.SchemaTypes.ObjectId],
                allowNull: true,
                default: null
            },
            archive: {
                type: String,
                unique: 'Já existe um material com esse arquivo',
                // required: false,
                allowNull: true,
                default: null,
                index: true
            },
            content: {
                type: String,
                allowNull: true,
                default: null,
                // required: false
            },
            description: {
                type: String,
                allowNull: true,
                default: null,
                // required: false
            },
            version: {
                type: Number,
                allowNull: true,
                default: null,
                // required: false
            },
            numPages: {
                type: Number,
                allowNull: true,
                default: null,
                // required: false
            },
            amount: Number,
            isAmount: {
                type: Boolean,
                required: true,
                default: false
            },
            tags: [
                {
                    type: String,
                    required: true
                }
            ],
            mainThemes: [
                {
                    type: String,
                    allowNull: true,
                    default: null,
                    // required: false
                }
            ],
            isActive: {
                type: Boolean,
                default: true
            },
            certifiers: {
                type: [String],
                required: true
            },
            pathKnowledge: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            courseTypes: {
                type: [String],
                required: true,
            },
        },
        indexes: [
            {
                fields : {
                    'title': 'text',
                    'description': 'text',
                    'tags': 'text',
                },
                options: {}
            },
            {
                fields : {
                    'title': 1,
                    'version': 1,
                },
                options: {
                    unique: true
                }
            }
        ]
    }
};
module.exports = Coursewares;
