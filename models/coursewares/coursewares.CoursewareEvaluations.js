const mongoose = require('mongoose');

let CoursewareEvaluations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'CoursewareEvaluations',
        fields: {
            questionsAmount: {
                type: Number,
                required: true
            },
            coursewares: {
                type: [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            questions: [
                {
                    _id: false,
                    _questionId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    alternatives: {
                        type: [mongoose.SchemaTypes.ObjectId],
                        required: true
                    },
                    _correct: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _response: {
                        type: mongoose.SchemaTypes.ObjectId,
                    }
                }
            ],
            metadata: {
                course: {
                    name: {
                        type: String,
                        required: true
                    }
                },
                discipline: {
                    name: {
                        type: String,
                        required: true
                    },
                    _disciplineId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    }
                },
                activity: {
                    name: {
                        type: String,
                        required: true
                    },
                    _activityId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    value: {
                        type: Number,
                        required: true
                    },
                    maxDuration: {
                        type: Number,
                        required: true
                    }
                },
                certifier: {
                    name: {
                        type: String,
                        required: true
                    }
                },
                student: {
                    name: {
                        type: String,
                        required: true
                    },
                    cpf: {
                        type: String,
                        required: true
                    }
                },
                enrolment: {
                    _enrolmentId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    }
                },
                questionsWithEnunciation: [
                    {
                        _id: false,
                        enunciation: String,
                        _questionId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        alternatives: [{
                            _id: mongoose.SchemaTypes.ObjectId,
                            enunciation: String
                        }],
                        _correct: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        _response: {
                            type: mongoose.SchemaTypes.ObjectId,
                        }
                    }
                ],
            }
        }
    }
};

module.exports = CoursewareEvaluations;
