const mongoose = require('mongoose');

let PathKnowledgeEnrolments = {
    database: {
        collection: 'PathKnowledgeEnrolments',
        connection: 'database_piaget',
        fields: {
            pathKnowledge: {
                // Id do caminho
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                useGrade: Boolean,
                grade: Number
            },
            enrolment: {
                _id: { // id da matricula
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false
                },
                name: {
                    type: String,
                    // required: false
                },
                courseName: {// nome do curso
                    type: String,
                    // required: false
                }
            },

            discipline: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false
                },
                name: {
                    type: String,
                    // required: false
                },
                pieces: [
                    {
                        pieceId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        type: {
                            type: String,
                            required: true
                        },
                        slidesViewed: {
                            type: [mongoose.SchemaTypes.ObjectId],
                            // required: false
                        },
                        finishedStep: { // se ja visualizou
                            type: <PERSON><PERSON><PERSON>,
                            required: true
                        },
                        name: {
                            type: String,
                            required: true
                        },
                        attempts: {
                            type: Number,
                            // required: false,
                            allowNull: true,
                            default: null
                        },
                        rightAnswers: {
                            type: Number,
                            default: 0
                        },
                        finishedExercise: {
                            type: Boolean,
                            // required: false
                        },
                        answers: [
                            {
                                groupQuestionsId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false,
                                    allowNull: true,
                                    default: null
                                },
                                questionsId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    required: true
                                },
                                questionEnunciation: {
                                    type: String,
                                    required: true
                                },
                                answer: {
                                    type: String,
                                    required: true
                                },
                                alternativeId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                },
                                alternativeCorrectId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                },
                                //Se acertou ou nÃ£o
                                correct: {
                                    type: Boolean,
                                    default: false
                                },
                                response: {
                                    type: String,
                                    // required: false
                                }
                            }
                        ],
                        //Salvar a ultima porcetagem de acertos
                        percentageCorrectAnswers: {
                            type: Number,
                            default: 0
                        },
                        useGrade: Boolean,
                        grade: Number,
                        studentGrade: Number
                    }
                ]
            }
        },
        options: {
            timestamps: true
        },
        pre: {},
        post: {},
        indexes: [
            {
                fields: {
                    'pathKnowledge._id': 1,
                    'discipline._id': 1,
                    'discipline.name': 1,
                    'enrolment._id': 1
                },
                options: {}
            }
        ]
    }
};
module.exports = PathKnowledgeEnrolments;
