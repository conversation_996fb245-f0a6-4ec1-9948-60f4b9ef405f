const mongoose = require('mongoose');

let PracticeExam = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PracticeExam',
        fields    : {
            _enrolmentId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            numQuestions: {
                type    : Number,
                required: true
            },
            coursewares    : {
                type    : [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            level: {
                type    : String,
                required: true,
                enum    : [
                    'easy',
                    'normal',
                    'difficult'
                ]
            },
            status: {
                type    : String,
                required: true,
                enum    : [
                    'unfinished',
                    'finished'
                ],
                default : 'unfinished'
            },
            questions      : [
                {
                    _id         : false,
                    _questionId : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    enunciation : {
                        type    : String,
                        required: true
                    },
                    alternatives: [
                        {
                            _id : false,
                            _alternativeId: {
                                type    : mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            content : {
                                type    : String,
                                required: true
                            },
                            correct : {
                                type    : Boolean,
                                required: true
                            }
                        }
                    ]
                }
            ],
            answers         : [
                {
                    _id: false,
                    _questionId: {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _alternativeSelectedId: {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    }
                }
            ],
            result: {
                type    : Number,
                // required: false
            },
            timeSpent   : {
                type    : Number,
                // required: false,
                default : 0
            }
        }
    }
};

module.exports = PracticeExam;
