const mongoose = require('mongoose');

let DisciplineSeparates = {
    functions: {},
    database: {
        collection: 'DisciplineSeparates',
        fields    : {
            _enrolmentId     : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _gridDisciplineId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            isActive         : {
                type     : Boolean,
                default  : true,
                allowNull: false
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = DisciplineSeparates;
