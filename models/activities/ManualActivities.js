const mongoose = require('mongoose');

let ManualActivities = {
    functions: {},
    database: {
      collection: 'ManualActivities',
      connection: 'database_piaget',
      fields: {
        type: {
          type: String,
          enum: [
            'regular',
            'recuperation'
          ],
          required: true
        },
        modality: {
          type: String,
          enum: [
            'online',
            'presential'
          ],
          required: true
        },
        maxDuration: {
          type: Number,
        },
        model: {
          type: String,
          enum: [
            'form',
            'evaluation',
            'upload',
            'participation',
            'sagah'
          ],
          required: true
        },
        isFinalTest: {
          type: Boolean,
          required: true
        },
        modelMeta: {
          _id: false,
          numQuestions: {
            type: Number
          },
          enunciation: {
            type: String,
          },
          ltiUrl: {
            type: String,
          },
          ltiTitle: {
            type: String,
          }
        },
        attempts: {
          type: Number,
          default: 0
        },
        date: {
          type: Date,
          default: null
        },
        timeSpent: {
          type: Number,
          default: 0
        },
        evaluation: {
          _coursewareEvaluationId: {
            type: mongoose.SchemaTypes.ObjectId,
          },
          questions: [
            {
              _id: false,
              _questionId: {
                type: mongoose.SchemaTypes.ObjectId,
              },
              _response: {
                type: mongoose.SchemaTypes.ObjectId,
              },
              _correct: {
                type: mongoose.SchemaTypes.ObjectId,
              }
            }
          ]
        },
        upload: {
          archive: {
            type: String,
            default: null
          },
          requestNewRevision: {
            type: Boolean,
            default: true
          },
          revisions: [
            {
              _id: false,
              _userName: String,
              _userId: mongoose.SchemaTypes.ObjectId,
              archive: {
                type: String,
                required: true
              },
              date: {
                type: Date,
                required: true
              }
            }
          ]
        },
        class: {
          type: {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String
          }
        },
        disciplineId: {
          type: mongoose.SchemaTypes.ObjectId,
          required: true
        },
        history: [
          {
            _userName: {
              type: String,
              required: true
            },
            _userId: {
              type: mongoose.SchemaTypes.ObjectId,
              required: true
            },
            maxGrade: {
              type: Number,
              required: true
            },
            reason: {
              type: String,
              default: null
            },
            launchedAt: {
              type: Date,
              required: true
            }
          }
        ],
        maxGrade: {
          type: Number,
        },
        dateStartActivity: {
          type: Date,
          required: true
        },
        activityType: {
          _id: {
            type: mongoose.SchemaTypes.ObjectId
          },
          title: String,
          selectedActivity: String
        },
        isManual: {
          type: Boolean,
          default: true
        }
      },
      options: {
        timestamps: true
      }
    }
  };

module.exports = ManualActivities;
