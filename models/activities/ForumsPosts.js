const mongoose = require('mongoose');

let ForumsPosts = {
    functions: {},
    database: {
        collection: 'ForumsPosts',
        fields: {
            forum: {
                _id: { // id do forum
                    type: mongoose.SchemaTypes.ObjectId
                },
                title: {// nome do forum
                    type: String
                }
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                index: true
            },
            cpf: {
                type: String,
                index: true
            },

            post: {
                user: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                message: {
                    type: String,
                    required: true
                },
                launchedAt: {
                    type: Date,
                    required: true
                },
                ipSending: {
                    type: String,
                },
                files: [
                    {
                        url: {
                            type: String,
                            required: true
                        },
                        name: {
                            type: String,
                            required: true
                        },
                        type: {
                            type: String,
                            required: true
                        }
                    }
                ],
                reply: [
                    {// Replica em cima da mensagem do aluno
                        user: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            name: {
                                type: String,
                                required: true
                            },
                            type: {
                                type: String,
                                required: true
                            }
                        },
                        message: {
                            type: String,
                            required: true
                        },
                        launchedAt: {
                            type: Date,
                            required: true
                        },
                        ipSending: {
                            type: String,
                        }
                    }
                ]
            },
            grade: {
                type: Number,
                default: null
            },
        },
        options: {
            timestamps: true
        },
        pre: {},
        post: {},
        indexes: [
            {
                fields: {
                    turmaId: 1,
                    discId: 1,
                    _userCorrespondingId: 1,
                    _userCreationId: 1,
                    _enrolmentId: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = ForumsPosts;