const mongoose = require('mongoose');

const Forums = {
  functions: {},
  database: {
    collection: 'Forums',
    fields: {
      title: {
        type: String,
        required: true,
      },
      description: {
        type: String,
      },
      model: {
        type: Boolean,
        default: false,
      },
      files: [
        {
          url: {
            type: String,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
          type: {
            type: String,
            required: true,
          },
        },
      ],
      allPostVisibleToStudent: {
        // Estudante poderam ver mensagens de outros estudantes
        type: Boolean,
        required: true,
      },
      sendArchiveStudent: {
        // Estudante podem enviar documentos
        type: Boolean,
        default: true,
      },
      manyPostsPerStudent: {
        // Mais de Uma Postagem Por Aluno
        type: Boolean,
        default: false,
      },

      type: {
        // informativo ou avaliativo
        type: String,
        enum: ['informativo', 'avaliativo'],
        required: true,
      },
      grade: {
        // Nota mรกxima que pode ser alcanรงada
        type: Number,
        default: null,
      },
      class: {
        _id: {
          // id da turma
          type: mongoose.SchemaTypes.ObjectId,
        },
        name: {
          // nome da turma
          type: String,
        },
      },
      grouping: {
        _id: {
          // id do agrupamento
          type: mongoose.SchemaTypes.ObjectId,
        },
        name: {
          // nome do agrupamento
          type: String,
        },
      },
      discipline: {
        _id: {
          // id da disciplina
          type: mongoose.SchemaTypes.ObjectId,
        },
        name: {
          // nome da disciplina
          type: String,
        },
      },
      isActive: {
        type: Boolean,
        default: true,
      },
      dateStart: {
        // Data inicial para postagem
        type: Date,
      },
      dateEnd: {
        // Data final para postagem
        type: Date,
      },
      forumModelId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      log: [],
      certifiers: [
        {
          _id: mongoose.SchemaTypes.ObjectId,
          name: String,
        },
      ],
      typeNames: [
        {
          _id: mongoose.SchemaTypes.ObjectId,
          name: String,
        },
      ],
      courses: [
        {
          _id: mongoose.SchemaTypes.ObjectId,
          name: String,
        },
      ],
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
    },
    options: {
      timestamps: true,
    },
    indexes: [
      {
        fields: {
          'class._id': 1,
          'discipline._id': 1,
          _userCorrespondingId: 1,
          _userCreationId: 1,
          _enrolmentId: 1,
        },
        options: {},
      },
    ],
  },
};

module.exports = Forums;
