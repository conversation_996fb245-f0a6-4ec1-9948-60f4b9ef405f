const mongoose = require('mongoose');

const DirectedStudiesPosts = {
  functions: {},
  database: {
    collection: 'DirectedStudiesPosts',
    fields: {
      directedStudy: {
        _id: {
          // id do Estudo dirigido
          type: mongoose.SchemaTypes.ObjectId,
        },
        title: {
          // nome do Estudo dirigido
          type: String,
        },
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        index: true,
      },
      cpf: {
        type: String,
        index: true,
      },
      post: {
        user: {
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
        },
        message: {
          type: String,
          required: true,
        },
        launchedAt: {
          type: Date,
          required: true,
        },
        ipSending: {
          type: String,
          required: true,
        },
        files: [
          {
            url: {
              type: String,
              required: true,
            },
            name: {
              type: String,
              required: true,
            },
            type: {
              type: String,
              required: true,
            },
          },
        ],
        reply: [
          {
            // Replica em cima da mensagem do aluno
            user: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
              type: {
                type: String,
                required: true,
              },
            },
            message: {
              type: String,
              required: true,
            },
            launchedAt: {
              type: Date,
              required: true,
            },
            ipSending: {
              type: String,
              required: true,
            },
          },
        ],
      },
      grade: {
        type: Number,
        default: null,
        allow: null,
      },
    },
    options: {
      timestamps: true,
    },
    indexes: [
      {
        fields: {
          turmaId: 1,
          discId: 1,
          _userCorrespondingId: 1,
          _userCreationId: 1,
          _enrolmentId: 1,
        },
        options: {},
      },
    ],
  },
};

module.exports = DirectedStudiesPosts;
