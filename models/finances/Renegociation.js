const mongoose = require('mongoose');
let Renegociation = {
    database: {
        collection: 'Renegociation',
        fields: {
            number: {
                type: Number,
                required: true
            },
            cpf: {
                type: String,
                required: true
            },
            name: {
                type: String,
                required: true
            },
            enrolments: {
                type: [
                    {
                        _enrolmentId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        _certifierName: {
                            type: String,
                            required: true
                        },
                        _typeName: {
                            type: String,
                            required: true
                        },
                        _courseName: {
                            type: String,
                            required: true
                        },
                    }
                ],
                required: true
            },
            _chargeTypeAlias: {
                type: String,
                required: true
            },
            _chargeType: {
                type: String,
                required: true
            },
            status: {
                type: String,
                required: true,
                enum: [
                    'pending',
                    'open',
                    'complete',
                    'canceled',
                    'rejected',
                    'expired'
                ],
                default: 'pending'
                //pending  - Pendente
                //open     - Aberta
                //complete - Concluída
                //canceled - Cancelada
                //rejected - Rejeitada
                //expired  - Expirada
            },
            amount: {
                type: {
                    charges: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    paid: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    waitingPayment: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    fees: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    totalAmount: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    percentDiscount: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    discount: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    realDiscount: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    percentAdditions: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    additions: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    total: {
                        type: Number,
                        required: true,
                        default: 0
                    },
                    buyIn: {
                        type: Number,
                        required: true,
                        default: 0
                    }
                },
                required: true
            },
            charges: {
                type    : [mongoose.SchemaTypes.ObjectId],
                // required: false,
                allowNull: true,
                default: null
            },
            newCharges: {
                type    : [mongoose.SchemaTypes.ObjectId],
                // required: false,
                allowNull: true,
                default: null
            },
            user: {
                type: {
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            checkoutLink: {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                default: null
            },
            dueDate: {
                type: Date,
                required: true
            },
            installments: {
                type: Number,
                required: true,
                default: 1
            },
            paymentMethod: {
                type: String,
                required: true
            },
            buyInPaymentMethod: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            buyInDueDate: {
                type: Date,
                // required: false
            },
            buyInInstallments:{
                type: Number,
                required: true,
                default: 1
            },
            terms: {
                type    : Boolean,
                required: true,
                default: false
            },
            acceptedTerms: {
                type    : Boolean,
                required: true,
                default: false
            },
            termsStorageURL: {
                type     : String,
                required : false,
                allowNull: true,
                default  : null
            },
            signedTermsStorageURL: {
                type     : String,
                required : false,
                allowNull: true,
                default  : null
            },
            history: {
                type: [
                    {
                        _userId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        _userName: {
                            type: String,
                            required: true
                        },
                        status: {
                            type: String,
                            required: true,
                            enum: [
                                'pending',
                                'open',
                                'complete',
                                'canceled',
                                'rejected'
                            ],
                            default: 'pending'
                            //pending  - Pendente
                            //open     - Aberta
                            //complete - Concluída
                            //canceled - Cancelada
                            //rejected - Rejeitada
                        },
                        launchAt: {
                            type: Date,
                            required: true
                        }
                    }
                ],
                // required: false,
                allowNull: true,
                default: null
            },
            observations: {
                type     : String,
                required : false,
                allowNull: true,
                default  : null
            },
            files: {
                type     : [String],
                required : false,
                allowNull: true,
                default  : null
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                allowNull: true,
                default: null
            },
            renegociationDueDate: {
                type: Date,
            }
        }
    }
}
module.exports = Renegociation;
