const mongoose = require('mongoose');

let MonetaryCorrectionLogs = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'MonetaryCorrectionLogs',
    fields: {
      action: {
        type: String,
        required: true
      },
      before: mongoose.SchemaTypes.Mixed,
      after: mongoose.SchemaTypes.Mixed,
      body: mongoose.SchemaTypes.Mixed,
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      _userName: {
        type: String,
        required: true
      },
      _userType: {
        type: String,
        required: true,
        enum: [
          'employer',
          'computer'
        ]
      },
    },
    options: {
      timestamp: true
    }
  }
};

module.exports = MonetaryCorrectionLogs;