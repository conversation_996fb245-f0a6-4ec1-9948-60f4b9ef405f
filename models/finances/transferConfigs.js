const mongoose = require('mongoose');

let TransferConfigs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TransferConfigs',
        fields: {
            minAmount: {
                type: Number,
                default: 0
            },
            percentDiscount: {
                type: Number,
                default: 0
            },
            transferRate: {
                type: Number,
                default: 0
            },
            startDay: Number,
            endDay: Number,
            freeTransfersPerMonth: {
                rateEnrolment: {
                    type: mongoose.SchemaTypes.Mixed,
                    // required: false,
                    default: 0
                },
                monthly: {
                    type: mongoose.SchemaTypes.Mixed,
                    // required: false,
                    default: 0
                }
            },
            isActive: {
                type: Boolean,
                default: true
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = TransferConfigs;
