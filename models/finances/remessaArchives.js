let RemessaArchives = {
    functions: {},
    database: {
        collection: 'RemessaArchives',
        fields: {
            order: {
                type: Number,
                required: true
            },
            serviceType: {
                type: String,
                enum: [
                    'CREDITO_CONTA',
                    'TED'
                ]
            },
            arquivoHeader: {
                empresaInscricaoTipo: Number,
                empresaInscricaoNum: String,
                codigoConvenio: Number,
                parametroTransicao: String,
                ambienteCliente: String,
                agenciaContaCorrenteEmpresa: String,
                agenciaDigVerificador: Number,
                contaNum: Number,
                contaDigVerificador: Number,
                empresaNome: String,
                codigoBanco: String,
                nomeBanco: String,
                arquivoDataGeracao: String,
                arquivoHoraGeracao: String,
                arquivoSequenciaNsa: Number
            },
            arquivoTrailler: {
                qtdeLotes: Number,
                qtdeRegistros: Number
            },
            loteHeader: {
                tipoServico: String,
                formaLancamento: String,
                empresaTipoInsc: Number,
                empresaNumInsc: String,
                codigoConvenio: Number,
                tipoCompromisso: String,
                codigoCompromisso: String,
                parametroTransmissao: String,
                contaAgenciaCorrente: String,
                agenciaDigVerificador: Number,
                contaCorrenteNum: Number,
                contaDigVerificador: Number,
                empresaNome: String,
                enderecoLogradouro: String,
                enderecoNum: String,
                enderecoCompl: String,
                enderecoCidade: String,
                enderecoCep: String,
                enderecoCepCompl: String,
                enderecoEstado: String
            },
            loteTrailler: {
                qtdeRegistrosLote: Number,
                somatoriaValores: Number
            },
            registroDetalheA: [
                {
                    numSeqRegistroLote: Number,
                    movimentoTipo: Number,
                    camaraCompensacao: String,
                    favorecidoCodBanco: String,
                    favorecidoAgencia: String,
                    favorecidoDigAgencia: String,
                    favorecidoNumConta: Number,
                    favorecidoDigVerificador: String,
                    favorecidoNome: String,
                    numDocEmpresa: Number,
                    tipoContaFinalidadeTed: Number,
                    dataVencimento: String,
                    valorLancamento: Number,
                    indicadorFormaParc: Number,
                    periodoDiaVencimento: String,
                    codFinalidadeDoc: String,
                    avisoFavorecido: Number
                }
            ],
            registroDetalheB: [
                {
                    numSeqRegistroLote: Number,
                    favorecidoTipoInscricao: Number,
                    favorecidoNumInscricao: String,
                    favorecidoLogradouro: String,
                    favorecidoNum: Number,
                    favorecidoCompl: String,
                    favorecidoBairro: String,
                    favorecidoCidade: String,
                    favorecidoCep: Number,
                    favorecidoCepCompl: Number,
                    favorecidoEstado: String,
                    dataVencimento: String,
                    valorDocumento: Number
                }
            ],
            archiveRemessa: String,
            status: {
                type: String,
                enum: [
                    'success',
                    'refused'
                ]
            }
        },
        indexes: [
            {
                fields: {
                    order: 1,
                    'arquivoHeader.codigoBanco': 1
                },
                options: {
                    unique: 'Já existe uma remessa com este número'
                }
            }
        ]
    }
};

module.exports = RemessaArchives;
