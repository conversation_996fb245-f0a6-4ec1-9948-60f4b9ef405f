const mongoose = require('mongoose');
const {_getModels} = require('../../services/Utils');

const ChargeOrders = {
  functions: {
    propagateFieldChanges: async function (chargeOrder, next) {
      const models = _getModels.call(this, 'ChargeOrders');
      const {Charges} = models;
      try {

        if (chargeOrder.status !== 'paid') next();

        await Charges.updateMany(
          {
            _id: chargeOrder._charges.map(c => c._chargeId),
          },
          {
            $set: {
              _chargeOrderId: chargeOrder._id,
            },
          },
          {
            session: chargeOrder.$session()?.hasEnded === false ? chargeOrder.$session() : null,
          },
        );

      } catch(err) {
        console.error(err);

        await Charges.updateMany(
          {
            _id: chargeOrder._charges.map(c => c._chargeId),
          },
          {
            $set: {
              _chargeOrderId: chargeOrder._id,
            },
          },
          {
            session: chargeOrder.$session()?.hasEnded === false ? chargeOrder.$session() : null,
          },
        );

      } finally {
        next();
      }
    },
  },
  database: {
    collection: 'ChargeOrders',
    fields: {
      amount: {
        type: Number,
        required: true,
      },
      amountPaid: Number,
      paymentDate: Date,
      _charges: [
        {
          _chargeId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          amount: {
            type: Number,
            required: true,
          },
          finalAmount: {
            type: Number,
            // required: false
          },
          applyDiscount: {
            discount: mongoose.SchemaTypes.Mixed,
            amountWithDiscount: Number,
          },
        },
      ],
      _billingId: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
        index: true,
      },
      payment: {
        type: {
          type: String,
          enum: [
            'boleto',
            'creditCard',
            'debitCard',
            'boleto_crm',
            'creditCard_crm',
            'debitCard_crm',
            'studentCredit',
            'deposit',
            'transfer',
            'pix',
            'lyrapay',
            'transfer_cobrafix',
          ],
          required: true,
        },
        operator: {
          type: String,
          required: true,
        },
        amountPaid: Number,
        metadata: {
          digitableLine: String,
          boleto: {
            caixa: String,
            api: String,
          },
          tid: String,
          txId: String,
          pixQrCode: String,
          payment_id: String,
          discount: mongoose.SchemaTypes.Mixed,
          scholarships: mongoose.SchemaTypes.Mixed,
          invoiceId: String,
          allDiscounts: mongoose.SchemaTypes.Mixed,
        },
      },
      invoiceFor: {
        type: {
          name: {
            type: String,
            required: true,
          },
          cnpj: {
            type: String,
            required: true,
          },
          courseType: {
            type: String,
            // required: false,
            allowNull: true,
          },
        },
        // required: false,
        allowNull: true,
      },
      status: {
        type: String,
        enum: [
          'waiting_payment',
          'paid',
          'canceled',
        ],
        default: 'waiting_payment',
      },
      _renegociationId: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
        allowNull: true,
        default: null,
      },
      metadata: mongoose.SchemaTypes.Mixed,
    },
  },
};

ChargeOrders.database.post = {
  save: [
    ChargeOrders.functions.propagateFieldChanges,
  ],
  findOneAndUpdate: [
    ChargeOrders.functions.propagateFieldChanges,
  ],
};

module.exports = ChargeOrders;
