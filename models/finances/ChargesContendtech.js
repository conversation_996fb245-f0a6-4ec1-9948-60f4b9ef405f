const mongoose = require('mongoose');
let ChargesContendtech = {
    functions: {
        parseEnrolmentId: function (next) {
            if (Array.isArray(this._enrolmentId)) {
                this._enrolmentId = this._enrolmentId.map(id => new mongoose.Types.ObjectId(id));
            } else {
                this._enrolmentId = new mongoose.Types.ObjectId(this._enrolmentId);
            }
            next();
        },

        parseChargesId: function (next) {
            if (Array.isArray(this._chargeId)) {
                this._chargeId = this._chargeId.map(id => new mongoose.Types.ObjectId(id));
            } else {
                this._chargeId = new mongoose.Types.ObjectId(this._chargeId);
            }
            next();
        }
    },
    database: {
        collection: 'ChargesContendtech',
        connection: 'database_piaget',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
                index: true
            },
            _chargeId: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
                index: true
            },
            _cpf: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            status: {
                type: String,
                index: true,
                default: 'waiting_send'
            },
            invoiceId: {
                type: String,
                // required: false
            },
            checkoutLink: {
                type: String,
                // required: false
            },
            chargeDueDate: {
                type: Date,
                // required: false,
                index: true
            },
            chargeAmount: {
                type: Number,
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};
ChargesContendtech.database.pre = {
   save: [
        ChargesContendtech.functions.parseEnrolmentId,
        ChargesContendtech.functions.parseChargesId,
    ],
   create: [
        ChargesContendtech.functions.parseEnrolmentId,
        ChargesContendtech.functions.parseChargesId,
    ],
   findOneAndUpdate: [
        ChargesContendtech.functions.parseEnrolmentId,
        ChargesContendtech.functions.parseChargesId,
   ],
   update: [
        ChargesContendtech.functions.parseEnrolmentId,
        ChargesContendtech.functions.parseChargesId,
    ],
};
module.exports = ChargesContendtech;
