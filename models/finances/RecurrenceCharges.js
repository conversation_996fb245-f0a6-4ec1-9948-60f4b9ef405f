const mongoose = require('mongoose');
let RecurrenceCharges = {
    database: {
        collection: 'RecurrenceCharges',
        fields: {
            cpf: {
                type: String,
                required: true,
                index: true
            },
            name: {
                type: String,
                required: true,
                index: true
            },
            amount: {
                type: Number,
                required: true
            },
            isTaxFreeBalance: {
                type: Boolean,
                default: false,
                index: true
            },
            dayDebt: {
                type: Number,
                required: true,
                index: true
            },
            description: {
                type: String,
                required: true
            },
            archive: String,
            isActive: {
                type: Boolean,
                default: true,
                index: true
            },
            history: [
                {
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    },
                    _userType: {
                        type: String,
                        required: true,
                        enum: [
                            'employer',
                            'computer'
                        ]
                    },
                    description: {
                        type: String
                    },
                    metadata: mongoose.SchemaTypes.Mixed,
                    launchedAt: {
                        type: Date,
                        default: new Date()
                    }
                }
            ],
            _walletId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            installment:{
                type: Number,
                required: true,
                index: true
            }
        },
        options: {
            timestamp: true
        }
    }
}
module.exports = RecurrenceCharges;
