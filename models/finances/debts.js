const mongoose = require('mongoose');

let Debts = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Debts',
        fields: {
            cpf: {
                type: String,
                required: true,
                index: true
            },
            name: {
                type: String,
                required: true,
                index: true
            },
            amount: {
                type: Number,
                required: true
            },
            isTaxFreeBalance: {
                type: Boolean,
                default: false
            },
            installment: {
                type: Number,
                default: 1
            },
            interest: {
                type: Number,
                default: 0
            },
            description: {
                type: String,
                required: true
            },
            dateStart: {
                type: Date,
                required: true
            },
            archive: String,
            status: {
                type: String,
                enum: [
                    'released',
                    'canceled'
                ],
                default: 'released'
            },
            history: [
                {
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    },
                    _userType: {
                        type: String,
                        required: true,
                        enum: [
                            'employer',
                            'computer'
                        ]
                    },
                    description: {
                        type: String
                    },
                    status: {
                        type: String,
                        enum: [
                            'released',
                            'canceled'
                        ],
                        required: true
                    },
                    launchedAt: {
                        type: Date,
                        required: true
                    }
                }
            ]
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = Debts;
