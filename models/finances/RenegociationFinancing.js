
const mongoose = require('mongoose');

let RenegociationFinancing = {
  database: {
    collection: 'RenegociationFinancing',
    fields: {
      status: {
        type: String,
        required: true,
        enum: [
          'open',
          'complete',
          'canceled',
        ],
        default: 'open'
      },
      student: {
        type: {
          _id: false,
          cpf: {
            type: String,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
        },
        required: true,
      },
      enrolments: {
        type: [{
          _id: false,
          _enrolmentId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _certifierName: {
            type: String,
            required: true
          },
          _typeName: {
            type: String,
            required: true
          },
          _courseName: {
            type: String,
            required: true
          },
        }],
        required: true
      },
      originalCharges: {
        type: [mongoose.SchemaTypes.ObjectId],
        required: true,
      },
      installmentsConfig: {
        type: [{
          _id: false,
          _chargeId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _chargeTypeAlias: {
            type: String,
            enum: [
              'fies-repasse',
              'fies-coparticipacao',
              'antecipacao-semestral',
            ],
            required: true
          },
          installment: {
            type: Number,
            required: true
          },
          amount: {
            type: Number,
            required: true
          },
          dueDate: {
            type: Date,
            required: true
          }
        }],
        required: true,
      },
      fees: {
        type: Number,
        required: true
      },
      percentDiscount: {
        type: Number,
        required: true
      },
      discount: {
        type: Number,
        required: true
      },
      percentAdditions: {
        type: Number,
        required: true
      },
      additions: {
        type: Number,
        required: true
      },
      installments: {
        type: Number,
        required: true
      },
      dueDateStart: {
        type: Date,
        required: true
      },
      history: {
        type: [{
          _id: false,
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _userName: {
            type: String,
            required: true
          },
          status: {
            type: String,
            required: true,
          },
          launchAt: {
            type: Date,
            required: true
          },
          observation: {
            type: String,
            required: false,
          }
        }],
      }
    }
  }
};

module.exports = RenegociationFinancing;
