const mongoose = require('mongoose');
const {SchemaTypes} = require("mongoose");

let Recurrences = {
    database: {
        collection: 'Recurrences',
        connection: 'database_piaget',
        fields: {
            cpf: {
              type: String,
              index: true,
              required: false
            },
            _studentId: {
              type: SchemaTypes.ObjectId,
              index: true,
              required: function () {
                return !this.cpf;
              }
            },
            _cardTokenId: {
                type: mongoose.Types.ObjectId,
                required: true
            },
            _enrolmentId: {
                type: mongoose.Types.ObjectId,
                required: true,
                unique: 'already_in_recurrence'
            },
            isActive: {
                type: Boolean,
                // required: false,
                default: true
            },
            nextAt: {
                type: Date,
                // required: false,
                default: null
            },
            history: {
                type: [
                    {
                        _id: false,
                        occurrence: {
                            type: String,
                            required: true
                        },
                        date: {
                            type: Date,
                            // required: false,
                            default: new Date()
                        },
                        message: {
                            type: String,
                            required: true
                        },
                        metadata: mongoose.SchemaTypes.Mixed
                    }
                ],
                // required: false,
                default: [
                    {
                        occurrence: 'created',
                        message: 'Recorrência criada com sucesso',
                        date: new Date()
                    }
                ]
            },
            lastOccurrence: {
                type: {
                    occurrence: {
                        type: String,
                        required: true
                    },
                    date: {
                        type: Date,
                        // required: false,
                        default: new Date()
                    },
                    message: {
                        type: String,
                        required: true
                    },
                    metadata: mongoose.SchemaTypes.Mixed
                },
                // required: false,
                default: {
                    occurrence: 'created',
                    message: 'Recorrência criada com sucesso',
                    date: new Date()
                }
            },
            inQueue: {
                type: Boolean,
                // required: false,
                default: false
            },
            chargesQueue: {
                type: [
                    {
                        _id: false,
                        _chargeId: {
                            type: mongoose.Types.ObjectId,
                            required: true
                        },
                        tries: {
                            type: Number,
                            // required: false,
                            default: 0
                        },
                        lastTryAt: Date
                    }
                ],
                // required: false,
                default: []
            },
            operator: {
                type: String,
                default: 'cielo'
            }
        }
    }
};
module.exports = Recurrences;
