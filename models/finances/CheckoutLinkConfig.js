const mongoose = require('mongoose');
let CheckoutLinkConfig = {
  functions: {
    parsePaymentMethods: function (next) {
      this.paymentType =
        this.paymentType
          .map(paymentType => paymentType === 'twoCards' ? '2cards' : paymentType);

      next();
    },
    parseEnrolmentId: function (next) {
      try{
        if (Array.isArray(this._enrolmentId)) {
          this._enrolmentId = this._enrolmentId.map(id => new mongoose.Types.ObjectId(id));
        } else {
          this._enrolmentId = new mongoose.Types.ObjectId(this._enrolmentId);
        }
        next();
      } catch (e) {
        console.error(e);
        next(e);
      }
    },
  },
  database: {
    collection: 'CheckoutLinkConfig',
    connection: 'database_piaget',
    fields: {
      _enrolmentId: {
        type: mongoose.SchemaTypes.Mixed,
        required: true
      },
      charges: {
        type: [mongoose.SchemaTypes.ObjectId],
        required: true
      },
      paymentType: {
        type: [
          {
            type: String,
            enum: [
              '2cards',
              'twoCards',
              'card',
              'boleto',
              'pix'
            ]
          }
        ],
        required: true
      },
      isInterestFree: {
        type: Boolean,
        default: false
      },
      _renegociationId: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
        allowNull: true,
        default: null
      },
      metadata: mongoose.SchemaTypes.Mixed
    }
  }
};

CheckoutLinkConfig.database.pre = {
  save: [
    CheckoutLinkConfig.functions.parsePaymentMethods,
    CheckoutLinkConfig.functions.parseEnrolmentId,
  ],
};

module.exports = CheckoutLinkConfig
