const {SchemaTypes, isValidObjectId, Types } = require("mongoose");

let Wallets = {
    database: {
        collection: 'Wallets',
        fields: {
            name: {
                type: String,
                required: true
            },
            cpf: {
                type: String,
                required: false,
                index: true
            },
            _userId: {
              type: SchemaTypes.ObjectId,
              index: true,
              required: function () {
                return !this.cpf;
              }
            },
            userType: {
                type: String,
                required: true,
                enum: [
                    'student',
                    'partner',
                    'teacher',
                    'employer'
                ]
            },
            balance: {
                type: Number,
                default: 0
            },
            taxFreeBalance: {
                type: Number,
                default: 0
            },
            advancedBalance: {
                type: Number,
                default: 0
            },
            cnpjNF: {
                type: String,
                // required: false,
                allowNull: true
            },
            nameNF: {
                type: String,
                // required: false,
                allowNull: true
            },
            address: {
                type: {
                    street: {
                        type: String,
                        required: true
                    },
                    number: {
                        type: Number,
                        required: true
                    },
                    complement: {
                        type: String
                    },
                    zone: {
                        type: String,
                        required: true
                    },
                    zip: {
                        type: Number,
                        required: true
                    },
                    city: {
                        type: String,
                        required: true
                    },
                    uf: {
                        type: String,
                        required: true
                    }
                },
                // required: false,
                allowNull: true
            }
        },
        indexes: [
            {
                fields: {
                    cpf: 1,
                    cnpjNF: 1
                },
                options: {
                    unique: 'wallet_already_exists'
                }
            }
        ],
        methods: {
            getWallet: async function(cpfOrId, cnpjNF, nameNF = '', session = null) {
                const match = isValidObjectId(cpfOrId) ? {_userId: new Types.ObjectId(cpfOrId)} : {cpf: cpfOrId};
                let wallet = await this.findOne({...match, cnpjNF: cnpjNF}).session(session).catch(async () => null);
                if (wallet) {
                    return wallet;
                } else {
                    wallet = await this.findOne({...match, cnpjNF: null}).session(session).catch(async () => null);
                    if (wallet) {
                        return await this.findOneAndUpdate(
                            {
                                ...match,
                                cnpjNF: null
                            },
                            {
                                $set: {
                                    cnpjNF: cnpjNF,
                                    nameNF: nameNF
                                }
                            },
                            {
                                session: session,
                                new: true
                            }
                        ).catch(async () => null);
                    } else {
                        wallet = await this.findOne({...match, cnpjNF: {$exists: true}}).session(session).catch(async () => null);
                        if (wallet) {
                            wallet = JSON.parse(JSON.stringify(wallet));
                            delete wallet._id;
                            delete wallet.createdAt;
                            delete wallet.updatedAt;
                            wallet.cnpjNF = cnpjNF;
                            wallet.nameNF = nameNF;
                            wallet.balance = 0;
                            wallet.taxFreeBalance = 0;
                            wallet.advancedBalance = 0;
                            let newWallet = await this.create([wallet]).catch(async (e) => {console.log(e); return null;});
                            if (newWallet && Array.isArray(newWallet) && newWallet.length) {
                                [newWallet] = newWallet;
                                return newWallet;
                            }
                        }
                    }
                }
                return null;
            }
        }
    }
};
module.exports = Wallets;
