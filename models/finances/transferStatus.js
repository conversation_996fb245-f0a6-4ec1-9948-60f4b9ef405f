const mongoose = require('mongoose');

let TransferStatus = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TransferStatus',
        fields: {
            _transferId: {
                type: mongoose.SchemaTypes.ObjectId,
                index: true,
                required: true
            },
            _userId : {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            userType: {
                type: String,
                required: true,
                enum: [
                    'student',
                    'partner',
                    'teacher',
                    'employer',
                    'computer'
                ]
            },
            userName: {
              type: String,
              required: true
            },
            status: {
                type: String,
                required: true,
                index: true,
                enum: [
                    'analysis',
                    'approved',
                    'transmitted',
                    'transferred',
                    'released',
                    'refused',
                    'error',
                    'canceled'
                ],
                default: 'analysis'
            },
            description: String
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = TransferStatus;
