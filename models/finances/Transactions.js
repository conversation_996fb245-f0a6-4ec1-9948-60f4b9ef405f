const mongoose = require('mongoose');
let Transactions = {
    database: {
        collection: 'Transactions',
        fields: {
            cpf: {
                type: String,
                required: true,
                index: true
            },
            amount: {
                type: Number,
                required: true
            },
            subtotal: {
                type: Number,
                required: true
            },
            description: String,
            isTaxFreeBalance: {
                type: Boolean,
                default: false
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};
module.exports = Transactions;
