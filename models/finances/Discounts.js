let Discounts = {
    database: {
        collection: 'Discounts',
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true
            },
            certifiers: {
                type: [String],
                required: true
            },
            courseTypes: {
                type: [String],
                required: true
            },
            chargeType: {
                type: [String],
                default: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            discountType: {
                type: String,
                enum: [
                    'fixedValue',
                    'percentage'
                ],
                default: true
            },
            value: {
                type: Number,
                required: false
            },
            dueDateDays: {
                type: Number,
                // required: false
            },
            applyAll     : {
                type    : Boolean,
                default : false
            },
            observation: {
                type: String
            },
            useInOverdueCharge: {
                type: <PERSON>olean,
                requried: false
            },
            discountRule: {
                type: String,
                enum: [
                    'not_applied',
                    'antecipation',
                    'fixed_date'
                ]
            }
        },
        options: {
            timestamps: true
        }
    }
};
module.exports = Discounts;
