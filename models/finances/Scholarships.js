let Scholarship = {
    database: {
        collection: 'Scholarships',
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true
            },
            chargeType: {
                type: [String],
                default: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            discountType: {
                type: String,
                enum: [
                    'fixedValue',
                    'percentage'
                ]
            },
            value: {
                type: Number,
                required: false
            },
            useDueDate: {
                type: Boolean,
                default: false
            },
            dateStart: {
                type: Date,
                // required: false
            },
            dateEnd: {
                type: Date,
                // required: false
            },
            observation: {
                type: String
            },
            useInOverdueCharge: {
                type: Boolean,
                requried: false,
                default: true
            }
        },
        options: {
            timestamps: true
        }
    }
};
module.exports = Scholarship;
