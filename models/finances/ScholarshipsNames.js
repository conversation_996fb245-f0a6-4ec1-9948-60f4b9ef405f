let ScholarshipsNames = {
    database: {
        collection: 'ScholarshipsNames',
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            alias: {
                type:String,
                unique: 'Este nome de bolsa já existe',
                required: true
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    alias: 1
                },
                options: {
                    unique: 'Este nome de bolsa já existe',
                    name  : 'dup_alias'
                }
            }
        ]
    }
};
module.exports = ScholarshipsNames;
