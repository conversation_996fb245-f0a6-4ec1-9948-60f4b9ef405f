let TransferCompanies = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TransferCompanies',
        fields: {
            cnpj: {
                type: String,
                required: true
            },
            fantasyName: {
                type: String,
                required: true
            },
            socialName: {
                type: String,
                required: true
            },
            paymentProvider: {
                agreement: {
                    type: String,
                    required: true
                },
                transmissionNumber: String,
                environmentClient: {
                    type: String,
                    enum: [
                        'T',
                        'P'
                    ]
                },
                magneticName: String,
                companyTransmission: String,
                nickName: String
            },
            dataBank: {
                assignor: {
                    type: String
                },
                bankCode: {
                    type: String,
                    required: true
                },
                agency: {
                    type: String,
                    required: true
                },
                agencyDv: {
                    type: Number
                },
                operation: {
                    type: String,
                    default: '0000'
                },
                account: {
                    type: String,
                    required: true
                },
                accountDv: {
                    type: Number,
                    required: true
                },
                accountType: {
                    type: String,
                    required: true,
                    enum: [
                        'conta_corrente',
                        'conta_poupanca',
                        'conta_corrente_conjunta',
                        'conta_poupanca_conjunta'
                    ]
                }
            },
            address: {
                street: {
                    type: String,
                    required: true
                },
                number: {
                    type: Number,
                    required: true
                },
                complement: {
                    type: String
                },
                zone: {
                    type: String,
                    required: true
                },
                zip: {
                    type: Number,
                    required: true
                },
                city: {
                    type: String,
                    required: true
                },
                uf: {
                    type: String,
                    required: true
                }
            },
            isActive: {
                type: Boolean,
                default: true
            }
        },
        indexes: [
            {
                fields: {
                    'paymentProvider.agreement': 1,
                    'dataBank.assignor': 1
                },
                options: {
                    unique: 'Empresa já cadastrada'
                }
            }
        ]
    }
};

module.exports = TransferCompanies;
