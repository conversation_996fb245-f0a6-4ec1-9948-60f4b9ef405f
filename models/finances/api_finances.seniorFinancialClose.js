const mongoose = require('mongoose');

let SeniorFinancialClose = {
    functions: {},
    database: {
        collection: 'SeniorFinancialClose',
        connection: 'database_piaget',
        fields: {
            month: {
                type    : Number,
                required: true
            },
            year: {
                type    : Number,
                required: true
            },
            certifiers:[
                {
                    _certifierId : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _certifierName : {
                        type    : String,
                        required: true
                    },
                    _certifierStatus : {
                        type    : String,
                        enum    : [
                            'open',
                            'closed'
                        ],
                        required: true,
                        default: 'open'
                    }
                }
            ],
            logs:[
                {
                    _userId   : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName : {
                        type    : String,
                        required: true
                    },
                    _userType: {
                        type: String,
                        required: true,
                        enum: [
                            'employer',
                            'computer'
                        ]
                    },
                    action : {
                        type    : String,
                        required: true
                    },
                    metadata: mongoose.SchemaTypes.Mixed,
                    launchedAt: {
                        type: Date,
                        default: new Date()
                    },
                    Previousdata: {
                        type    : String,
                        required: true
                    }
                }
            ]
        }
    }
};

module.exports = SeniorFinancialClose;
