const sendDiscord = async (channel, content) => {
  let uri;

  switch (channel) {
  case 'alerta-pagamentos':
    uri = 'https://discord.com/api/webhooks/1354196328850133405/HJKNRMRnCirpUu7JTcv1oGOkDJkq5KwFOPXFsda8_3V7BzUx0C4asYBDRpM6ADf5Qbhb';
    break;
  case 'alerta-duplicados':
    uri = 'https://discord.com/api/webhooks/1382441198400049183/RkklDQyeMu2f0x6xzs2wSH722GA_ArSiu2MxYcd1hUQ8964TcAnKyS1RqnnwEh_JsyQV';
    break;
  }

  await request({
    method: 'POST',
    json: true,
    uri,
    body: {
      username: 'cronBot',
      avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
      content,
    },
  });
};

const mongoose = require('mongoose');
const request = require('request');
const FinancialMovement = {
  functions: {
    calRehearse: function (next) {
      if (((this || {}).rehearses || []).length > 0) {
        this.rehearse = this.rehearses.map(rehearse => rehearse.value).reduce((a, b) => a + b, 0);
      }
      return next();
    },
    checkDuplicate: function (next) {
      const model = this.constructor;

      // Create a query object with the unique index fields
      const query = {};
      if (this.movementType) query.movementType = this.movementType;
      if (this.method) query.method = this.method;
      if (this.operator) query.operator = this.operator;
      if (this.installment) query.installment = this.installment;
      if (this.parcel) query.parcel = this.parcel;
      if (this.parcels) query.parcels = this.parcels;
      if (this.payer && this.payer.document) query['payer.document'] = this.payer.document;
      if (this.recipient && this.recipient.document) query['recipient.document'] = this.recipient.document;
      if (this.boleto && this.boleto.ourNumber) query['boleto.ourNumber'] = this.boleto.ourNumber;
      if (this.pix && this.pix.txId) query['pix.txId'] = this.pix.txId;
      if (this.card && this.card.tid) query['card.tid'] = this.card.tid;
      if (this.studentCredit && this.studentCredit.walletCpf) query['studentCredit.walletCpf'] = this.studentCredit.walletCpf;
      if (this.value) query.value = this.value;
      if (this.charges) query.charges = this.charges;
      if (this.paymentDate) query.paymentDate = this.paymentDate;

      // Skip checking for duplicates if this is a new document with no fields to check
      if (Object.keys(query).length === 0) {
        return next();
      }

      // Check if a document with the same unique fields already exists
      model.findOne(query).then(existingDoc => {
        if (existingDoc) {
          // Extract only the index fields from the existing document
          const existingIndexFields = {};
          if (existingDoc._id) existingIndexFields._id = existingDoc._id;
          if (existingDoc.movementType) existingIndexFields.movementType = existingDoc.movementType;
          if (existingDoc.method) existingIndexFields.method = existingDoc.method;
          if (existingDoc.operator) existingIndexFields.operator = existingDoc.operator;
          if (existingDoc.installment) existingIndexFields.installment = existingDoc.installment;
          if (existingDoc.parcel) existingIndexFields.parcel = existingDoc.parcel;
          if (existingDoc.parcels) existingIndexFields.parcels = existingDoc.parcels;
          if (existingDoc.payer && existingDoc.payer.document) existingIndexFields['payer.document'] = existingDoc.payer.document;
          if (existingDoc.recipient && existingDoc.recipient.document) existingIndexFields['recipient.document'] = existingDoc.recipient.document;
          if (existingDoc.boleto && existingDoc.boleto.ourNumber) existingIndexFields['boleto.ourNumber'] = existingDoc.boleto.ourNumber;
          if (existingDoc.pix && existingDoc.pix.txId) existingIndexFields['pix.txId'] = existingDoc.pix.txId;
          if (existingDoc.card && existingDoc.card.tid) existingIndexFields['card.tid'] = existingDoc.card.tid;
          if (existingDoc.studentCredit && existingDoc.studentCredit.walletCpf) existingIndexFields['studentCredit.walletCpf'] = existingDoc.studentCredit.walletCpf;
          if (existingDoc.value) existingIndexFields.value = existingDoc.value;
          if (existingDoc.charges) existingIndexFields.charges = existingDoc.charges;
          if (existingDoc.paymentDate) existingIndexFields.paymentDate = existingDoc.paymentDate;

          // Document already exists, send notification to Discord webhook
          const message = `⚠️ **ALERTA DE DUPLICIDADE** ⚠️\n\nDetectado documento duplicado na coleção FinancialMovement durante pre-save.\n\nDados: \`\`\`${JSON.stringify(existingIndexFields, null, 2)}\`\`\``;

          sendDiscord('alerta-duplicados', message);
        }
        // Continue with save operation regardless
        return next();
      }).catch(err => {
        // If there's an error during the check, log it and continue
        console.error('Error checking for duplicates:', err);
        return next();
      });
    },
  },
  database: {
    collection: 'FinancialMovement',
    connection: 'database_piaget',
    fields: {
      account: {
        type: {
          number: String,
          dv: String,
          agency: String,
          agencyDv: String,
          op: String,
        },
        // required: false
      },
      assignor: {
        type: {
          company: {
            cnpj: {
              type: String,
              // required: false
            },
            name: {
              type: String,
              // required: false
            },
          },
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            default: undefined,
          },
        },
        // required: false,
        default: undefined,
      },
      movementType: {
        type: String,
        enum: ['in', 'out', 'cancel'],
        required: true,
        index: true,
      },
      method: {
        type: String,
        enum: ['boleto', 'creditCard', 'debitCard', 'studentCredit', 'deposit', 'transfer', 'pix', 'lyrapay', 'getnet','transfer_cobrafix'],
        required: true,
        index: true,
      },
      operator: {
        type: String,
        required: true,
        index: true,
      },
      installment: {
        type: Number,
        // required: false,
        default: 1,
      },
      parcel: {
        type: Number,
        // required: false,
        default: 1,
      },
      parcels: {
        type: Number,
        // required: false,
        default: 1,
      },
      payer: {
        type: {
          name: {
            type: String,
            required: true,
          },
          document: {
            type: String,
            min: 11,
            max: 14,
            required: true,
          },
          documentType: {
            type: String,
            enum: ['cpf', 'cnpj'],
            required: true,
          },
          address: {
            type: {
              street: {
                type: String,
                required: true,
              },
              number: {
                type: String,
                // required: false,
                default: 'SN',
              },
              complement: {
                type: String,
                // required: false,
                default: '',
              },
              zone: {
                type: String,
                required: true,
              },
              city: {
                type: String,
                required: true,
              },
              uf: {
                type: String,
                maxlength: 2,
                required: true,
              },
              zip: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
        },
        required: function () {
          return ['in', 'cancel'].includes(this.movementType);
        },
      },
      recipient: {
        type: {
          name: {
            type: String,
            required: true,
          },
          document: {
            type: String,
            min: 11,
            max: 14,
            required: true,
          },
          documentType: {
            type: String,
            enum: ['cpf', 'cnpj'],
            required: true,
          },
          address: {
            type: {
              street: {
                type: String,
                required: true,
              },
              number: {
                type: String,
                // required: false,
                default: 'SN',
              },
              complement: {
                type: String,
                // required: false,
                default: '',
              },
              zone: {
                type: String,
                required: true,
              },
              city: {
                type: String,
                required: true,
              },
              uf: {
                type: String,
                maxlength: 2,
                required: true,
              },
              zip: {
                type: String,
                required: true,
              },
            },
            // required: false
          },
        },
        required: function () {
          return this.movementType === 'out';
        },
      },
      course: {
        certifier: {
          type: String,
          // required: false
        },
        type: {
          type: String,
          // required: false
        },
        name: {
          type: String,
          // required: false
        },
        workload: {
          type: String,
          // required: false
        },
        area: {
          type: String,
          // required: false
        },
      },
      combo: [{
        certifier: {
          type: String,
          // required: false
        },
        type: {
          type: String,
          // required: false
        },
        name: {
          type: String,
          // required: false
        },
        workload: {
          type: String,
          // required: false
        },
        area: {
          type: String,
          // required: false
        },
      }],
      boleto: {
        type: {
          ourNumber: {
            type: String,
            // required: false,
            default: null,
          },
          digitableLine: {
            type: String,
            // required: false,
            default: null,
          },
          metadata: mongoose.SchemaTypes.Mixed,
        },
        required: function () {
          return this.movementType === 'in' && this.method === 'boleto';
        },
      },
      pix: {
        type: {
          txId: {
            type: String,
            // required: false,
            default: null,
          },
          metadata: mongoose.SchemaTypes.Mixed,
        },
        required: function () {
          return this.movementType === 'in' && this.method === 'pix';
        },
      },
      card: {
        type: {
          tid: {
            type: String,
          },
          authorization: {
            type: String,
          },
          metadata: mongoose.SchemaTypes.Mixed,
        },
        required: function () {
          return this.movementType === 'in' && ['creditCard', 'debitCard'].includes(this.method);
        },
      },
      studentCredit: {
        type: {
          walletCpf: {
            type: String,
            required: true,
          },
          metadata: mongoose.SchemaTypes.Mixed,
        },
        required: function () {
          return this.movementType === 'in' && this.method === 'studentCredit';
        },
      },
      transfer: {
        type: {
          receiptCode: {
            type: String,
            // required: false,
            default: null,
          },
          metadata: mongoose.SchemaTypes.Mixed,
        },
        required: function () {
          return this.movementType === 'in' && ['transfer', 'transfer_cobrafix'].includes(this.method);
        },
      },
      paymentDate: {
        type: Date,
        required: true,
        index: true,
      },
      processDate: {
        type: Date,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      value: {
        type: Number, // centavos
        required: true,
      },
      discount: {
        type: Number, // centavos
        // required: false,
        default: 0,
      },
      interest: {
        type: Number, // centavos
        // required: false,
        default: 0,
      },
      mulct: {
        type: Number, // centavos
        // required: false,
        default: 0,
      },
      rehearse: {
        type: Number, // centavos
        // required: false,
        default: 0,
      },
      rehearses: {
        type: [
          {
            _id: false,
            name: {
              type: String,
              required: true,
            },
            document: {
              type: String,
              minLength: 11,
              maxlength: 14,
              required: true,
            },
            documentType: {
              type: String,
              required: true,
            },
            value: {
              type: Number,
              required: true,
            },
          },
        ],
        required: function () {
          return this.rehearse > 0;
        },
        default: [],
      },
      integration: {
        type: [
          {
            _id: false,
            name: {
              type: String,
              required: true,
            },
            isSent: {
              type: Boolean,
              // required: false,
              default: false,
            },
            dateSent: Date,
            uuid: {
              type: Number,
              // required: false
            },
            dataSend: mongoose.SchemaTypes.Mixed,
            response: mongoose.SchemaTypes.Mixed,
            metadata: mongoose.SchemaTypes.Mixed,
          },
        ],
        // required: false,
        default: [],
        index: true,
      },
      sentToFinancialErp: {
        type: Boolean,
        // required: false,
        default: true,
      },
      charges: {
        type: [mongoose.SchemaTypes.ObjectId],
        // required: false,
        default: undefined,
      },
      metadata: mongoose.SchemaTypes.Mixed,
    },
  },
  indexes: [
    {
      fields: {
        movementType: 1,
        method: 1,
        operator: 1,
        installment: 1,
        parcel: 1,
        parcels: 1,
        'payer.document': 1,
        'recipient.document': 1,
        'boleto.ourNumber': 1,
        'pix.txId': 1,
        'card.tid': 1,
        'studentCredit.walletCpf': 1,
        value: 1,
        charges: 1,
        paymentDate: 1,
      },
      options: {
        unique: true,
        name: 'movement_1',
        sparse: true,
      },
    },
  ],
};
FinancialMovement.database.pre = {
  save: [FinancialMovement.functions.checkDuplicate, FinancialMovement.functions.calRehearse],
};

// Keep post-error hook as a fallback
FinancialMovement.database.post = {
  error: function (error, doc, next) {
    // Always pass the error to the next middleware
    next(error);
  },
};

module.exports = FinancialMovement;
