let Transfers = {
    database: {
        collection: 'Transfers',
        fields: {
            order: {
                type: Number,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                index: true
            },
            cnpjNF: {
                type: String,
                // required: false,
                allowNull: true
            },
            nameNF: {
                type: String,
                // required: false,
                allowNull: true
            },
            amountRequest: {
                value: {
                    type: Number, // Valor Solicitado
                    required: true
                },
                inss: {
                    type: Number, // Valor cobrado de INSS
                    default: 0
                },
                ir: {
                    type: Number, // Valor cobrado de IR
                    default: 0
                },
                discount: {
                    valueType: {
                        type: String, // Tipo de valor para desconto, porcentagem ou valor
                        enum: [
                            'percent',
                            'value'
                        ],
                        default: 'percent'
                    },
                    discountValue: {
                        type: Number, // Valor do desconto em porcentagem ou valor
                        default: 0
                    },
                    value: {
                        type: Number, // Valor total descontado
                        default: 0
                    }
                },
                transferRate: {
                    type: Number, // Taxa de transferência
                    required: true
                },
                toReceive: {
                    type: Number, // Liquido a receber
                    required: true
                }
            },
            isTaxFreeBalance: {
                type: Boolean, // Se é para descontar na carteira de mensalidade ou taxa de inscrição
                index: true,
                default: false
            },
            invoice: {
                type: Boolean, // Se tem nota fiscal ou não
                default: false
            },
            archive: String, // Arquivo da nota
            dataBank: {
                legalName: {
                    type: String,
                    required: true
                },
                documentType: {
                    type: String,
                    required: true,
                    enum: [
                        'cpf',
                        'cnpj'
                    ]
                },
                documentNumber: {
                    type: String,
                    required: true
                },
                bankCode: {
                    type: String,
                    required: true
                },
                agency: {
                    type: String,
                    required: true
                },
                agencyDv: {
                    type: String
                },
                operation: {
                    type: String,
                    default: '0000'
                },
                account: {
                    type: String,
                    required: true
                },
                accountDv: {
                    type: String,
                    required: true
                },
                accountType: {
                    type: String,
                    required: true,
                    enum: [
                        'conta_corrente',
                        'conta_poupanca',
                        'conta_corrente_conjunta',
                        'conta_poupanca_conjunta'
                    ]
                }
            },
            address: {
                street: {
                    type: String,
                    required: true
                },
                number: {
                    type: Number,
                    required: true
                },
                complement: {
                    type: String
                },
                zone: {
                    type: String,
                    required: true
                },
                zip: {
                    type: Number,
                    required: true
                },
                city: {
                    type: String,
                    required: true
                },
                uf: {
                    type: String,
                    required: true
                }
            },
            status: {
                type: String,
                required: true,
                index: true,
                enum: [
                    'analysis', // Em análise - Para o financeiro analisar a solicitação (nota fiscal)
                    'approved', // Aprovado - A solicitação foi aprovada pelo financeiro
                    'transmitted', // Transmitida - O arquivo de remessa foi gerado
                    'transferred', // Transferido - Arquivo de remessa enviado para o banco
                    'released', // Realizada - Arquivo de retorno processado e descontado na carteira
                    'refused', // Solicitação recusada por alguma pendência
                    'error', // Solicitação com erro
                    'canceled' // Solicitação cancelada
                ],
                default: 'analysis'
            },
            dataRemessa: {
                arquivoSequenciaNsa: String,
                dataVencimento: String
            },
            dataRetorno: {
                dataRealEfetivacaoPgto: String,
                valorRealEfetivacaoPgto: Number,
                codigosOcorrencias: String,
                msgOcorrencias: String,
                comprovante: String
            },
            history: {
                type: [
                    {
                        code: {
                            type: String,
                            required: true
                        },
                        message: {
                            type: String,
                            required: true
                        }
                    }
                ],
                // required: false,
                default: []
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    order: 1
                },
                options: {
                    unique: 'Já existe uma transferência com este número'
                }
            }
        ]
    }
};
module.exports = Transfers;
