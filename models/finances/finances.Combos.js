const mongoose = require('mongoose');

const Installment = {
  _id: false,
  installment: { type: Number, required: true },
  value: { type: Number, required: true },
};

const PaymentMethods = {
  creditCard: { type: [Installment], default: [] },
  debitCard: { type: [Installment], default: [] },
  cardRecurrence: { type: [Installment], default: [] },
  boleto: { type: [Installment], default: [] },
  pix: { type: [Installment], default: [] },
};

const PlanItem = {
  courses: { type: Number, required: true },
  plan: PaymentMethods,
};

const QuantityCourse = {
  typeCourse: { type: String },
  quantity: { type: Number },
  subcategory: { type: String },
};

const Amount = {
  boleto: { type: Number, required: true },
  creditCard: { type: Number, required: true },
  debitCard: { type: Number, required: true },
  cardRecurrence: { type: Number, required: true },
};

const VoucherSection = {
  amountType: {
    type: String,
    enum: ['percentage', 'value', ''],
    required: true,
  },
  amount: Amount,
};

const VoucherCertifier = {
  _id: false,
  name: { type: String, required: true },
  description: { type: String, required: true },
  courseType: [{ type: String, required: true }],
};

const ReleaseVoucher = {
  tags: { type: [String], default: [] },
  maximunQuantity: Number,
  validateType: String,
  isFree: Boolean,
  referenceCertifier: String,
  course: VoucherSection,
  enrolment: VoucherSection,
  certifier: { type: [VoucherCertifier], default: [] },
};

const Combos = {
  database: {
    connection: 'database_piaget',
    collection: 'Combos',
    fields: {
      maxCourses: { type: Number, required: true },
      certificateCredits: { type: Number, default: 0 },
      certifier: mongoose.SchemaTypes.Mixed,
      courseType: { type: String, default: '*' },
      subcategory: { type: String, default: '*' },
      area: { type: String, default: '*' },
      tags: mongoose.SchemaTypes.Mixed,
      _courses: mongoose.SchemaTypes.Mixed,
      _exceptions: mongoose.SchemaTypes.Mixed,
      blackListedTags: mongoose.SchemaTypes.Mixed,
      commissionLevel: {
        type: String,
        enum: ['min', 'med', 'max'],
        required: true,
      },
      paymentPlan: {
        type: [PlanItem],
        default: [],
      },
      minPaymentPlan: {
        type: [PlanItem],
        default: [],
      },
      isEnabled: { type: Boolean, default: false },
      name: { type: String },
      quantityCourses: {
        type: [QuantityCourse],
        default: [],
      },
      referenceCommissionType: { type: String },
      referenceDiscountCertifier: { type: String },
      releaseVouchers: {
        type: [ReleaseVoucher],
        default: [],
      },
    },
  },
};

module.exports = Combos;
