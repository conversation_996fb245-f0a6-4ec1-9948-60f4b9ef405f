let TaxRates = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TaxRates',
        fields: {
            INSS: {
                maxValue: {
                    type: Number,
                    required: true
                },
                aliquot: {
                    type: Number,
                    default: 0
                }
            },
            IR: {
                freeBase: {
                    maxValue: {
                        type: Number,
                        required: true
                    }
                },
                intervalBase: [
                    {
                        minValue: {
                            type: Number,
                            required: true
                        },
                        maxValue: {
                            type: Number,
                            required: true
                        },
                        aliquot: {
                            type: Number,
                            required: true
                        },
                        cotaDeduce: {
                            type: Number,
                            required: true
                        }
                    }
                ],
                maxBase: {
                    maxValue: {
                        type: Number,
                        required: true
                    },
                    aliquot: {
                        type: Number,
                        required: true
                    },
                    cotaDeduce: {
                        type: Number,
                        required: true
                    }
                }
            },
            isActive: {
                type: Boolean,
                default: true
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = TaxRates;
