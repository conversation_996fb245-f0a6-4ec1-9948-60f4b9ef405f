const moment = require('moment');
const customGuids = require('../../../services/CustomGuids');
const {sendDiscord} = require("../../../services/Utils");

const CONF = {
    maxTransactions: 10,
    hours: 1
};

/*const changeGenerateCharges = async (order) => {
    if (order && (order.status === 'canceled' || order.status === 'blocked' || order.status === 'expired')) {
        order.generateCharges = false;
    }

    return order.save()
};*/

let BillingsCielo = {
    functions: {
        maskCard: function (next) {
            if (this.PAGAMENTO.CARTAO.NUMERO)
                this.PAGAMENTO.CARTAO.NUMERO = `**********${this.PAGAMENTO.CARTAO.NUMERO.substr(this.PAGAMENTO.CARTAO.NUMERO.length - 4, 4)}`;

            return next();
        },
        securityCheck: async function (next) {
            if (this.isNew && this.PAGADOR.CPF) {
                if (process.env.NODE_ENV === 'development') return next();

                const recentBills = await this.constructor.find({
                    'PAGADOR.CPF': this.PAGADOR.CPF,
                    createdAt: {
                        $gte: moment().subtract(CONF.hours, 'hours').toDate()
                    }
                }, {_id: 1}).exec();

                  if (recentBills.length >= CONF.maxTransactions) {
                    sendDiscord('alerta-pagamentos', `Limite de transações no cartão excedido para o mesmo pagador, limite: ${CONF.maxTransactions} em ${CONF.hours} horas | cpf pagador: ${this.PAGADOR.CPF}`);
                    return next(Error('security_check_error'));
                  }
            }

            return next();
        }
    },
    database: {
        collection: 'BillingsCielo',
        connection: 'database_payments',
        fields: {
            VALOR: {
                type: String,
                required: true
            },
            VALOR_PAGO: {
                type: String,
                // required: false,
                default: 0
            },
            PAGO: {
                type: Boolean,
                default: false
            },
            DATA_PAGAMENTO: {
                type: Date,
            },
            DATA_PROCESSAMENTO: {
                type: Date,
            },
            PEDIDO_ID: {
                type: String,
                length: 11,
                unique: true,
                default: function () {
                    return customGuids.hexGuid()
                        .toUpperCase();
                }
            },
            PEDIDO_DATA: {
                type: Date,
                default: function () {
                    return new Date().toISOString();
                },
                index: true
            },
            PAGADOR: {
                NOME: {
                    type: String,
                    required: true,
                    lowercase: true
                },
                CPF: {
                    type: String,
                    length: 11,
                    default: null,
                    index: true
                },
                DATA_NASCIMENTO: {
                    type: Date,
                    default: null
                },
                EMAIL: {
                    type: String,
                    default: null
                },
                ENDERECO: {
                    CEP: {
                        type: String,
                        lowercase: true,
                        length: 8,
                        default: null
                    },
                    RUA: {
                        type: String,
                        lowercase: true,
                        default: null
                    },
                    NUMERO: {
                        type: String,
                        default: null
                    },
                    COMPLEMENTO: {
                        type: String,
                        lowercase: true,
                        default: null
                    },
                    CIDADE: {
                        type: String,
                        lowercase: true,
                        default: null,
                        index: true
                    },
                    UF: {
                        type: String,
                        uppercase: true,
                        default: null,
                        length: 2,
                        index: true
                    },
                    PAIS: {
                        type: String,
                        uppercase: true,
                        default: null,
                        length: 2
                    }
                }
            },
            PAGAMENTO: {
                TIPO: {
                    type: String,
                    enum: [
                        'DebitCard',
                        'CreditCard'
                    ],
                    default: 'CreditCard'
                },
                VALOR: {
                    type: String,
                    required: true
                },
                PARCELAS: {
                    type: Number,
                    required: true,
                    index: true
                },
                RECORRENTE: {
                    type: Boolean,
                    // required: false,
                    default: false
                },
                CARTAO: {
                    TOKEN: {
                        type: String,
                        // required: false
                    },
                    NUMERO: {
                        type: String,
                        length: 19
                    },
                    HOLDER: {
                        type: String,
                        lowercase: true
                    },
                    DATA_EXPIRACAO: {
                        type: String,
                        length: 7
                    },
                    BANDEIRA: {
                        type: String,
                        enum: [
                            'Visa',
                            'Master',
                            'Amex',
                            'Elo',
                            'Aura',
                            'JCB',
                            'Diners',
                            'Discover',
                            'Hipercard'
                        ]
                    },
                    CPF: {
                        type: String
                    }
                }
            },
            CAPTURAR: {
                type: Boolean,
                default: true
            },
            AUTENTICAR: {
                type: Boolean,
                default: false
            },
            RESPOSTA_CIELO: {
                COD: {
                    type: String,
                    default: null
                },
                TID: {
                    type: String,
                    default: null
                },
                VALOR_CAPTURADO: {
                    type: String,
                    default: null
                },
                NUMERO_AUTORIZACAO: {
                    type: String,
                    default: null
                },
                COD_AUTORIZACAO: {
                    type: String,
                    default: null
                },
                PAGAMENTO_ID: {
                    type: String,
                    default: null
                },
                STATUS: {
                    type: String,
                    default: null
                },
                LINKS: {
                    type: Array,
                    default: null
                }
            },
            COMERCIANTE_ID: {
                type: String,
                default: null
            },
            ESTABELECIMENTO: {
                type: String,
                required: true
            },
            IP_REQUISITANTE: {
                type: String,
                default: null
            },
            TAGS: {
                type: Array,
                default: null,
                index: true
            }
        },
    }
};

BillingsCielo.database.pre = {
    save: [
        BillingsCielo.functions.securityCheck,
        BillingsCielo.functions.maskCard,
    ],
};

module.exports = BillingsCielo;
