const mongoose = require('mongoose');

let BillingsLyrapay = {
    functions: {},
    database: {
        collection: 'BillingsLyrapay',
        connection: 'database_payments',
        fields: {
            ID_INVOICE: {
                type: String,
                // required: false,
                index: true
            },
            DATA_VENCIMENTO: {
                type   : Date,
                required: true,
            },
            VALOR: {
                type: String,
                required: true,
            },
            DATA_EMISSAO: {
                type   : Date,
                default: new Date(),
                required: true,
            },
            PAGADOR: {
                CPF: {
                    type: String,
                    maxlength: 11,
                    required: true,
                    index: true
                },
                NOME: {
                    type: String,
                    required: true,
                },
            },
            BENEFICIARIO_FINAL: {
              CPF: {
                type: String,
                maxlength: 11,
                index: true
              },
              NOME: {
                type: String,
                maxlength: 40,
                uppercase: true
              },
              CNPJ: {
                type: String,
                maxlength: 14,
                default: null,
                index: true
              },
            },
            PAGAMENTO: {
                TIPO: {
                    type: String,
                },
                PARCELAS: {
                    type: Number,
                },
            },
            PAGO: {
                type: Boolean,
                default: false
            },
            VALOR_PAGO: {
                type: String,
                default: null,
                index: true,
            },
            DATA_PAGAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            DATA_PROCESSAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            CHARGES: [{
                _chargeId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false
                },
                name: {
                    type: String,
                    required: true
                },
                quantity: {
                    type: Number,
                    required: true
                },
                value: {
                    type: Number,
                    required: true
                }
            }],
            metadata: mongoose.SchemaTypes.Mixed,
            PIX: {
                URL: String,
                TXID: String,
                QRCODE: String
            },
            BOLETO: {
                API: {
                    type: String,
                    default: null
                }
            },
            LINHA_DIGITAVEL: {
                type: String,
                default: null
            },
            CODIGO_BARRAS: {
                type: String,
                default: null
            },
        },
    }
};

module.exports = BillingsLyrapay;
