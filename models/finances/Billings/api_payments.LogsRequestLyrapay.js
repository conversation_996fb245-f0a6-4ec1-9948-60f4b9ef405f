const mongoose = require('mongoose');
let LogsRequestLyrapay = {
    functions: {},
    database: {
        collection: 'LogsRequestLyrapay',
        connection: 'database_payments',
        fields: {
            _billingId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            _invoiceId: {
                type: String,
                // required: false,
                default: null
            },
            headers: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            path: {
                type: String,
                // required: false
            },
            body: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            response: {
                type: String,
                // required: false,
                default: '-'
            },
            isSucess: {
                type: Boolean,
                default: false
            },
            method: {
                type: String,
            },
            metadata: mongoose.SchemaTypes.Mixed,
        },
        indexes: [
        ]
    }
}

module.exports = LogsRequestLyrapay;
