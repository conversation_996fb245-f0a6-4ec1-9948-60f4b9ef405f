const mongoose = require('mongoose');
const moment = require('moment');
const customGuids = require('../../../services/CustomGuids');

let BillingsBB = {
    functions: {},
    database: {
        collection: 'BillingsBB',
        connection: 'database_payments',
        fields: {
            CODIGO_BENEFICIARIO: {
                type: String,
                maxlength: 7,
                required: true,
                index: true
            },
            UNIDADE: {
                type: String,
                maxlength: 4,
                required: true,
                index: true
            },
            BANCO: {
                type: String,
                required: true
            },
            NOSSO_NUMERO: {
                type: String,
                maxlength: 20,
                // required: false,
                default: null
            },
            NUMERO_DOCUMENTO: {
                type: String,
                maxlength: 20,
                unique: true,
                default: function () {
                    return '000' + this.CODIGO_BENEFICIARIO + customGuids.makeid(10, 'onlyNumbers');
                }
            },
            DATA_VENCIMENTO: {
                type: String,
                maxlength: 10,
                required: true,
                index: true
            },
            VALOR: {
                type: String,
                required: true,
                index: true
            },
            TIPO_ESPECIE: {
                type: String,
                maxlength: 2,
                default: '99'
            },
            FLAG_ACEITE: {
                type: String,
                maxlength: 1,
                enum: [
                    'S',
                    'N'
                ],
                default: 'S'
            },
            DATA_EMISSAO: {
                type: String,
                maxlength: 10,
                default: function () {
                    return moment().format('YYYY-MM-DD');
                },
                index: true
            },
            JUROS_MORA: {
                TIPO: {
                    type: String,
                    required: true,
                    enum: [
                        'VALOR_POR_DIA',
                        'TAXA_MENSAL',
                        'ISENTO'
                    ]
                },
                DATA: {
                    type: String,
                    maxlength: 10,
                    default: function () {
                        return moment().format('YYYY-MM-DD');
                    }
                },
                VALOR: {
                    type: String,
                    required: function () {
                        return this.JUROS_MORA.TIPO !== 'ISENTO' && !this.JUROS_MORA.PERCENTUAL;
                    },
                    default: null
                },
                PERCENTUAL: {
                    type: String,
                    required: function () {
                        return this.JUROS_MORA.TIPO !== 'ISENTO' && !this.JUROS_MORA.VALOR;
                    },
                    min: 0,
                    max: 100,
                    default: null
                }
            },
            VALOR_ABATIMENTO: {
                type: String,
                default: 0
            },
            POS_VENCIMENTO: {
                ACAO: {
                    type: String,
                    enum: [
                        'PROTESTAR',
                        'DEVOLVER'
                    ],
                    required: true
                },
                NUMERO_DIAS: {
                    type: String,
                    required: true
                }
            },
            CODIGO_MOEDA: {
                type: String,
                default: 9
            },
            PAGADOR: {
                CPF: {
                    type: String,
                    maxlength: 11,
                    required: function () {
                        return !this.PAGADOR.CNPJ;
                    },
                    index: true
                },
                NOME: {
                    type: String,
                    maxlength: 40,
                    required: function () {
                        return !this.PAGADOR.RAZAO_SOCIAL;
                    },
                    uppercase: true
                },
                CNPJ: {
                    type: String,
                    maxlength: 14,
                    default: null,
                    index: true
                },
                RAZAO_SOCIAL: {
                    type: String,
                    maxlength: 40,
                    uppercase: true,
                    default: null
                },
                ENDERECO: {
                    LOGRADOURO: {
                        type: String,
                        maxlength: 40,
                        required: true,
                        uppercase: true
                    },
                    BAIRRO: {
                        type: String,
                        maxlength: 15,
                        required: true,
                        uppercase: true
                    },
                    CIDADE: {
                        type: String,
                        maxlength: 15,
                        required: true,
                        uppercase: true
                    },
                    UF: {
                        type: String,
                        maxlength: 2,
                        required: true,
                        uppercase: true
                    },
                    CEP: {
                        type: String,
                        length: 8,
                        required: true
                    }
                }
            },
            BENEFICIARIO_FINAL: {
              CPF: {
                type: String,
                maxlength: 11,
                index: true
              },
              NOME: {
                type: String,
                maxlength: 40,
                uppercase: true
              },
              CNPJ: {
                type: String,
                maxlength: 14,
                default: null,
                index: true
              },
            },
            SACADOR_AVALISTA: {
                CPF: {
                    type: String,
                    maxlength: 11,
                    default: null,
                    index: true
                },
                NOME: {
                    type: String,
                    maxlength: 40,
                    default: null,
                    uppercase: true
                },
                CNPJ: {
                    type: String,
                    maxlength: 14,
                    required: function () {
                        return !this.SACADOR_AVALISTA.CPF;
                    },
                    index: true
                },
                RAZAO_SOCIAL: {
                    type: String,
                    maxlength: 40,
                    required: function () {
                        return !this.SACADOR_AVALISTA.CPF;
                    },
                    uppercase: true
                }
            },
            MULTA: {
                DATA: {
                    type: String,
                    maxlength: 10,
                    default: null
                },
                VALOR: {
                    type: String,
                    default: null
                },
                PERCENTUAL: {
                    type: String,
                    default: null
                }
            },
            DESCONTOS: {
                DESCONTO: {
                    DATA: {
                        type: String,
                        maxlength: 10,
                        default: null
                    },
                    VALOR: {
                        type: String,
                        default: null
                    },
                    PERCENTUAL: {
                        type: String,
                        default: null
                    }
                }
            },
            VALOR_IOF: {
                type: String,
                default: null
            },
            PAGAMENTO: {
                QUANTIDADE_PERMITIDA: {
                    type: String,
                    min: 1,
                    max: 99,
                    default: 1
                },
                TIPO: {
                    type: String,
                    enum: [
                        'ACEITA_QUALQUER_VALOR',
                        'ACEITA_VALORES_ENTRE_MINIMO_MAXIMO',
                        'NAO_ACEITA_VALOR_DIVERGENTE',
                        'SOMENTE_VALOR_MINIMO'
                    ],
                    required: true
                },
                VALOR_MINIMO: {
                    type: String,
                    default: 0,
                    required: function () {
                        return (
                            this.PAGAMENTO.TIPO === 'SOMENTE_VALOR_MINIMO' ||
                            this.PAGAMENTO.TIPO === 'ACEITA_VALORES_ENTRE_MINIMO_MAXIMO' ||
                            this.PAGAMENTO.TIPO === 'NAO_ACEITA_VALOR_DIVERGENTE'
                        );
                    }
                },
                VALOR_MAXIMO: {
                    type: String,
                    default: 0,
                    required: function () {
                        return (
                            this.PAGAMENTO.TIPO === 'ACEITA_VALORES_ENTRE_MINIMO_MAXIMO'
                        );
                    }
                },
                PERCENTUAL_MINIMO: {
                    type: String,
                    default: null
                },
                PERCENTUAL_MAXIMO: {
                    type: String,
                    default: null
                }
            },
            PAGO: {
                type: Boolean,
                default: false
            },
            VALOR_PAGO: {
                type: String,
                default: null,
                index: true
            },
            DATA_PAGAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            DATA_CREDITO: {
                type: Date,
                default: null,
                index: true
            },
            DATA_PROCESSAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            IP_REQUISITANTE: {
                type: String,
                maxlength: 15,
                default: null
            },
            LINHA_DIGITAVEL: {
                type: String,
                default: null
            },
            CODIGO_BARRAS: {
                type: String,
                default: null
            },
            BOLETO: {
                BB: {
                    type: String,
                    default: null
                },
                API: {
                    type: String,
                    default: null
                }
            },
            TAGS: {
                type: Array,
                default: null,
                index: true
            },
            INSTRUCOES: {
                type: String,
                // required: false,
                default: ''
            },
            PIX: {
                URL: String,
                TXID: String,
                QRCODE: String
            },
            metadata: mongoose.SchemaTypes.Mixed,
            isCancel: Boolean,
            cancelDate: Date
        },
        indexes: [
            {
                fields: {
                    CODIGO_BENEFICIARIO: 1,
                    NUMERO_DOCUMENTO: 1
                },
                options: {
                    unique: 'assignor_own_number_already_exists'
                }
            }
        ]
    }
};

module.exports = BillingsBB;
