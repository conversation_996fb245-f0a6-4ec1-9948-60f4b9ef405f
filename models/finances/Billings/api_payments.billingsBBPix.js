const mongoose = require('mongoose');
const moment = require('moment');

let BillingsBBPix = {
    functions: {},
    database: {
        collection: 'BillingsBBPix',
        connection: 'database_payments',
        fields: {
            BANCO: {
                type: String,
                required: true
            },
            CHAVE: {
                type: String,
                required: true
            },
            TXID: {
                type: String,
                maxlength: 35,
                // required: false,
                index: true
            },
            ENDTOENDID:{
                type: String,
                required: false,
            },
            DURACAO: {
                type: Number,
                // required: false,
                default: 3600 // 1h
            },
            CHECAR_STATUS: {
                type: Boolean,
                // required: false,
                default: false
            },
            VALOR: {
                type: String,
                required: true,
                index: true
            },
            TIPO_ESPECIE: {
                type: String,
                maxlength: 2,
                default: '99'
            },
            DATA_EMISSAO: {
                type: String,
                maxlength: 10,
                default: function () {
                    return moment().format('YYYY-MM-DD');
                },
                index: true
            },
            VALOR_ABATIMENTO: {
                type: String,
                default: 0
            },
            CODIGO_MOEDA: {
                type: String,
                default: 9
            },
            PAGADOR: {
                CPF: {
                    type: String,
                    maxlength: 11,
                    required: function () {
                        return !this.PAGADOR.CNPJ;
                    },
                    index: true
                },
                NOME: {
                    type: String,
                    maxlength: 40,
                    required: function () {
                        return !this.PAGADOR.RAZAO_SOCIAL;
                    },
                    uppercase: true
                },
                CNPJ: {
                    type: String,
                    maxlength: 14,
                    default: null,
                    index: true
                },
                RAZAO_SOCIAL: {
                    type: String,
                    maxlength: 40,
                    uppercase: true,
                    default: null
                }
            },
            BENEFICIARIO_FINAL: {
              CPF: {
                type: String,
                maxlength: 11,
                index: true
              },
              NOME: {
                type: String,
                maxlength: 40,
                uppercase: true
              },
              CNPJ: {
                type: String,
                maxlength: 14,
                default: null,
                index: true
              },
            },
            SACADOR_AVALISTA: {
                CPF: {
                    type: String,
                    maxlength: 11,
                    default: null,
                    index: true
                },
                NOME: {
                    type: String,
                    maxlength: 40,
                    default: null,
                    uppercase: true
                },
                CNPJ: {
                    type: String,
                    maxlength: 14,
                    required: function () {
                        return !this.SACADOR_AVALISTA.CPF;
                    },
                    index: true
                },
                RAZAO_SOCIAL: {
                    type: String,
                    maxlength: 40,
                    required: function () {
                        return !this.SACADOR_AVALISTA.CPF;
                    },
                    uppercase: true
                }
            },
            PAGO: {
                type: Boolean,
                default: false
            },
            VALOR_PAGO: {
                type: String,
                default: null,
                index: true
            },
            DATA_PAGAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            DATA_CREDITO: {
                type: Date,
                default: null,
                index: true
            },
            DATA_PROCESSAMENTO: {
                type: Date,
                default: null,
                index: true
            },
            IP_REQUISITANTE: {
                type: String,
                maxlength: 15,
                default: null
            },
            PIX: {
                URL: {
                    type: String,
                    default: null
                },
                QRCODE: {
                    type: String,
                    default: null
                },
                BBLINK: {
                    type: String,
                    default: null
                }
            },
            TAGS: {
                type: Array,
                default: null,
                index: true
            },
            INSTRUCOES: {
                type: String,
                // required: false,
                default: ''
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = BillingsBBPix;
