const mongoose = require('mongoose');
let LogsLyrapay = {
    database: {
        collection: 'LogsLyrapay',
        connection: 'database_payments',
        fields: {
            ID_INVOICE: {
                type: String,
                // required: false,
                default: null
            },
            TID: {
                type: String,
                // required: false,
                default: null
            },
            card: {
                type: String,
                default: null
            },
            brand: {
                type: String,
                default: null
            },
            cpf: {
                type: String,
                default: null
            },
            value: {
                type: String,
                default: null
            },
            code: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                default: '-',
                index: true
            },
            reason: {
                type: String
            },
            response: {
                type: String,
                // required: false,
                default: '-'
            },
            isBad: {
                type: Boolean,
                default: false
            },
            operator: String,
            res: mongoose.SchemaTypes.Mixed,
            message: mongoose.SchemaTypes.Mixed,
            completeResponse: mongoose.SchemaTypes.Mixed,
            completeRequest: mongoose.SchemaTypes.Mixed,
            metadata: mongoose.SchemaTypes.Mixed,
        }
    }
}

module.exports = LogsLyrapay;
