let BillingsStudentCredit = {
    functions: {},
    database: {
        collection: 'BillingsStudentCredit',
        connection: 'database_payments',
        fields    : {
            VALOR             : {
                type    : String,
                required: true
            },
            VALOR_PAGO        : {
                type    : String,
                // required: false,
                default : 0
            },
            DATA_PAGAMENTO    : {
                type   : Date,
                default: new Date()
            },
            DATA_PROCESSAMENTO: {
                type   : Date,
                default: new Date()
            },
            PAGADOR           : {
                NOME           : {
                    type     : String,
                    required : true,
                    lowercase: true
                },
                CPF            : {
                    type   : String,
                    length : 11,
                    default: null,
                    index  : true
                }
            },
            PAGO: {
                type   : Boolean,
                default: false
            }
        }
    }
};

module.exports = BillingsStudentCredit;
