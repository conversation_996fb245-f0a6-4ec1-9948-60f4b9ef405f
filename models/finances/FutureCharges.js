const mongoose = require('mongoose');
let FutureCharges = {
    database: {
        collection: 'FutureCharges',
        fields: {
            cpf: {
                type: String,
                required: true,
                index: true
            },
            transactionType: {
                type: String,
                required: true,
                enum: [
                    'credit',
                    'debit'
                ]
            },
            amount: {
                type: Number,
                required: true
            },
            isTaxFreeBalance: {
                type: Boolean,
                default: false
            },
            description: String,
            metadata: mongoose.SchemaTypes.Mixed,
            launchAt: {
                type: Date,
                // required: false,
                default: null
            },
            launchedAt: {
                type: Date,
                // required: false,
                default: null
            },
            isCanceled: {
                type: Boolean,
                required: true,
                default: false
            },
            isBlocked: {
                type: Boolean,
                required: true,
                default: false
            },
            isLaunched: {
                type: Boolean,
                required: true,
                default: false
            },
            cancelReason    : {
                type    : String,
                // required: false
            }
        }
    }
};
module.exports = FutureCharges;
