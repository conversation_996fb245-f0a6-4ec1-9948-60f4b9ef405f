const mongoose = require("mongoose");

let Debts = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'MonetaryCorrection',
    fields: {
      certifiers:{
        type: [mongoose.SchemaTypes.ObjectId],
        required: true,
        ref: 'Certifiers'
      },
      types: {
        type: [String],
        required: true
      },
      objectCertifiers: {
        type: [{
          _id: mongoose.SchemaTypes.ObjectId,
          name: String
        }]
      },
      modality: {
        type: String,
        enum: [
            'online',
            'presential'
        ],
        required: true,
      },
      correction: {
        type: Number,
        required: true
      },
      month: {
        type: Number,
        required: true,
      },
      year: {
        type: Number,
        required: true,
      }
    },
    indexes: [
      {
        fields: {
          type: 1,
          year: 1,
          month: 1,
        },
      }
    ],
    options: {
      timestamp: true
    }
  }
};

module.exports = Debts;
