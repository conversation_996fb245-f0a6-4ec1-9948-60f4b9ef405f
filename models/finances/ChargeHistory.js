const mongoose = require('mongoose');
const ChargeHistory = {
  database: {
    collection: 'ChargeHistory',
    fields: {
      _chargeId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userType: {
        type: String,
        required: true,
        enum: [
          'student',
          'partner',
          'teacher',
          'employer',
          'computer',
          'broker',
        ],
      },
      _userName: {
        type: String,
        required: true,
      },
      description: String,
      metadata: mongoose.SchemaTypes.Mixed,
    },
    options: {
      timestamps: true,
    },
  },
};
module.exports = ChargeHistory;
