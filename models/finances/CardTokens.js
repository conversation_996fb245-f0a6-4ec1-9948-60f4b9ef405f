let CardTokens =  {
    functions: {
        maskCard: function (next) {
            this.number = `**********${this.number.substr(this.number.length - 4, 4)}`;

            return next();
        }
    },
    database: {
        collection: 'CardTokens',
        connection: 'database_payments',
        fields: {
            establishment: {
                type: String,
                // required: false
            },
            merchantId: {
                type: String,
                required: true
            },
            cpf: {
                type: String,
                required: true
            },
            name: {
                type: String,
                required: true
            },
            holder: {
                type: String,
                required: true
            },
            type: {
                type: String,
                required: true
            },
            number: {
                type: String,
                required: true
            },
            brand: {
                type: String,
                required: true
            },
            expiry: {
                type: String,
                required: true
            },
            cvc: {
                type: String,
                required: true
            },
            valid: {
                type: Boolean,
                // required: false,
                default: false
            },
            isEnabled: {
                type: Boolean,
                // required: false,
                default: true
            },
            lastCheck: Date,
            token: String,
            operator: String
        }
    }
};

CardTokens.database.pre = {
    save: [
        CardTokens.functions.maskCard,
    ],
    create: [
        CardTokens.functions.maskCard,
    ]
};

module.exports = CardTokens;
