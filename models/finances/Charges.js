const mongoose = require('mongoose');
const { _getModels } = require('../../services/Utils');

const populateEnrolmentRelatedFields = async (models, charge, chargeRef) => {
  const { Enrolments, Students, Users } = models;
  const enrolments = await Enrolments.find({
    _id: {
      $in: Array.isArray(charge._enrolmentId) ? charge._enrolmentId : [charge._enrolmentId],
    },
  });
    // fixme paliativo, devido a enrolment esta na transaction, nao acha logo, da erro na hora de encontrar o cpf do aluno.
  const cpf = ((enrolments || [])[0] || {}).cpf
  if (cpf) {
    const student = await Students.findOne({ cpf: enrolments[0].cpf });
    if (student) {
      const user = await Users.findById(student._userId);

      if (!Object.prototype.hasOwnProperty.call(charge, 'enrolments')) {
        chargeRef.set({
          enrolments: enrolments.map(e => ({
            _id: e._id,
            _name: e.registryCourse.course._name,
            _certifierName: e.registryCourse.course._certifierName,
            _typeName: e.registryCourse.course._typeName,
          })),
        });

        if ([
          'monthly',
          'rate-enrolment',
          'rate-enrolment-monthly',
        ].includes(charge._chargeTypeAlias)) {
          chargeRef.set({
            indication: enrolments[0].indication,
          });
        }
      }

      if (!Object.prototype.hasOwnProperty.call(charge, 'student')) {
        chargeRef.set({
          student: {
            name: student.name,
            email: user.email,
            cellPhone: student.cellPhone,
            cpf: student.cpf,
            address: student.address,
          },
        });
      }
    }

  }
};

let Charges = {
  functions: {
    parseEnrolmentId: function (next) {
      try {
        if (Array.isArray(this._enrolmentId)) {
          this._enrolmentId = this._enrolmentId.map(id => new mongoose.Types.ObjectId(id));
        } else {
          this._enrolmentId = new mongoose.Types.ObjectId(this._enrolmentId);
        }
        next();
      } catch (e) {
        console.error(e);
        next(e);
      }
    },
    populateEnrolmentRelatedFieldsOnSave: async function (next) {
      try {
        const models = _getModels.call(this, 'Charges');
        const charge = this;

        await populateEnrolmentRelatedFields(models, charge, this);

      } catch (err) {
        console.error(err);
        next();
      } finally {
        next();
      }
    },
    populateEnrolmentRelatedFieldsOnFindOneAndUpdate: async function (next) {
      try {
        const models = _getModels.call(this, 'Charges');
        const { Charges } = models;
        const charge = await Charges.findOne(this.getQuery());

        await populateEnrolmentRelatedFields(models, charge, this);

      } catch (err) {
        console.error(err);
        next();
      } finally {
        next();
      }
    },
  },
  database: {
    collection: 'Charges',
    fields: {
      _enrolmentId: {
        type: mongoose.SchemaTypes.Mixed,
        required: true,
        index: true,
      },
      _enrolmentRenegociationId: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
        index: true,
      },
      _renegociationId: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
        allowNull: true,
        default: null,
      },
      _chargeTypeAlias: {
        type: String,
        required: true,
        index: true,
      },
      installment: {
        type: Number,
        required: true,
      },
      amount: {
        type: Number,
        required: true,
      },
      originalAmount: {
        type: Number,
        // required: false,
        allowNull: true,
      },
      dueDate: {
        type: Date,
        required: true,
        index: true,
      },
      paymentDate: Date,
      status: {
        type: String,
        required: true,
        enum: [
          'waiting_payment',
          'paid',
          'canceled',
          'free',
          'blocked_by_reenrolment',
        ],
        default: 'waiting_payment',
        index: true,
      },
      amountPaid: Number,
      metadata: mongoose.SchemaTypes.Mixed,
      student: {
        name: {
          type: String,
          // required: false,
        },
        email: {
          type: String,
          // required: false,
        },
        cellPhone: {
          type: String,
          // required: false,
        },
        cpf: {
          type: String,
          // required: false,
        },
        address: {
          street: {
            type: String,
            // required: false,
          },
          number: {
            type: String,
            // required: false,
          },
          complement: {
            type: String,
            // required: false,
          },
          zone: {
            type: String,
            // required: false,
          },
          zip: {
            type: String,
            // required: false,
          },
          city: {
            type: String,
            // required: false,
          },
          uf: {
            type: String,
            // required: false,
          },
        },
      },
      enrolments: {
        // required: false,
        type: [{
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          _name: {
            type: String,
            required: true,
          },
          _certifierName: {
            type: String,
            required: true,
          },
          _typeName: {
            type: String,
            required: true,
          },
        }],
      },
      indication: {
        type: [
          {
            _id: false,
            level: {
              type: String,
              enum: [
                'master',
                'level1',
                'level2',
                'indicator',
              ],
              required: true,
            },
            cpf: {
              type: String,
              required: true,
            },
            userType: {
              type: String,
              required: true,
            },
            commissionMonthly: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionEnrolment: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionType: {
              type: String,
              // required: false,
              default: 'percentage',
              valid: [
                'value',
                'percentage',
              ],
            },
            polo: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  required: true,
                },
                name: {
                  type: String,
                  required: true,
                },
              },
              // required: false
            },
            commissionMonthlyExceptions: {
              type: [
                {
                  _id: false,
                  installment: {
                    type: Number,
                  },
                  commission: {
                    type: Number,
                  },
                },
              ],
              // required: false,
              default: undefined,
            },
          },
        ],
        // required: false,
      },
      _chargeOrderId: {
        type: mongoose.SchemaTypes.ObjectId,
        // required: false,
      },
      discounts: {
        type: [{
          _id: mongoose.SchemaTypes.ObjectId,
          name: String,
          chargeType: [String],
          discountType: String,
          value: Number,
          days: Number,
          useInOverdueCharge: Boolean,
          discountRule: {
            type: String,
            enum: [
              'not_applied',
              'antecipation',
              'fixed_date',
            ],
          },
          observation: String,
        }],
        // required: false
      },
      scholarships: {
        type: [
          {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            chargeType: [String],
            discountType: String,
            value: Number,
            useInOverdueCharge: Boolean,
            useDueDate: Boolean,
            dateStart: Date,
            dateEnd: Date,
          },
        ],
      },
    },
  },
};

Charges.database.pre = {
  save: [
    Charges.functions.parseEnrolmentId,
    Charges.functions.populateEnrolmentRelatedFieldsOnSave,
  ],
  findOneAndUpdate: [
    Charges.functions.populateEnrolmentRelatedFieldsOnFindOneAndUpdate,
  ],
};

module.exports = Charges;
