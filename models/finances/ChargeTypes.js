let ChargeTypes = {
    database: {
        collection: 'ChargeTypes',
        fields: {
            name: {
                type: String,
                required: true
            },
            alias: {
                type: String,
                unique: 'Este tipo de cobrança já existe',
                required: true
            },
            description: {
                type: String,
                required: true
            },
            visibleToProtocol: {
                type: Boolean,
                default: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            methods     : {
                type    : [String],
                // required: false
            },
            renegociation: {
                type: Boolean,
                // required: false,
                allowNull: true,
                default: false
            },
            blockManualCreateUpdate: {
                type: Boolean,
                // required: false,
                allowNull: true,
                default: false
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    alias: 1
                },
                options: {
                    unique: 'Este tipo de cobrança já existe',
                    name  : 'dup_alias'
                }
            }
        ]
    }
};
module.exports = ChargeTypes;
