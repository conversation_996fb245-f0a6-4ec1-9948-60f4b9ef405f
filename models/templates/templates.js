let Templates = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Templates',
        fields: {
            name: {
                type        : String,
                lowercase   : true,
                required    : true,
                index       : true
            },
            templateKeys    : [
                {
                    name     : {
                        type : String,
                        minlength: 1,
                        index    : true
                    },
                    description: String
                }
            ],
            _templateTypeAlias: {
                type    : String,
                required: true
            },
            isEnabled: {
                type     : Boolean,
                required : true,
                default  : false
            }
        }
    }
};

module.exports = Templates;
