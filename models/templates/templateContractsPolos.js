const mongoose = require('mongoose');

let templateContractsPolos = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TemplateContractsPolos',
        fields    : {
            name: {
                type    : String,
                required: true
            },
            content: {
                type    : String,
                required: true
            },
            _certifierName: {
                type     : String
            },
            _typeName: {
                type     : String
            },
            isActive: {
                type     : Boolean,
                required : true,
                default  : true
            },
            isVisible: {
                type     : Boolean,
                required : true,
                default  : true
            },
            typeContract: {
                type   : String,
                enum   : [
                    'contrato',
                    'aditivo'
                ],
                default: 'contrato'
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed
            }
        }
    }
};

module.exports = templateContractsPolos;