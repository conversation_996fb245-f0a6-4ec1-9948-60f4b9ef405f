let CommunicationModels = {
    functions: {},
    database: {
        collection: 'CommunicationModels',
        connection: 'database_piaget',
        fields    : {
            name: {
                type    : String,
                required: true
            },
            subject: {
                type    : String,
                required : false,
                allowNull: true
            },
            type: {
                type    : String,
                required : true
            },
            description: {
                type    : String,
                default : true
            },
            communication_model: {
                type     : String,
                required : true,
            },
            isActive: {
                type     : Boolean,
                required : true,
                default  : false
            },
            certifier: {
                type     : String,
                required : true,
                default  : 'all',
            },
        }
    }
};

module.exports = CommunicationModels;
