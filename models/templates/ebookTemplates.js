const EbookTemplates = {
  functions: {},
  database: {
    collection: 'EbookTemplates',
    connection: 'database_piaget',
    fields: {
      name: {
        type: String,
        required: true,
      },
      certifier: {
        type: [String],
        required: true,
        default: ['all'],
      },
      courseTypes: {
        type: [String],
        default: ['all'],
      },
      description: {
        type: String,
        required: false,
      },
      file: {
        type: String,
        required: false,
      },
      typeFile: {
        type: String,
        required: false,
      },
      thumbnail: {
        type: String,
        // required: false
      },
      isActive: {
        type: Boolean,
        required: false,
      },
    },
  },
};

module.exports = EbookTemplates;
