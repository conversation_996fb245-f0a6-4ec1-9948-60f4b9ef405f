const mongoose = require('mongoose');

const ChatRoom = {
  functions: {},
  database: {
    collection: 'ChatRoom',
    connection: 'database_piaget',
    fields: {
      _enrolment: {
        _enrolmentId: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        _courseName: {
          type: String,
        },
        _courseId: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        _classId: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        _className: {
          type: String,
        },
        _discId: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        _discName: {
          type: String,
        },
        _groupingId: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        _groupingName: {
          type: String,
        },
        _typeName: {
          type: String,
        },
      },
      participants: [
        {
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
            index: true,
          },
          _userName: String,
          _userType: {
            type: String,
            enum: [
              'student', // <PERSON><PERSON>
              'partner', // <PERSON><PERSON><PERSON>
              'teacher', // Professor
              'employer', // <PERSON><PERSON><PERSON><PERSON>
              'seller', // Vendedor
            ],
            required: true,
          },
        },
      ],
      log: [],
    },
    options: {
      timestamps: true,
    },
    pre: {},
    post: {},
    indexes: [
      {
        fields: {
          '_enrolment._id': 1,
          '_enrolment._classId': 1,
          '_enrolment._courseId': 1,
          '_enrolment._discId': 1,
          '_enrolment._groupingId': 1,
          '_enrolment._typeName': 1,
        },
        options: {},
      },
    ],
  },
};
module.exports = ChatRoom;
