const mongoose = require('mongoose');

const ChatRoomMessage = {
  functions: {},
  database: {
    collection: 'ChatRoomMessage',
    connection: 'database_piaget',
    fields: {
      _chatRoomId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _user: {
        _userId: {
          type: mongoose.SchemaTypes.ObjectId,
          required: true,
        },
        _userName: {
          type: String,
          required: true,
        },
        _userType: {
          type: String,
          enum: [
            'student', // <PERSON><PERSON>
            'partner', // <PERSON><PERSON><PERSON>
            'teacher', // Professor
            'employer', // Funcionário
            'seller', // Vendedor
          ],
          required: true,
        },
      },
      message: {
        type: String,
        required: false,
      },
      hasRead: {
        type: Boolean,
        default: false,
      },
      ipSending: {
        type: String,
        required: true,
      },
      files: [
        {
          url: {
            type: String,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
          type: {
            type: String,
            required: true,
          },
        },
      ],
      logs: {
        type: [mongoose.Schema.Types.Mixed],
        default: [],
      },
    },
    options: {
      timestamps: true,
    },
    pre: {},
    post: {},
    indexes: [
      {
        fields: {
          _chatRoomId: 1,
          '_user._id': 1,
        },
        options: {},
      },
    ],
  },
};
module.exports = ChatRoomMessage;
