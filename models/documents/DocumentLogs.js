const mongoose = require('mongoose');

let DocumentLogs = {
    database: {
        collection: 'DocumentLogs',
        fields: {
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            cpf: {
                type: String,
                // required: false
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            after: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            before: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            documentId: {
                type: mongoose.SchemaTypes.ObjectId,
            }
        }
    }
};

module.exports = DocumentLogs;
