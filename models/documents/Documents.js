const mongoose =  require('mongoose');
const moment = require('moment');
const { _getModels, sendDiscord }  = require('../../services/Utils');
const {SchemaTypes} = require("mongoose");
const _ = require('lodash');

let Documents = {
    functions: {
        updateEnrolments: async function(document, next) {
            try {
                const models = _getModels.call(this, 'Documents');
                const { Enrolments, Students } = models;
                const student = await Students.findOne({
                    cpf: document.cpf
                });

                const enrolments = await Enrolments.find({
                    cpf: document.cpf,
                    documents: {
                        $elemMatch: {
                            _documentTypeId: document._documentTypeId,
                        }
                    }
                }, {
                    documents: 1,
                    'registryCourse.course._typeName': 1,
                });

                if (student) {
                    const hasDocument = student.documents.find(item => item._documentTypeId.equals(document._documentTypeId));

                    if (hasDocument) {
                        hasDocument.status = document.status;
                        hasDocument.updatedAt = document.updatedAt;
                        hasDocument.courseTypes = _.uniq(enrolments.map(item => item.registryCourse.course._typeName));
                    } else {
                        student.documents.push({
                            name: document._documentTypeName,
                            _documentTypeId: document._documentTypeId,
                            status: document.status,
                            createdAt: document.createdAt,
                            updatedAt: document.updatedAt,
                            _studentDocumentId: document._id,
                            courseTypes: _.uniq(enrolments.map(item => item.registryCourse.course._typeName)),
                        });
                    }

                    student.markModified('documents');
                    await student.save();
                }

                (async () => {
                    for (const enrolment of enrolments) {
                        try {
                            const docToUpdate = enrolment.documents.find(item => item._documentTypeId.equals(document._documentTypeId));
                            docToUpdate.status = document.status;
                            docToUpdate.createdAt = document.createdAt;
                            docToUpdate.updatedAt = document.updatedAt;
                            docToUpdate._studentDocumentId = document._id;
                            enrolment.markModified('documents');
                            await enrolment.save();
                        } catch (err) {
                            sendDiscord('alerta-trigger-documentos', `
                                Erro ao tentar atualizar a enrolment.
                                Aluno: ${this.cpf} - Matrícula: ${enrolment._id},
                                Documento: ${document._documentTypeId},
                                erro: ${err.toString()}
                            `);
                        }
                    }
                })()
            } catch(err) {
                try {
                    sendDiscord('alerta-trigger-documentos', `
                        Erro ao tentar atualizar o objeto documents da enrolment e students.
                        Aluno: ${this.cpf},
                        Documento: ${document._documentTypeId},
                        erro: ${err.toString()}
                    `);
                } catch (error) {
                    console.log(error);
                }
            } finally {
                next();
            }
        },
        updateCertificateSolicitations: async function(document, next) {
            try {
                const models = _getModels.call(this, 'Documents');
                const { Enrolments, CertificateSolicitations } = models;

                const enrolments = await Enrolments.find({
                    cpf: document.cpf,
                    documents: {
                        $elemMatch: {
                            _documentTypeId: document._documentTypeId,
                        }
                    }
                });
                (async () => {
                    for (const enrolment of enrolments) {
                        try {
                            const requiredDocs = enrolment.documents.filter(doc => doc.isRequired);
                            const isDocumentsApproved = requiredDocs.every(requiredDoc => requiredDoc.status === 'approved');
                            const openSolicitations = await CertificateSolicitations.find({
                                _enrolmentId: enrolment._id,
                                status: 'waiting_approval',
                            });
    
                            for (const solicitation of openSolicitations) {
                                solicitation.dataStudent.sentDocuments = isDocumentsApproved;
    
                                if (solicitation && solicitation.metadata && solicitation.metadata.historyChecks) {
                                    solicitation.metadata.historyChecks.push({
                                        date: moment().toDate(),
                                        description: `Documents Model Hook: Setou sentDocuments como ${isDocumentsApproved.toString()}.`,
                                    })
                                } else {
                                    solicitation.metadata.historyChecks = [{
                                        date: moment().toDate(),
                                        description: `Documents Model Hook: Setou sentDocuments como ${isDocumentsApproved.toString()}.`,
                                    }];
                                }

                                solicitation.markModified('metadata');
                                await solicitation.save();
                            }
                        } catch (err) {
                            sendDiscord('alerta-trigger-documentos', `
                                Trigger: updateCertificateSolicitations,
                                Model: Documents,
                                Erro ao tentar atualizar as solicitações de certificado.
                                Aluno: ${this.cpf} - ${enrolment._id},
                                erro: ${err.toString()}
                            `);
                        }
                    }
                })()
            } catch(err) {
                console.error(err);
                sendDiscord('alerta-trigger-documentos', `
                    Trigger: updateCertificateSolicitations,
                    Model: Documents,
                    Erro ao tentar atualizar as solicitações de certificado.
                    Aluno: ${this.cpf}
                    erro: ${err.toString()}
                `);
            } finally {
                next();
            }
        },
    },
    database: {
        collection: 'Documents',
        fields: {
            _documentTypeName: {
                type: String,
                index: true,
                required: true
            },
            cpf: {
              type: String,
              index: true,
              required: false
            },
            _studentId: {
              type: SchemaTypes.ObjectId,
              index: true,
              required: function () {
                return !this.cpf;
              }
            },
            archive: {
                type: [String],
                required: true
            },
            status: {
                type: String,
                required: true,
                enum: [
                    'analysis', // Em análise status inicial
                    'waiting_copy_original_graduation', // Aguardando cópia original de graduação (quando o aluno ainda não tem o diploma, somente uma declaração)
                    'divergent', // Documento divergente ou ilegível
                    'approved', // Documento aprovado (status final compartilhado pelas certificadoras e tipos de cursos)
                    'awaiting_authenticated_copy_by_mail', // Aguardando cópia autenticada pelos correios **
                    'waiting'
                ]
            },
            isAuthenticatedByCertifiers: {
                type: [String],
                // required: false,
                default: []
            },
            _documentTypeId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: true, TODO: MUDAR PRA REQUIRED POSTERIORMENTE DPS QUE ESTIVER TUDO OK
            },
            history         : [
                {
                    status: {
                        type: String,
                        required: true,
                        enum: [
                            'analysis', // Em análise status inicial
                            'waiting_copy_original_graduation', // Aguardando cópia original de graduação (quando o aluno ainda não tem o diploma, somente uma declaração)
                            'divergent', // Documento divergente ou ilegível
                            'approved', // Documento aprovado (status final compartilhado pelas certificadoras e tipos de cursos)
                            'awaiting_authenticated_copy_by_mail', // Aguardando cópia autenticada pelos correios **
                            'waiting'
                        ]
                    },
                    description: {
                        type: String,
                        required: true
                    },
                    archive: {
                        type: [String]
                    },
                    _userId: {
                        type: mongoose.Schema.ObjectId,
                        required: true
                    },
                    _userType: {
                        type: String,
                        required: true,
                        enum    : [
                            'student',
                            'partner',
                            'teacher',
                            'employer',
                            'computer'
                        ]
                    },
                    _userName: {
                        type: String,
                        required: true
                    },
                    launchedAt: {
                        type    : Date,
                        required: true
                    },
                    historyEdit: [
                        {
                            status: {
                                type: String,
                                required: true,
                                enum: [
                                    'analysis', // Em análise status inicial
                                    'waiting_copy_original_graduation', // Aguardando cópia original de graduação (quando o aluno ainda não tem o diploma, somente uma declaração)
                                    'divergent', // Documento divergente ou ilegível
                                    'approved', // Documento aprovado (status final compartilhado pelas certificadoras e tipos de cursos)
                                    'awaiting_authenticated_copy_by_mail', // Aguardando cópia autenticada pelos correios **
                                    'waiting'
                                ]
                            },
                            description: {
                                type: String,
                                required: true
                            },
                            archive: {
                                type: [String]
                            },
                            _userId: {
                                type: mongoose.Schema.ObjectId,
                                required: true
                            },
                            _userType: {
                                type: String,
                                required: true,
                                enum    : [
                                    'student',
                                    'partner',
                                    'teacher',
                                    'employer',
                                    'computer'
                                ]
                            },
                            _userName: {
                                type: String,
                                required: true
                            },
                            launchedAt: {
                                type    : Date,
                                required: true
                            },
                        }
                    ]
                }
            ],
            needSignTerm: {
                type: Boolean,
                default: false
            }
        }
    }
};

Documents.database.post = {
    save: [
        Documents.functions.updateEnrolments,
        Documents.functions.updateCertificateSolicitations,
    ],
    findOneAndUpdate: [
        Documents.functions.updateEnrolments,
        Documents.functions.updateCertificateSolicitations,
    ],
};

module.exports = Documents;
