const mongoose = require('mongoose');

let Teams = {
    functions: {},
    database: {
        collection: 'Teams',
        connection: 'database_leads',
        fields: {
            name: {
                type: String,
                required: true,
                unique: 'duplicate_team'
            },
            location: {
                state: {
                    type: String,
                    required: true
                },
                city: {
                    type: String,
                    required: true
                }
            },
            commission: {
                type: Number,
                required: true
            },
            distribuationRules: [
                {
                    _id: false,
                    interest: {
                        type: {
                            course: {
                                certifier: [String],
                                type: [String]
                            }
                        }
                    }
                }
            ],
            _brokerManagerId: [mongoose.SchemaTypes.ObjectId],
            canReceivePendingEnrolmentsFromSite: {
                type: Boolean,
                default: false
            },
            isActive: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = Teams;
