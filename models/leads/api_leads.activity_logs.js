const mongoose = require('mongoose');

let ActivityLogs = {
    functions: {},
    database: {
        collection: 'ActivityLogs',
        connection: 'database_leads',
        fields    : {
            activityType : {
                type    : String,
                enum    : [
                    'buttonClick',
                    'contentViewed',
                    'contentLeaved',
                    'pageScroll'
                ],
                required: true
            },
            buttonClick  : {
                type    : {
                    btnLabel: {
                        type    : String,
                        // required: false
                    }
                },
                // required: false
            },
            contentViewed: {
                type    : {
                    name: {
                        type    : String,
                        // required: false
                    }
                },
                // required: false
            },
            pageScroll   : {
                type    : {
                    from: {
                        type    : String,
                        // required: false
                    },
                    to  : {
                        type    : String,
                        // required: false
                    }
                },
                // required: false
            },
            userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            ip           : String,
            url          : String,
            metadata     : mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = ActivityLogs;
