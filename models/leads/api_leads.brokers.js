const mongoose = require('mongoose');

let Brokers = {
    functions: {},
    database: {
        collection: 'Brokers',
        connection: 'database_leads',
        fields    : {
            name       : {
                type    : String,
                required: true
            },
            isPaused : {
                type: Boolean,
                default: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            cpf        : {
                type    : String,
                required: true,
                unique  : 'duplicate_broker'
            },
            location   : {
                state: {
                    type    : String,
                    required: true
                },
                city : {
                    type    : String,
                    required: true
                }
            },
            score      : {
                type    : Number,
                default : 1
            },
            dailyLeads      : {
                type    : Number,
                required: true,
                default : 0
            },
            numbersLeadsOfDay  : {
                type    : Number,
                required: true,
                default : 0
            },
            _teamId    : [mongoose.SchemaTypes.ObjectId],
            piaget     : {
                _employerId: mongoose.SchemaTypes.ObjectId
            },
            commissions: [
                {
                    _id       : false,
                    certifier : {
                        type    : String,
                        required: true
                    },
                    type      : {
                        type    : String,
                        required: true
                    },
                    tracks: [
                        {
                            _id       : false,
                            goal      : {
                                min: {
                                    type    : Number,
                                    // required: false
                                },
                                max: {
                                    type    : Number,
                                    // required: false
                                }
                            },
                            commission: {
                                type    : Number,
                                required: true
                            },
                            chargeType: {
                                type: String,
                                // required: false
                            }
                        }
                    ],
                    bonusCourseValue: {
                        min: {
                            type    : Number,
                            // required: false
                        },
                        mid: {
                            type    : Number,
                            // required: false
                        },
                        max: {
                            type    : Number,
                            // required: false
                        }
                    },
                    bonusPaymentType: [
                        {
                            _id        : false,
                            paymentType: {
                                type    : String,
                                // required: false
                            },
                            bonus: {
                                type    : Number,
                                // required: false
                            }
                        }
                    ]
                }
            ],
            limitLeadsOfDay: {
                type: Number,
                // required: false
            },
            limitMinimum: {
                type: Number,
                // required: false
            },
            queue: {
                type: Number,
                // required: false,
                default: 0
            },
            canSelectLead: {
                type: Boolean,
                default: false
            },
            canReceiveLead: {
                type: Boolean,
                default: true
            },
            isSdr: {
                type: Boolean,
                default: false
            },
            completeQueue: {
                type: Date,
                // required: false
            },
            isMostValueBroker: {
                type: Boolean
            },
            positionQueue: {
                type: Number
            },
            totalNewLead: {
                type: Number
            },
            distribuationRules: [
                {
                    _id: false,
                    interest: {
                        type: {
                            course: {
                                certifier: [String],
                                type: [String]
                            }
                        }
                    }
                }
            ],
            locations: [
                {
                    state: {
                        type: String,
                        required: true
                    },
                    city: {
                        type: String,
                        required: true
                    }
                }
            ],
        }
    }
};

module.exports = Brokers;
