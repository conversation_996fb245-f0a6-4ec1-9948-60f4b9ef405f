let Origins = {
    functions: {},
    database: {
        collection: 'Origins',
        connection: 'database_leads',
        fields    : {
            name    : {
                type    : String,
                required: true,
                trim: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisibleToBroker:{
                type: Boolean,
                default: true
            }
        },
        indexes   : [
            {
                fields : {
                    'name' : 1
                },
                options: {
                    unique: 'Origem já existe.'
                }
            }
        ]
    }
};

module.exports = Origins;