const mongoose = require('mongoose');

let IndicationCoursesFree = {
    functions: {
    },
    database: {
        collection: 'IndicationCoursesFree',
        connection: 'database_piaget',
        fields: {
            name: String,
            email: {
                type: String
            },
            cellPhone: String,
            indication: {
                type: {
                    name: String,
                    cpf: String
                },
                required: true
            },
            useVoucher: {
                type   : Boolean,
                default: false
            },
            voucherReleased: {
                type   : Boolean,
                default: false
            },
            enrolmentData: {
                type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    courseName: String,
                    typeName: String,
                    certifierName: String,
                    indication: {
                        type: [
                            {
                                _id: false,
                                level: {
                                    type: String,
                                    enum: [
                                        'master',
                                        'level1',
                                        'level2',
                                        'indicator'
                                    ],
                                    required: true
                                },
                                cpf: {
                                    type: String,
                                    required: true
                                },
                                userType: {
                                    type: String,
                                    required: true
                                },
                                commissionMonthly: {
                                    type: Number,
                                    // required: false,
                                    default: 0
                                },
                                commissionEnrolment: {
                                    type: Number,
                                    // required: false,
                                    default: 0
                                },
                                commissionType: {
                                    type: String,
                                    // required: false,
                                    default: 'percentage',
                                    valid: [
                                        'value',
                                        'percentage'
                                    ]
                                },
                                polo: {
                                    type: {
                                        _id: {
                                            type: mongoose.SchemaTypes.ObjectId,
                                            required: true,
                                        },
                                        name: {
                                            type: String,
                                            required: true,
                                        },
                                    },
                                    // required: false
                                },
                                commissionMonthlyExceptions: {
                                    type: [
                                        {
                                            _id: false,
                                            installment: {
                                                type: Number
                                            },
                                            commission: {
                                                type: Number
                                            },
                                        }
                                    ],
                                    // required: false,
                                    default: undefined,
                                }
                            }
                        ],
                        // required: false,
                        default: []
                    }
                }
            },
            metadata: mongoose.SchemaTypes.Mixed,
            link: String
        },
        pre: {}
    }
};

module.exports = IndicationCoursesFree;
