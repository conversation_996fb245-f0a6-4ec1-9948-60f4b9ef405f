const mongoose = require('mongoose');

let CallbackFacebook = {
    functions: {},
    database: {
        collection: 'CallbackFacebook',
        connection: 'database_leads',
        fields: {
            method: mongoose.SchemaTypes.Mixed,
            body: mongoose.SchemaTypes.Mixed,
            params: mongoose.SchemaTypes.Mixed,
            query: mongoose.SchemaTypes.Mixed,
            headers: mongoose.SchemaTypes.Mixed,
            isProcessed: {
                type: Boolean,
                // required: false,
                default: false
            },
            isIgnored: {
                type: Boolean,
                // required: false,
                default: false
            }
        }
    }
};

module.exports = CallbackFacebook;
