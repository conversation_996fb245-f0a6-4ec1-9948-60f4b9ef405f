const mongoose = require('mongoose');

let LeadsLogs = {
    functions: {},
    database: {
        collection: 'LeadsLogs',
        connection: 'database_leads',
        fields: {
            _leadId         : mongoose.SchemaTypes.ObjectId,
            status          : String,
            _brokerId       : mongoose.SchemaTypes.ObjectId,
            scheduledTo     : Date,
            distributedAt   : Date,
            origin          : {
                _id             : false,
                date            : Date,
                name            : String,
                utm             : {
                    source          : String,
                    campaign        : String,
                    medium          : String,
                    term            : String,
                    content         : String,
                    gclid           : String,
                    fbclid          : String,
                }
            },
            interest        : {
                date            : Date,
                isDone          : Boolean,
                payment         : {
                    paid            : Boolean,
                },
                course          : {
                    system          : String,
                    certifier       : String,
                    area            : String,
                    name            : String,
                    type            : {
                        type            : String,
                    },
                },
            },
            address              : {
                street    : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                number    : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                complement: {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                zone      : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                zip       : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                city      : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                uf        : {
                    type     : String,
                    required : false,
                    allowNull: false
                }
            },
            activity          : {
                _id                 : mongoose.SchemaTypes.ObjectId,
                _activityName       : String,
                observation         : String,
                observationImage    : String,
                type                : {
                    type                : String,
                },
            }
        }
    }
};

module.exports = LeadsLogs;
