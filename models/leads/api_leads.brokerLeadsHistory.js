const mongoose = require('mongoose');

let BrokerLeadsHistory = {
    functions: {},
    database: {
        collection: 'BrokerLeadsHistory',
        connection: 'database_leads',
        fields    : {
            date: {
                type    : Date,
                required: true,
            },
            _brokerId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true,
            },
            cpf: {
                type    : String,
                required: true,
            },
            name: {
                type    : String,
                required: true,
            },
            positionQueue: {
                type: Number
            },
            totalNewLead: {
                type: Number
            },
            score: {
                type: Number,
            },
            dailyLeads: [
                {
                    _id: {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true,
                    },
                    name: {
                        type: String,
                    },
                    email: {
                        type: String,
                    },
                    cellPhone: {
                        type: String,
                    },
                    whatsApp: {
                        type: String,
                    }
                }
            ],
            otherLeads: [
                {
                    _id: {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true,
                    },
                    name: {
                        type: String,
                    },
                    email: {
                        type: String,
                    },
                    cellPhone: {
                        type: String,
                    },
                    whatsApp: {
                        type: String,
                    }
                }
            ],
        }
    }
};

module.exports = BrokerLeadsHistory;
