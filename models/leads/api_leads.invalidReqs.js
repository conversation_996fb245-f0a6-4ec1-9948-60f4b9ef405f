const mongoose = require('mongoose');

let InvalidReqs = {
    functions: {},
    database: {
        collection: 'InvalidReqs',
        connection: 'database_leads',
        fields    : {
            headers: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body   : {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            err    : {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};

module.exports = InvalidReqs;