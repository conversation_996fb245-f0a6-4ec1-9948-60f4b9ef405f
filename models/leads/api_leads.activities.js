let Activities = {
    functions: {},
    database: {
        collection: 'Activities',
        connection: 'database_leads',
        fields    : {
            name    : {
                type    : String,
                required: true,
                unique  : 'duplicate_activity'
            },
            onLaunch: {
                isObservationRequired: {
                    type    : Boolean,
                    required: true
                },
                changeStatusTo       : {
                    type: String,
                    enum: [
                        'lead',
                        'waiting_broker',
                        'waiting_rate_enrolment',
                        'in_progress',
                        'finished'
                    ]
                },
                permissionOfManager: {
                    type: Boolean,
                    default: false
                },
                convertLead: {
                    type: Boolean,
                    default: false
                }
            },
            isActive: {
                type: Boolean,
                default: true
            },
            _tabulationSonax: {
                type: Number,
                // required: false
            }
        }
    }
};

module.exports = Activities;
