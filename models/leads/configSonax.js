let ConfigSonax = {
    functions: {},
    database: {
        collection: 'ConfigSonax',
        connection: 'database_leads',
        fields    : {
            userPABXVirtual : {
                type    : String,
                required: true
            },
            token : {
                type: String,
                required: true
            },
            codeClient :{
                type: Number,
                required: true
            },
            queue : {
                type: Array
            },
            campaing: {
                type: Array
            }
        }
    }
}

module.exports = ConfigSonax;
