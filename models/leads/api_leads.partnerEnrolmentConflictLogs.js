const mongoose = require('mongoose');

let PartnerEnrolmentConflictLogs = {
    functions: {},
    database: {
        collection: 'PartnerEnrolmentConflictLogs',
        connection: 'database_leads',
        fields: {
            student         : {
                name            : String,
                cpf             : String,
                email           : String,
                phoneNumbers    : [String],
            },
            partner         : {
                name            : String,
                cpf             : String,
            },
            origin          : String,
            matchingLeads   : mongoose.SchemaTypes.Mixed,
            _enrolmentId    : mongoose.SchemaTypes.Mixed,
        }
    }
};

module.exports = PartnerEnrolmentConflictLogs;
