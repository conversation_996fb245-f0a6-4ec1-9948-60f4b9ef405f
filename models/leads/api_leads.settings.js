const mongoose = require('mongoose');

let Settings = {
    functions: {},
    database: {
        collection: 'Settings',
        connection: 'database_leads',
        fields    : {
            name    : {
                type    : String,
                required: true,
                unique  : 'duplicate_settings'
            },
            description    : {
                type    : String,
                required: true
            },
            isActive: {
                type: Boolean,
                required: true
            },
            data    : {
                type    : mongoose.Schema.Types.Mixed,
                required: true
            }
        }
    }
};

module.exports = Settings;
