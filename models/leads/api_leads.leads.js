const mongoose = require('mongoose');
const notInformed = 'Não informado';

const removeSpecialCharacter = str => {
    return str
        .toLowerCase()
        .normalize('NFD')
        .replace(/([\u0300-\u036f]|[^0-9a-zA-Z])/g, '');
}

const fixCertifier = certifier => {
    const str = removeSpecialCharacter(certifier);

    if (str.includes('unica')) return 'Faculdade ÚNICA';
    else if (str.includes('prominas')) return 'Faculdade Prominas';
    else if (str.includes('acesita')) return 'Faculdade Acesita';
    else if (str.includes('ucam') || str.includes('candidomendes')) return 'Universidade Candido Mendes';

    return notInformed;
}

const fixModality = modality => {
    const str = removeSpecialCharacter(modality);

    if (str.includes('posgraduacao')) return 'Pós-Graduação';
    else if (str.includes('graduacaoead')) return 'Graduação - EaD';
    else if (str.includes('segundagraduacao')) return 'Segunda Graduação';
    else if (str.includes('cursogratuito')) return 'Curso Gratuito';
    else if (str.includes('extensao')) return 'Extensão';
    else if (str.includes('graduacaopresencial')) return 'Graduação Presencial';
    else if (str.includes('aperfeicoamento')) return 'Aperfeiçoamento';
    else if (str.includes('segundalicenciatura')) return 'Segunda Licenciatura';
    else if (str.includes('licenciatura')) return 'Licenciatura';
    else if (str.includes('isolada')) return 'Disciplina Isolada';
    else if (str.includes('cursos') && str.includes('eja')) return 'Cursos Técnicos e EJA';
    else if (str === 'graduacao') return 'Graduação';

    return notInformed;
}

const fixEmail = email => {
    let newEmail = (email || '').trim().toLowerCase();

    if (newEmail.slice(-1) === '.')
        newEmail = newEmail.substr(0, newEmail.length - 1);

    if (newEmail.slice(-3) === ' br')
        newEmail = newEmail.substr(0, newEmail.length - 3) + '.br';

    return newEmail;
};

const addMessageError = (messageError, origin = notInformed) => {
    return {
        metadata: {
            errorTask:
                {
                    messageError,
                    origin,
                    date: (new Date()).toISOString()
                }
        }
    }
};

/*
    Entenda o score:
    0 - informou seus dados basicos, nao iniciou nenhuma inscricao, nao possui nenhum interesse por enquanto - deve ser "esquentado na RD" de acordo com a origem do lead
    1 - informou seus dados basicos e escolheu o curso - deve cair na distribuicao com baixa prioridade
    2 - informou seus dados basicos, escolheu o curso e dados pessoais - deve cair na distribuicao com mais prioridade
    3 - informou seus dados basicos, escolheu o curso, dados pessoais e plano de pagamento, nao concluiu inscricao - deve cair na distribuicao prioridade alta
    4 - informou seus dados basicos, escolheu o curso, dados pessoais, plano de pagamento e conclui inscricao - deve cair na distribuicao prioridade altissima
*/
let Leads = {
    functions: {
        fixData: function(next) {
            const lead = this;
            let error = false,
            messageError = 'Erro ao salvar o lead na central';
            let isBlip = false;
            let hasOrigin = false;
            const origin = lead.origins && lead.origins.length ? lead.origins[lead.origins.length - 1] : {};
            const interest = lead.interests && lead.interests.length ? lead.interests[lead.interests.length - 1] : {};

            if (!!origin.name && typeof origin.name === 'string') {
                hasOrigin = true;
                isBlip = removeSpecialCharacter(origin.name).includes('blip');
            }
            if (!!interest.course && typeof interest.course.certifier === 'string') {
                const certifierOld = interest.course.certifier;

                interest.course.certifier = fixCertifier(interest.course.certifier);
                if (!isBlip && interest.course.certifier === notInformed) {
                    error = true;
                    messageError += ' | Certificadora inválida: ' + certifierOld;
                }
            }
            if (!!interest.course && typeof interest.course.type === 'string') {
                const modalityOld = interest.course.type;

                interest.course.type = fixModality(interest.course.type);
                if (!isBlip && interest.course.type === notInformed) {
                    error = true;
                    messageError += ' | Modalidade inválida: ' + modalityOld;
                }
            }
            lead.email = fixEmail(lead.email);
            if (error) {
                if (hasOrigin) {
                    Object.assign(lead, addMessageError(messageError, origin.name));
                } else {
                    Object.assign(lead, addMessageError(messageError));
                }
            }

            return next();
        },
    },
    database: {
        collection: 'Leads',
        connection: 'database_leads',
        fields: {
            origins: [
                {
                    _id: false,
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    name: {
                        type: String,
                        required: true
                    },
                    // Cursos gratuitos, site ucam, site unica, landing page coronga, landing page black friday, etc
                    utm: {
                        source: String,
                        campaign: String,
                        medium: String,
                        term: String,
                        content: String,
                        gclid: String,
                        fbclid: String,
                        campaignId: String
                    }
                }
            ],
            name: String,
            email: {
                type: String
            },
            cellPhone: String,
            cpf: String,
            whatsApp: String,
            location: {
                state: String,
                city: String
            },
            exam: {
                location: {
                    city: String,
                    typeZone: String
                }
            },
            score: {
                type: Number,
                // required: false,
                default: 0
            },
            isFirstAttendance: {
                type: Boolean,
                default: false
            },
            hasFirstAttendance: {
                type: Boolean,
                default: false
            },
            isRedistribute: {
                type: Boolean,
                default: true
            },
            dateStopRedistribute: {
                type: Date
            },
            _brokerId: mongoose.SchemaTypes.ObjectId,
            _brokerName: String,
            _teamId: mongoose.SchemaTypes.ObjectId,
            _contactSonax: Number,
            _teamName: String,
            distributedAt: Date,
            scheduledTo: Date,
            receivedCallDate: Date,
            duplicated: {
                isDuplicated: Boolean,
                original: mongoose.SchemaTypes.ObjectId
            },
            activities: [
                {
                    type: {
                        type: String
                    },
                    _activityName: {
                        type: String,
                        required: true
                    },
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    _brokerId: mongoose.SchemaTypes.ObjectId,
                    _teamId: mongoose.SchemaTypes.ObjectId,
                    observation: String,
                    observationImage: String,
                    metadata: mongoose.SchemaTypes.Mixed,
                    brokerName: String,
                    isAutomatic: {
                        type: Boolean,
                        default: false
                    }
                }
            ],
            interests: {
                type: [
                    {
                        date: {
                            type: Date,
                            // required: false,
                            default: Date.now()
                        },
                        course: {
                            system: {
                                type: String,
                                // required: false,
                                allowNull: true
                            },
                            certifier: {
                                type: String,
                                // required: false,
                                allowNull: true
                            },
                            typeName: {
                                type: String,
                                // required: false,
                                allowNull: true
                            },
                            type: {
                                type: String
                            },
                            area: String,
                            name: String,
                            workload: String,
                            _courseId: String
                        },
                        payment: {
                            method: String,
                            paid: {
                                type: Boolean,
                                // required: false,
                                default: false
                            },
                            enrolment: {
                                installments: Number,
                                dueDate: Date
                            },
                            course: {
                                installments: Number,
                                monthliesDay: Number,
                                value: Number
                            }
                        },
                        enrolmentId: String,
                        isDone: {
                            type: Boolean,
                            // required: false,
                            default: false
                        }
                    }
                ],
                // required: false,
                default: []
            },
            address: {
                street: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                number: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                complement: {
                    type: String,
                    // required: false,
                    allowNull: true
                },
                zone: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                zip: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                city: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                uf: {
                    type: String,
                    // required: false,
                    allowNull: false
                }
            },
            birthDate: {
                type: Date,
                // required: false,
                allowNull: false
            },
            status: {
                type: String,
                enum: [
                    'lead',
                    'waiting_rate_enrolment',
                    'waiting_broker',
                    'in_progress',
                    'finished'
                ],
                // required: false,
                default: 'lead'
            },
            rdStatus: {
                type: String,
                enum: [
                    'sent',
                    'not_sent',
                    'removed'
                ],
                // required: false,
                default: 'not_sent'
            },
            metadata: mongoose.SchemaTypes.Mixed,
            rdStation: [
                {
                    _id: false,
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    tag: {
                        type: String,
                        required: true
                    },
                    event: {
                        type: String,
                        // required: false,
                        default: 'added'
                    }
                }
            ],
            blips: [
                {
                    _id: false,
                    identity: {
                        type: String,
                        // required: false
                    },
                    ticket: {
                        type: String,
                        // required: false
                    },
                    ticketIsActivate: {
                        type: Boolean,
                        default: true,
                        // required: false
                    },
                    sequentialId: {
                        type: Number,
                        // required: false
                    },
                    date: {
                        type: Date,
                        // required: false
                    },
                    company: {
                        type: String,
                        requried: false
                    },
                    department: {
                        type:String,
                        required:false
                    }
                }
            ],
            convertLead: {
                type: Boolean,
                // required: false
            },

            /*
            Dados do metadata (todos opcionais):
            -parametros utm
            -parametros geolocation
            -qualquer dado extra relevante
            */
        },
        pre: {}
    }
};

Leads.database.pre = {
    save: [
        Leads.functions.fixData,
    ],
    findOneAndUpdate: [
        Leads.functions.fixData,
    ],
};

module.exports = Leads;
