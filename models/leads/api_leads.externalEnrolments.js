let ExternalEnrolments = {
    functions: {},
    database: {
        collection: 'ExternalEnrolments',
        connection: 'database_leads',
        fields    : {
            student: {
                name          : {
                    type    : String,
                    required: true
                },
                email         : {
                    type    : String,
                    required: true
                },
                cpf                    : {
                  type     : String,
                  required: function () {
                    return !this.student.foreigner;
                  },
                  unique   : 'Cpf já cadastrado',
                  sparse   : true,
                  default  : function () {
                    return this.document;
                  },
                  allowNull: false
                },
                rg                    : {
                  type     : String,
                  required: false,
                },
                documentCountryOrigin  : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  },
                  default : 'Brasil'
                },
                documentType           : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  },
                  default : 'CPF'
                },
                document               : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  }
                },
                foreigner              : {
                  type   : Boolean,
                  default: false
                },
                cellPhone     : String,
                dateConclusion: Date,
                address       : {
                    street    : {
                        type     : String,
                        required : true,
                        allowNull: false
                    },
                    number    : {
                        type     : String,
                        required : true,
                        allowNull: false
                    },
                    complement: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    zone      : {
                        type     : String,
                        required : true,
                        allowNull: false
                    },
                    zip       : {
                        type     : String,
                        required : true,
                        allowNull: false
                    },
                    city      : {
                        type     : String,
                        required : true,
                        allowNull: false
                    },
                    uf        : {
                        type     : String,
                        required : true,
                        allowNull: false
                    }
                }
            },
            course : {
                system   : {
                    type    : String,
                    required: true
                },
                name     : {
                    type    : String,
                    required: true
                },
                certifier: {
                    type    : String,
                    required: true
                },
                type     : {
                    type    : String,
                    required: true
                },
                payment  : {
                    enrolment: {
                        dueDate    : {
                            type    : Date,
                            required: true
                        },
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    },
                    course   : {
                        paymentDay : {
                            type    : Number,
                            required: true
                        },
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                }
            }
        }
    }
};

module.exports = ExternalEnrolments;
