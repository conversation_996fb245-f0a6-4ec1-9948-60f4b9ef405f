const mongoose = require('mongoose');

let OnlineBrokers = {
    functions: {},
    database: {
        collection: 'OnlineBrokers',
        connection: 'database_leads',
        fields: {
            name: {
                type: String,
                required: true
            },
            _brokerId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _partnerUserId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            isPaused: {
                type: Boolean,
                default: false
            },
            location: [
                {
                    state: {
                        type: String,
                        required: true
                    },
                    city: {
                        type: String,
                        required: true
                    }
                }
            ],
            score: {
                type: Number,
                required: true
            },
            dailyLeads: {
                type: Number,
                required: true
            },
            numbersLeadsOfDay  : {
                type    : Number,
                required: true,
                default : 0
            },
            _teamId: [mongoose.SchemaTypes.ObjectId],
            _teamName: String,
            hasReceivedOnLastDistribution: {
                type: Boolean,
                // required: false,
                default: false
            },
            queue: {
                type: Number,
                // required: false
            },
            hasReceivedPendingEnrolmentsOnLastDistribution: {
                type: Boolean,
                // required: false,
                default: false
            },
            active: {
                type: Boolean,
                required: true
            },
            distribuationRules: [
                {
                    _id: false,
                    interest: {
                        type: {
                            course: {
                                certifier: [String],
                                type: [String]
                            }
                        }
                    }
                }
            ],
            canReceiveLead: {
                type: Boolean,
                required: true
            },
            completeQueue: {
                type: Date,
                // required: false
            },
            positionQueue: {
                type: Number
            },
            totalNewLead: {
                type: Number
            },
            limitLeadsOfDay: {
                type: Number,
                // required: false
            }
        }
    }
};

module.exports = OnlineBrokers;
