const mongoose = require('mongoose');

let Exams = {
    functions: {},
    database: {
        collection: 'Exams',
        connection: 'database_leads',
        fields: {
            _leadId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            type: {
                type: String,
                enum: [
                    'enem',
                    'dissertation',
                    'transfer',
                    'new_title',
                    'proof',
                    'return'
                ]
            },
            enem: {
                year: {
                    type: Number
                },
                grade: {
                    type: Number,
                    min: 0,
                    max: 1000
                }
            },
            dissertation: {
                theme: {
                    type: String
                },
                link: {
                    type: String
                },
                grade: {
                    type: Number,
                    min: 0,
                    max: 100
                }
            },
            approved: {
                type: Boolean
            },
            student: {
                name: {
                    type: String,
                    required: true
                },
                email: {
                    type: String,
                    required: true
                },
                phone: {
                    type: String,
                    required: true
                },
                cpf: {
                    type: String
                },
                birthDate: {
                    type: Date
                },
                address: {
                    street: {
                        type: String
                    },
                    number: {
                        type: String
                    },
                    complement: {
                        type: String
                    },
                    zip: {
                        type: String
                    },
                    city: {
                        type: String
                    },
                    uf: {
                        type: String
                    }
                }
            },
            course: {
                name: {
                    type: String
                },
                type: {
                    type: String
                }
            },
            modality: {
                type: String
            },
            params: {
                type: mongoose.SchemaTypes.Mixed
            },
            local: {
                type: {
                    type: String
                },
                name: {
                    type: String
                }
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = Exams;
