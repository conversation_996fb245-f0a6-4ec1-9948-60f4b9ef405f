const mongoose = require('mongoose');

let TypeAdditionalWorkload = {
    functions: {},
    database: {
        collection: 'TypeAdditionalWorkload',
        connection: 'database_piaget',
        fields: {
            _certifiers: [{
                type: String,
                required: true
            }],
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            title: {
                type:  String,
                required: true
            },
            _courseTypes: [{
                type: String,
                required: true
            }],
            _course: {
                type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                },
                // required: false
            },
            workload: {
                type: Number,
                required: true
            },
            sendByPortal: {
                type: Boolean,
                default: true
            },
            observation: {
                type: String
            }
        }
    }
};

module.exports = TypeAdditionalWorkload;
