const mongoose = require('mongoose');

let Authorizations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Authorizations',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            name: {
                type: String,
                required: true
            },
            email: {
                type: String,
                required: true,
                lowercase: true
            },
            cpf: {
                type: String,
                required: true,
                allowNull: false
            },
            userType: {
                type: String,
                required: true,
                lowercase: true
            },
            instance: {
                type: String,
                required: true
            },
            origin: {
                type: String,
                // required: false
            },
            tokenDuration: {
                type: String,
                required: true
            },
            expiresIn: {
                type: Date,
                required: true
            },
            authorizationToken: {
                type: String,
                required: true
            }
        }
    }
};

module.exports = Authorizations;
