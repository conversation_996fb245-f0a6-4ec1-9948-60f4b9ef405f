let Registers = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Registers',
        fields    : {
            name     : {
                type     : String,
                required : true,
                maxlength: 150,
                unique   : 'register_already_exists'
            },
            _cnpj: {
                length: 14,
                required: true,
                type: String,
                unique: 'cnpj_already_exists'
            },
            logo     : {
                url  : {
                    type    : String,
                    required: true
                },
                brand: {
                    type    : String,
                    required: true
                },
                icon : {
                    type    : String,
                    required: true
                }
            },
            address  : {
                street    : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                number    : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                complement: {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                zone      : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                zip       : {
                    type     : Number,
                    required : true,
                    allowNull: false
                },
                city      : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                uf        : {
                    type     : String,
                    required : true,
                    allowNull: false
                }
            },
            alias    : {
                type    : String,
                required: true,
                unique: 'alias_already_exists'
            },
            contacts : {
                phone      : {
                    type    : String,
                    required: true
                },
                secondPhone: {
                    type: String
                }
            },
            isEnabled: {
                type    : Boolean,
                // required: false,
                default : false
            }
        },
        post      : {}
    }
}

module.exports = Registers;
