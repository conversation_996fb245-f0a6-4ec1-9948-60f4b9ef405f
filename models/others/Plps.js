const mongoose = require('mongoose');

let Plps = {
    database: {
        connection: 'database_piaget',
        collection: 'Plps',
        fields    : {
            id                      : {
                type    : Number,
                required: true
            },
            idCorreios              : {
                type    : Number,
                required: true
            },
            xml                     : {
                type    : String,
                required: true
            },
            xmlObject               : {
                type    : Object,
                required: true
            },
            labelsWithoutVerifyDigit: {
                type    : Array,
                required: true
            },
            labelsWithVerifyDigit   : {
                type    : Array,
                required: true
            },
            contractNumber          : {
                type    : String,
                required: true
            },
            postCard                : {
                type    : String,
                required: true
            },
            user                    : {
                type    : String,
                required: true
            },
            password                : {
                type    : String,
                required: true
            },
            sender                  : {
                _userId    : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _userName  : {
                    type    : String,
                    required: true
                },
                _department: {
                    type    : String,
                    required: true
                }
            }
        }
    }
};

module.exports = Plps;
