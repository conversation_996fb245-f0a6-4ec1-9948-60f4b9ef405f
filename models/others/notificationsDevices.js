const mongoose = require('mongoose');

let AccessRecords = {
    functions: {},
    database: {
        collection: 'AccessRecords',
        connection: 'database_piaget',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            name: {
                type: String,
                // required: false
            },
            email: {
                type: String,
                // required: false,
                lowercase: true
            },
            cpf: {
                type: String,
                // required: false,
                allowNull: false
            },
            userType: {
                type: String,
                // required: false,
                lowercase: true
            },
            instance: {
                type: String,
                // required: false
            },
            origin: {
                type: String,
                required: true
            },
            tokenNotify: {
                type: String,
                // required: false
            },
            deviceUUID: {
                type: String,
                // required: false
            },
            expireToken: {
                type: Date,
                // required: false
            },
            lastAccess: {
                type: Date,
                required: true
            },
            ip: {
                type: String,
                // required: false
            },
            certifier: {
                type: String,
                // required: false
            },
            logout: {
                type: Boolean,
                // required: false
            }
        }
    }
};

module.exports = AccessRecords;
