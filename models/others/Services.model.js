let Services = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Services',
        fields    : {
            name     : {
                type    : String,
                required: true
            },
            alias    : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe um serviço com este alias'
            },
            link     : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe um serviço com este link'
            },
            isEnabled: {
                type    : Boolean,
                // required: false,
                default : true
            }
        },
        indexes   : [
            {
                fields : {
                    link     : 1,
                    isEnabled: 1
                },
                options: {
                    unique: 'Já existe um serviço com esta configuração'
                }
            }
        ]
    }
};

module.exports = Services;
