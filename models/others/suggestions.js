const mongoose = require('mongoose');

let Suggestions = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Suggestions',
        fields: {
            description: {
                type: mongoose.SchemaTypes.String,
                required: true
            },
            title: {
                type: mongoose.SchemaTypes.String,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            userName: {
                type: mongoose.SchemaTypes.String,
                required: true
            },
            isEnabled: {
                type: mongoose.SchemaTypes.Boolean,
                // required: false,
                default: true
            },
            positiveVotes: {
                type: mongoose.SchemaTypes.Number,
                required: true
            },
            negativeVotes: {
                type: mongoose.SchemaTypes.Number,
                required: true
            },
            votes: [
                {
                    assessment: {
                        type: mongoose.SchemaTypes.String,
                        required: true
                    },
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    userName: {
                        type: mongoose.SchemaTypes.String,
                        required: true
                    },
                    launchedAt: {
                        type: mongoose.SchemaTypes.Date,
                        required: true
                    }
                }
            ],
            file: [
                {
                    url: {
                        type: String,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    },
                    type: {
                        type: String,
                        required: true
                    }
                }
            ],
            status: {
                type: String,
                enum: [
                    'waiting', //Aguardando para ser avaliado por um colaborador interno
                    'in_progress', // Passada ao ser de de desemvolminento
                    'closed', // Fechado
                    'concluding' // Demanda concluida
    
                ],
                default: 'waiting',
                required: true
            },
            suggestionResponse: {
                type: mongoose.SchemaTypes.String,
                // required: false
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    _userId: 1,
                    _certifierName: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = Suggestions;
