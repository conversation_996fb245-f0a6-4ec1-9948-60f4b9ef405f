const mongoose = require('mongoose');
const { SchemaTypes } = require('mongoose');

let Protocol = {
  functions: {},
  database: {
    collection: 'Protocol',
    connection: 'database_piaget',
    fields: {
      _prototocoloId: {
        type: Number,
        required: true,
        index: true,
        unique: true
      },
      _departamentAlias: {
        type: String,
        required: true,
        lowercas: true,
        index: true
      },
      departamentName: {
        type: String,
        required: true,
      },
      system: {
        type: String,
        required: true,
      },
      _userCorrespondingId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: false,
        index: false,
      },
      userCorrespondingName: {
        type: String,
        required: false,
        index: false,
      },
      userCreationName: {
        type: String,
        required: true,
      },
      _userCreationId : {
        type:  mongoose.SchemaTypes.ObjectId,
        required: true,
        index: true,
      },
      courseNameToDisplay: {
        type: String,
        required: false,
        index: true,
      },
      email: {
        type: String,
        required: false,
        index: true,
      },
      cpf: {
        type: String,
        required: false,
        index: true,
      },
      studentRegistrationCode:   {
        type: Number,
        required: false,
      },
      notify: {
        type: Boolean,
        required: false,
        index: false,
        default: true
      },
      status: {
        type: String,
        enum: [
          'Active',
          'Analysis',
          'Student_Return',
          'Deferred',
          'Rejected',
          'Close',
          'Waiting_Payment',
        ],
        default: 'Active'
      },
      subjectMatter: {
        type: String,
        required: true,
      },
      subjectMatterCustom: {
        type: Boolean,
        default: false
      },
      _certifierName: {
        type: String,
        required: true,
      },
      posts: [
        {
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          userName: {
            type: String,
            required : true,
          },
          message: {
            type: String,
            required: false,
          },
          launchedAt: {
            type: Date,
            required: true,
          },
          readedAt: {
            type: Date,
            required : false,
          },
          updatedAt: {
            type: Date,
            required : false,
          },
          deletedAt: {
            type: Date,
            required: false,
          },
          postsVisibleStudent: {
            type: Boolean,
            required: true,
          },
          ipSending: {
            type: String,
            required: true,
          },
          files: [
            {
              url: {
                type: String,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
              type: {
                type: String,
                required: true,
              },
            }
          ],
          logs: {
            type: [mongoose.Schema.Types.Mixed],
            default: []
          }
        }
      ],
      log: [],
      chargeHistory: [
        {
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          _userName: {
            type: String,
            required: true,
          },
          chargeId: {
            type: SchemaTypes.ObjectId,
            required: true,
          },
          amount: {
            type: Number,
            required: true,
          },
          _chargeTypeAlias: {
            type: String,
            required: true,
          },
        }
      ],
      arrEnrolment: [
        {
          courseName:   {
            type: String,
            required: true,
          },
          courseTypeName:   {
            type: String,
            required: false,
          },
          _id:   {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
        }
      ],
      solicitation: {
        _id: {
          type: mongoose.SchemaTypes.ObjectId,
          // required: false,
        },
        description: {
          type: mongoose.SchemaTypes.String,
          // required: false
        }
      },
      source: {
        type: mongoose.SchemaTypes.String,
        // required: false
      },
      price: {
        _chageTypeName: {
          type: mongoose.SchemaTypes.String,
          // required: false
        },
        amount: {
          type: mongoose.SchemaTypes.Number,
          // required: false
        },
        chargeId: {
          type: mongoose.SchemaTypes.ObjectId,
          // required: false,
        },
        checkoutLinkConfigId: {
          type: mongoose.SchemaTypes.ObjectId,
          // required: false,
        },
      },
      maxTime: {
        type: Number
      },
      maxTimeWaitingStudentReturn: {
        type: Number,
        required: true,
        default: 7,
      },
    },
    options: {
      timestamps: true
    },
    pre: {
    },
    post: {
    },
    indexes: [
      {
        fields : {
          _prototocoloId : 1,
          _departamentAlias: 1,
          _userCorrespondingId: 1,
          _userCreationId: 1,
          _enrolmentId: 1,
          cpf: 1,
          email: 1,
        },
        options: {
          unique: 'Já existe um protocolo com esse código '
        }
      }
    ]
  }
};

module.exports = Protocol;
