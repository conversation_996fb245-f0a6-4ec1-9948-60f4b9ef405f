const mongoose = require('mongoose');

let Campaign = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Campaign',
        fields: {
            _partnerId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                maxLength: 11,
                minLength: 11,
                index: true
            },
            name: {
                type: String,
                required: true
            },
            pixels: [{
                type: mongoose.SchemaTypes.ObjectId,
                ref: 'Pixels'
            }],
            types: [{
                type: String,
                enum: [
                    'Pós-Graduação',
                    /*'graduacao',*/
                    'Segunda Licenciatura',
                    'Segunda Graduação',
                    'Extensão',
                    'Aperfeiçoamento',
                    /* 'disciplinas_isoladas',*/
                    'Complementação Pedagógica',
                    'Graduação EaD'
                ]
            }
            ],
            certifiers: [
                {
                    type: String
                }
            ],
            areas: [
                {
                    type: String
                }
            ],
            courses: [
                {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                    type: {
                        type: String
                    },
                    acronym: {
                        type: String,
                        required: true
                    }
                }
            ],
            is_dynamic: {
                type: Boolean,
                default: false
            },
            link_dynamic: {
                type: String,
                // required: false
            },

            deletedAt: {
                type: Date,
                // required: false
            }
        }
    }
};

module.exports = Campaign;
