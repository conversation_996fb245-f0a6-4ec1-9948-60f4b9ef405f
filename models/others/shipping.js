let Shipping = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Shipping',
        fields    : {
            packing: {
                type    : String,
                enum    : [
                    'box',
                    'envelope',
                    'cylinder'
                ]
            },
            description: {
                type    : String,
                required: true
            },
            notes: {
                type    : String
            },
            weight: {
                type    : Number,
                required: true
            },
            cubage: {
                type    : Number,
                required: true
            },
            dimensions: {
                width: {
                    type    : Number,
                    required: true
                },
                length: {
                    type    : Number,
                    required: true
                },
                height: {
                    type    : Number,
                    required: true
                },
                diameter: {
                    type    : Number,
                    required: true
                }
            },
            additionalService: {
                ownHand: {
                    type    : Number
                },
                receiptNotice: {
                    type    : Number
                },
                declaredValue: {
                    type    : Number
                }
            },
            _codePacking: {
                type    : Number,
                required: true
            }
        }
    }
};

module.exports = Shipping;
