let ShippingContracts = {
    database: {
        connection: 'database_piaget',
        collection: 'ShippingContracts',
        fields    : {
            number            : {
                type    : String,
                required: true,
                unique  : true
            },
            name              : {
                type    : String,
                required: true
            },
            postCard          : {
                type    : String,
                required: true
            },
            administrativeCode: {
                type    : String,
                required: true
            },
            cnpj              : {
                type    : String,
                required: true
            },
            user              : {
                type    : String,
                required: true
            },
            password          : {
                type    : String,
                required: true
            },
            services          : [
                {
                    name      : {
                        type    : String,
                        required: true
                    },
                    code      : {
                        type    : String,
                        required: true
                    },
                    id        : {
                        type    : Number,
                        required: true
                    },
                    validUntil: {
                        type    : Date,
                        required: true
                    },
                    sigep     : {
                        category                : {
                            type    : String,
                            required: true
                        },
                        requireDimensions       : {
                            type    : Boolean,
                            required: true
                        },
                        requireAmountToBeCharged: {
                            type    : Boolean,
                            // required: false
                        },
                        paymentOnDelivery       : {
                            type    : Boolean,
                            required: true
                        },
                        groupedShipment         : {
                            type    : Boolean,
                            required: true
                        },
                        postalCode              : {
                            type    : String,
                            required: true
                        }
                    }
                }
            ]
        }
    }
};

 module.exports = ShippingContracts;
