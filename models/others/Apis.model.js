let Apis = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Apis',
        fields    : {
            name     : {
                type     : String,
                required : true
            },
            alias    : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe uma API com este alias'
            },
            baseUrl  : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe uma API com esta URL'
            },
            isEnabled: {
                type    : Boolean,
                // required: false,
                default : true
            }
        },
        indexes   : [
            {
                fields : {
                    baseUrl  : 1,
                    isEnabled: 1
                },
                options: {
                    unique: 'Já existe uma API com esta configuração'
                }
            }
        ]
    }
};

module.exports = Apis;
