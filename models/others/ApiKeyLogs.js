const mongoose = require('mongoose');

let ApiKeyLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ApiKeyLogs',
        fields    : {
            _apiKeyId: {
                type    : mongoose.Types.ObjectId,
                required: true
            },
            requestIp: {
                type    : String,
                required: true
            },
            route    : {
                type    : String,
                required: true
            },
            userAuthetication: {
                type    : String,
            }
        }
    }
};

module.exports = ApiKeyLogs;
