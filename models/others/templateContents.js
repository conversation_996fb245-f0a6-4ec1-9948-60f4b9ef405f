const mongoose = require('mongoose');

let TemplateContents = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TemplateContents',
        fields    : {
            name: {
                type    : String,
                required: true
            },
            content: {
                type    : String,
                required: true
            },
            alias: {
                type    : String,
                required: true
            },
            isToFreeCourse: {
                type: Boolean,
                default: false
            },
            _templateTypeAlias: {
                type    : String,
                // required: false
            },
            _patternId : {
                type    : mongoose.SchemaTypes.ObjectId
            },
            _certifierName: {
                type     : String
            },
            _courseTypeName: {
                type: String
            },
            isEnabled: {
                type     : Boolean,
                required : true,
                default  : false
            }
        }
    }
};

module.exports = TemplateContents;
