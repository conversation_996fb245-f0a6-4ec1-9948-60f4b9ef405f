function removePasswords(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj; // se não for um objeto, retorna o valor original
  }

  // Se for um array, aplica recursivamente em cada elemento
  if (Array.isArray(obj)) {
    return obj.map(item => removePasswords(item));
  }

  // Cria um novo objeto para armazenar os valores sem 'password'
  const newObj = {};

  // Itera sobre as chaves do objeto
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      // Verifica se a chave contém 'password'
      if (key.toLowerCase().includes('password')) {
        continue; // Ignora chaves que contêm 'password'
      }

      // Aplica recursivamente se o valor for um objeto ou array
      if (typeof obj[key] === 'object') {
        newObj[key] = removePasswords(obj[key]);
      } else {
        newObj[key] = obj[key]; // Copia o valor se não for objeto
      }
    }
  }

  return newObj;
}


let ReCaptchaBlocks = {
  functions: {
    hidePassword: function (next) {
      const doc = this;
      doc.set(removePasswords(doc.toObject()));

      return next();
    }
  },
  database: {
    collection: 'ReCaptchaBlocks',
    fields: {},
    options: {
      strict: false
    }
  }
};

ReCaptchaBlocks.database.pre = {
  save: [ReCaptchaBlocks.functions.hidePassword]
};

module.exports = ReCaptchaBlocks;
