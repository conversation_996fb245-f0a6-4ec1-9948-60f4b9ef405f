const mongoose = require('mongoose');

let Requests = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Requests',
        fields    : {
            _enrolmentId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            metadata: {
                type    : Object,
                required: true
            },
            status: {
                type: String,
                enum: [
                    'approved',
                    'pending'
                ],
                required: true
            }
        }
    }
};

module.exports = Requests;
