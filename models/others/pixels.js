const mongoose = require('mongoose');

let Pixels = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Pixels',
        fields: {
            _partnerId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                maxLength: 11,
                minLength: 11,
                index: true
            },
            name: {
                type: String,
                required: true
            },
            status: {
                type: Boolean,
                required: true
            },
            note: {
                type: String,
                // required: false
            },
            checkout: {
                type: String,
                // required: false
            },
            execBillet: {
                type: Boolean,
                required: true
            },
            notBound: {
                type: Boolean,
                required: true
            },
            whenToRun: {
                purchase: {
                    type: Boolean,
                    required: true
                },
                initiateCheckout: {
                    type: Boolean,
                    required: true
                },
            },
            value: {
                total: {
                    type: Boolean,
                    default: false
                },
                commission: {
                    type: Boolean,
                    default: false
                },
                fixed: {
                    type: Boolean,
                    default: false
                },
                fixedValue: {
                    type: Number,
                    default: false
                },
                customPayment: {
                    type: Boolean,
                    required: true
                },
                billet: {
                    total: {
                        type: Boolean,
                        default: false
                    },
                    commission: {
                        type: Boolean,
                        default: false
                    },
                    fixed: {
                        type: Boolean,
                        default: false
                    },
                    fixedValue: {
                        type: Number,
                        default: false
                    },
                },
                card: {
                    total: {
                        type: Boolean,
                        default: false
                    },
                    commission: {
                        type: Boolean,
                        default: false
                    },
                    fixed: {
                        type: Boolean,
                        default: false
                    },
                    fixedValue: {
                        type: Number,
                        default: false
                    },
                }
            },
            platform: {
                name: {
                    type: String,
                    required: true,
                    default: ''
                },
                id: {
                    type: String,
                    required: true,
                },
                token: {
                    type: String,
                    // required: false,
                }
            },
            campaigns: [{
                type: mongoose.SchemaTypes.ObjectId,
                ref: 'Campaign'
            }],
            deletedAt: {
                type: Date,
                // required: false,
            },
        }
    }
};

module.exports = Pixels;
