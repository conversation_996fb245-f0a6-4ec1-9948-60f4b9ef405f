const mongoose = require('mongoose');

let SessionTokens = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'SessionTokens',
        fields    : {
            email           : {
                type            : String,
                required        : true,
            },
            ip              : {
                type            : String,
                required        : true,
            },
            token           : {
                type            : String,
                required        : true,
            },
            tokenExpiration : {
                type            : Date,
                required        : true,
            },
            isEnabled       : {
                type            : Boolean,
                required        : true,
                default         : true
            },
            headers         : {
                type            : mongoose.SchemaTypes.Mixed,
            },
            forceDisable         : {
                _sessionTokenNew:{
                    type: mongoose.SchemaTypes.ObjectId,
                },
                dateDisable:{
                    type: Date,
                }
            }
        }
    }
};

module.exports = SessionTokens;
