let piagetEndpoints = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PiagetEndpoints',
        fields    : {
            name        : {
                type    : String,
                required: true,
                default : null
            },
            alias   : {
                type     : String,
                required : true,
                lowercase: true
            },
            isActive   : {
                type    : Boolean,
                default : true
            },
            isVisible:  {
                type    : Boolean,
                default : true
            }
        },
        indexes   : [
            {
                fields : {
                    alias: 1,
                    isActive: 1
                },
                options: {
                    unique: 'Já existe um endpoint ativo com esta configuração'
                }
            }
        ]
    }
};

module.exports = piagetEndpoints;
