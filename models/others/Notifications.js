const mongoose = require('mongoose');

let Notifications = {
    functions: {},
    database: {
        collection: 'Notifications',
        connection: 'database_piaget',
        fields    : {
            message   : {
                type    : String,
                required: true
            },
            sender    : {
                _userId  : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                userType : {
                    type    : String,
                    required: true,
                    enum    : [
                        'student',
                        'partner',
                        'teacher',
                        'employer'
                    ]
                },
                _userName: {
                    type    : String,
                    required: true
                }
            },
            method    : {
                type    : [String],
                required: true,
                enum    : [
                    'email',
                    'sms',
                    'whatsapp',
                    'push'
                ]
            },
            recipients: [
                {
                    _userId  : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    email    : String,
                    cellPhone: String,
                    whatsApp : String,
                    isSend   : {
                        type   : Boolean,
                        default: false
                    }
                }
            ],
            metadata  : mongoose.SchemaTypes.Mixed
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = Notifications;
