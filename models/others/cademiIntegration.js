const mongoose = require('mongoose');

const CademiIntegration = {
  functions: {},
  database: {
    collection: 'CademiIntegration',
    fields: {
      _enrolmentId: mongoose.SchemaTypes.ObjectId,
      _idIntegration: mongoose.SchemaTypes.ObjectId,
      _cpf: {
        type: String,
        required: true,
      },
      success: {
        type: Boolean,
        default: true,
      },
      erroResponse: {
        type: String,
        required: true,
      },
      response: mongoose.SchemaTypes.Mixed,
      body: mongoose.SchemaTypes.Mixed,
      createdAt: {
        type: Date,
        required: false,
      },
      updatedAt: {
        type: Date,
        required: false,
      },
    },
  },
};

module.exports = CademiIntegration;
