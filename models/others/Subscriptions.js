const mongoose = require('mongoose');

let Subscriptions = {
    functions: {},
    database: {
        collection: 'Subscriptions',
        connection: 'database_piaget',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            data: {
                endpoint: {
                    type: String,
                    required: true
                },
                keys: {
                    auth: {
                        type: String,
                        required: true
                    },
                    p256dh: {
                        type: String,
                        required: true
                    }
                }
            }
        },
        indexes: [
            {
                fields: {
                    _userId: 1,
                    'data.endpoint': 1,
                    'data.keys.auth': 1,
                    'data.keys.p256dh': 1
                },
                options: {
                    unique: 'user_already_subscribed'
                }
            }
        ]
    }
};

module.exports = Subscriptions;
