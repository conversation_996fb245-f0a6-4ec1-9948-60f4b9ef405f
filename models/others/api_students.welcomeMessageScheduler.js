const mongoose = require('mongoose');

let WelcomeMessageScheduler = {
    functions: {},
    database: {
        collection: 'WelcomeMessageScheduler',
        connection: 'database_piaget',
        fields: {
            daysElapsed: {
                type: Number,
                required: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                index: true
            },
            alreadySent: {
                type: Boolean,
                default: false,
                index: true
            }
        }
    }
};

module.exports = WelcomeMessageScheduler;
