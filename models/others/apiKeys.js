const mongoose = require('mongoose');

let ApiKeys = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ApiKeys',
        fields: {
            name: {
                type: String,
                required: true,
                index: true
            },
            typeKey: {
                type: String,
                default: 'partner'
            },
            cpf: {
                type: String,
                index: true
            },
            permissions: {
                type: [String],
                required: true,
                minLength: 1
            },
            allowedIps: {
                type: [String],
                required: true,
                minLength: 1
            },
            isEnabled: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            authorizationByEmployer: {
                type: Boolean,
                default: false
            },
            description: {
                type: String,
            },
            metadata: mongoose.SchemaTypes.Mixed
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = ApiKeys;
