let Modules = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Modules',
        fields    : {
            name         : {
                type    : String,
                required: true
            },
            alias        : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe um módulo com este alias'
            },
            _serviceAlias: {
                type    : String,
                required: true
            },
            link         : {
                type     : String,
                required : true,
                lowercase: true
            },
            isEnabled    : {
                type    : Boolean,
                // required: false,
                default : true
            }
        },
        indexes   : [
            {
                fields : {
                    _serviceAlias: 1,
                    link         : 1,
                    isEnabled    : 1
                },
                options: {
                    unique: 'Já existe um módulo com esta configuração'
                }
            }
        ]
    }
};

module.exports = Modules;
