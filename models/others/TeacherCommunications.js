const mongoose = require('mongoose');

let TeacherCommunications = {
  functions: {},
  database: {
    collection: 'TeacherCommunications',
    connection: 'database_piaget',
    fields: {
      channels: {
        type: [{
          type: String,
          enum: [
            'email',
            'sms',
            'portal',
          ],
          required: true,
        }],
        required: true,
      },
      subject: {
        type: String,
        required: true,
      },
      message: {
        type: String,
        required: true,
      },
      targetList: {
        type: [{
          _id: false,
          _enrolmentId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          _studentId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          results: {
            type: [{
              _id: false,
              email: Boolean,
              sms: Boolean,
              portal: Boolean,
            }],
            required: true,
          }
        }],
        required: true,
      },
      _classId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _disciplineId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      sender: {
        type: {
          _id: false,
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          }
        },
        required: true,
      },
    },
    options: {
      timestamps: true,
    },
  }
};

module.exports = TeacherCommunications;
