let TemplateCoursewareContracts = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'TemplateCoursewareContracts',
    fields: {
      isEnabled: {
        type: Boolean,
        required: true,
        default: false,
      },
      name: {
        type: String,
        required: true,
      },
      content: {
        type: String,
        required: true,
      },
      _certifierName: {
        type: String,
      },
      _courseTypeName: {
        type: String,
      },
    }
  }
};

module.exports = TemplateCoursewareContracts;
