const mongoose = require('mongoose');

let GroupQuestions = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogSagah',
        fields    : {
            headers : {
                type    : mongoose.SchemaTypes.Mixed
            },
            body : {
                type    : mongoose.SchemaTypes.Mixed
            },
            code : {
                type    : mongoose.SchemaTypes.Mixed
            },
            data : {
                type    : mongoose.SchemaTypes.Mixed
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = GroupQuestions;
