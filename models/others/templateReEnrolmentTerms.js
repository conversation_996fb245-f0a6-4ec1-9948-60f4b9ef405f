let TemplateReEnrolmentTerms = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'TemplateReEnrolmentTerms',
    fields: {
      isEnabled: {
        type: Boolean,
        required: true,
        default: true,
      },
      content: {
        type: String,
        required: true,
      },
      _certifierName: {
        type: String,
        required: true,
      },
      _courseTypeName: {
        type: String,
        required: true,
      },
    },
  }
};

module.exports = TemplateReEnrolmentTerms;
