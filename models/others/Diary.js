const mongoose = require('mongoose');

let Diary = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Diary',
        fields: {
            _classId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            className: {
                type: String,
                required: true
            },
            _discId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            discName: {
                type: String,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _userName: {
                type: String,
                // required: false,
                allowNull: true
            },
            students: {
                type: Number,
                required: true,
                default: 0
            },
            approved: {
                type: Number,
                required: true,
                default: 0
            },
            disapproved: {
                type: Number,
                required: true,
                default: 0
            },
            recuperation: {
                type: Number,
                required: true,
                default: 0
            },
            enrolments: {
                type: [
                    {
                        _enrolmentId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true,
                            index: true
                        },
                        _studentId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true,
                            index: true
                        },
                        cpf: {
                            type: String,
                            required: true
                        },
                        name: {
                            type: String,
                            required: true
                        },
                        status: {
                            type: String,
                            required: true
                        },
                        presence: {
                            type:
                                {
                                    lessons: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    present: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    absences: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    justified: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    presence: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    approved: {
                                        type: Boolean,
                                        required: true,
                                        default: false
                                    }
                                },
                            // required: false,
                            allowNull: true
                        },
                        discipline: {
                            type:
                                {
                                    grade: {
                                        type: Number,
                                        required: true,
                                        default: 0
                                    },
                                    status: {
                                        type: String,
                                        required: true
                                    }
                                },
                            required: true
                        }
                    }
                ],
                // required: false,
                allowNull: true
            },
            isActive: {
                type: Boolean,
                required: true,
                default: true
            }
    
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    _classId: 1,
                    _discId: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = Diary;
