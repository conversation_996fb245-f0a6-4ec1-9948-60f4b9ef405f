const mongoose = require('mongoose');

let BankAccounts = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'BankAccounts',
    fields: {
      cpf: {
        type: String,
        required: true,
        index: true
      },
      documentType: {
        type: String,
        required: true,
        enum: [
          'cpf',
          'cnpj'
        ]
      },
      documentNumber: {
        type: String,
        required: true
      },
      legalName: {
        type: String,
        required: true
      },
      dataBank: {
        bankCode: {
          type: String,
          required: true,
          index: true
        },
        agency: {
          type: String,
          required: true
        },
        agencyDv: {
          type: String
        },
        operation: {
          type: String,
          default: '0000'
        },
        account: {
          type: String,
          required: true
        },
        accountDv: {
          type: String,
          required: true
        },
        accountType: {
          type: String,
          required: true,
          enum: [
            'conta_corrente',
            'conta_poupanca',
            'conta_corrente_conjunta',
            'conta_poupanca_conjunta'
          ]
        }
      },
      receipt: {
        type: String,
        required: true
      },
      status: {
        type: String,
        enum: [
          'approved',
          'refused',
          'analysis'
        ],
        default: 'analysis'
      },
      history: [
        {
          status: {
            type: String,
            enum: [
              'approved',
              'refused',
              'analysis'
            ],
            required: true
          },
          description: String,
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _userType: {
            type: String,
            required: true,
            enum: [
              'student',
              'partner',
              'teacher',
              'employer',
              'computer'
            ]
          },
          _userName: {
            type: String,
            required: true
          },
          launchedAt: {
            type: Date,
            required: true
          }
        }
      ],
      isActive: {
        type: Boolean,
        default: false
      }
    },
    options: {
      timestamps: true
    }
  }
};

module.exports = BankAccounts;
