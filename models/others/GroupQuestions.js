const mongoose = require('mongoose');

let GroupQuestions = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'GroupQuestions',
        fields: {
            tags: [
                {
                    name: {
                        type: String,
                        required: true,
                    },
                    _tagId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                    }
                }
            ],
            // capitulos do livro
            chapter: {
                type: [Number],
                default: undefined
            },
            visibleProcessSelective: {
                type: Boolean,
                default: false
            },
            //discipline
            discipline: {
                type: [{
                    _disciplineId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false,
                    },
                    name: {
                        type: String,
                        // required: false,
                    }
                }],
                // required: false,
                default: undefined
            },
    
            questions: [
                {
                    name: {
                        type: String,
                        required: true,
                    },
                    // capitulos do livro
                    chapter: {
                        type: Number,
                        // required: false,
                    },
                    pageApostille: {
                        type: Number,
                        required: true
                    },
                    isActive: {
                        type: Boolean,
                        default: true
                    },
                    // Fala se a questão é do tipo de selecionar ou texto
                    typeOfAnswer: {
                        type: String,
                        enum: [
                            'text',
                            'select',
                            'file'
                        ],
                        default: 'text'
                    },
                    //Aonde ela pode ser usada
                    type: {
                        type: [String],
                        required: true
                    },
    
                    difficultyLevel: {
                        type: String,
                        enum: [
                            'easy',
                            'medium',
                            'difficult'
                        ],
                        default: 'easy'
                    },
                    //Enunciado para o aluno ler
                    utterance: {
                        type: String,
                        required: true,
                    },
                    // Esses arquivos tem que salvos no storagem e aqui referenciado pelo url para exibir ao alunos.
                    files: {
                        type: [{
                            url: {
                                type: String,
                                required: true,
                            },
                            name: {
                                type: String,
                                required: true,
                            },
                            type: {
                                type: String,
                                required: true,
                            },
                        }],
                        // required: false,
                        default: undefined
                    },
    
                    alternatives: {
                        type: [{
                            utteranceAlternative: {
                                type: String,
                                required: true,
                            },
                            correctAlternative: {
                                type: Boolean,
                                default: false,
                            },
                        }],
                        // required: false,
                        default: undefined
                    },
    
                }
            ]
        },
        options: {
            timestamps: true
        },
        pre: {},
        post: {},
        indexes: [
            {
                fields: {
                    name: 1,
                    type: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = GroupQuestions;
