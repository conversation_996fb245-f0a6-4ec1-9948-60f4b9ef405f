const mongoose = require('mongoose');
const {_getModels} = require('../../services/Utils');

let Partners = {
  functions: {
    addSettingsPartner: function (next) {
      const models = _getModels.call(this, 'Partners');
      const {Settings} = models;

      return Settings
        .findOneAndUpdate({_cpf: this.documents.cpf}, {integration: {paymentsPer: 'in'}}, {upsert: true})
        .then(() => next())
        .catch((err) => {
          console.log(err);
          next(Error('cannot_create_settings'));
        });
    },
    addWallet: async function (next) {
      try {

        const models = _getModels.call(this, 'Partners');
        const {Wallets} = models;

        const wallet = await Wallets.findOne({cpf: this.documents.cpf});

        if (wallet) return next();

        await Wallets.create({
          cpf: this.documents.cpf,
          name: this.name,
          userType: 'partner',
          balance: 0,
          address: {
            street: this.addresses.street,
            number: this.addresses.number,
            complement: this.addresses.complement,
            zone: this.addresses.zone,
            zip: this.addresses.zip,
            city: this.addresses.city,
            uf: this.addresses.state
          }
        });

      } catch (err) {
        console.error(err);
        throw err;
      } finally {
        next();
      }
    }
  },
  database: {
    collection: 'Partners',
    connection: 'database_piaget',
    fields: {
      name: {
        type: String,
        required: true
      },
      code: {
        type: String,
        unique: 'Código já cadastrado',
        required: true
      },
      archives: {
        cpf: {
          type: String
        },
        rg: {
          type: String
        },
        address: {
          type: String
        }
      },
      documents: {
        cpf: {
          type: String,
          // required: false,
          index: true,
          length: 11,
          sparse: true
        },
        rg: {
          dispatcher: {
            type: String,
            // required: false,
            uppercase: true
          },
          code: {
            type: String,
            // required: false
          }
        },
        numberPis: {
          type: String
        }
      },
      startDate: {
        type: Date,
        // required: false,
        default: null
      },
      validateMonth: {
        type: Number,
        // required: false,
        default: null
      },
      birthDate: {
        type: Date
      },
      motherName: {
        type: String
      },
      civilStatus: {
        type: String,
        // required: false,
        enum: [
          'single',
          'married',
          'divorced',
          'widower'
        ]
      },
      gender: {
        type: String,
        // required: false,
        enum: [
          'f',
          'm'
        ]
      },
      contacts: {
        phone: {
          type: String,
          // required: false
        },
        secondPhone: {
          type: String
        }
      },
      addresses: {
        state: {
          type: String,
          // required: false,
          length: 2,
          uppercase: true
        },
        city: {
          type: String,
          // required: false
        },
        zone: {
          type: String,
          // required: false
        },
        street: {
          type: String,
          // required: false
        },
        number: {
          type: Number,
          // required: false
        },
        complement: {
          type: String,
          // required: false
        },
        zip: {
          type: String,
          // required: false,
          length: 8
        },
        lat: {
          type: String
        },
        lng: {
          type: String
        }
      },
      commissionType: {
        type: String,
        enum: [
          'priceRange',
          'courseSubcategory',
          'commission_by_modality'
        ],
        // required: false,
        default: 'courseSubcategory'
      },
      minRateEnrolment: {
        type: Number,
        // required: false,
        default: 5000
      },
      subcategoryCommissions: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          },
          _courseSubcategoryName: {
            type: String
          },
          exceptions: {
            type: [
              {
                installment: {
                  type: Number
                },
                commission: {
                  type: Number
                }
              }
            ],
            // required: false,
            default: undefined
          }
        }
      ],
      bonus: {
        creditCard: {
          maxInstallment: {
            type: Number,
            default: 0
          },
          value: {
            type: Number,
            default: 0
          }
        },
        boleto: {
          maxInstallment: {
            type: Number,
            default: 0
          },
          value: {
            type: Number,
            default: 0
          }
        },
        debitCard: {
          maxInstallment: {
            type: Number,
            default: 0
          },
          value: {
            type: Number,
            default: 0
          }
        },
        cardRecurrence: {
          maxInstallment: {
            type: Number,
            default: 0
          },
          value: {
            type: Number,
            default: 0
          }
        },
        pix: {
          maxInstallment: {
            type: Number,
            default: 0
          },
          value: {
            type: Number,
            default: 0
          }
        }
      },
      commissions: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          },
          exceptions: {
            type: [
              {
                installment: {
                  type: Number
                },
                commission: {
                  type: Number
                }
              }
            ],
            // required: false,
            default: undefined
          }
        }
      ],
      commissionMedium: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          }
        }
      ],
      commissionMinimum: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          }
        }
      ],
      commissionLeads: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          }
        }
      ],
      rates: {
        loot: {
          type: {
            type: String,
            enum: [
              'value',
              'percentage'
            ],
            // required: false
          },
          value: {
            type: Number,
            // required: false
          }
        },
        boletoLiquidation: {
          type: {
            type: String,
            enum: [
              'value',
              'percentage'
            ],
            // required: false
          },
          value: {
            type: Number,
            // required: false
          }
        },
        pixLiquidation: {
          type: {
            type: String,
            enum: [
              'value',
              'percentage'
            ],
            // required: false
          },
          value: {
            type: Number,
            // required: false
          }
        },
        creditCardLiquidation: {
          type: {
            type: String,
            enum: [
              'value',
              'percentage'
            ],
            // required: false
          },
          value: {
            type: Number,
            // required: false
          }
        },
        debitCardLiquidation: {
          type: {
            type: String,
            enum: [
              'value',
              'percentage'
            ],
            // required: false
          },
          value: {
            type: Number,
            // required: false
          }
        }
      },
      taxEnrolments: [
        {
          percentage: {
            type: Number
          },
          percentageToLinkPolo: {
            type: Number
          },
          percentageToLinkThirdPartyPolo: {
            type: Number
          },
          _certifierName: {
            type: String
          },
          _courseTypeName: {
            type: String
          }
        }
      ],
      customRates: mongoose.SchemaTypes.Mixed,
      _contractsId: mongoose.SchemaTypes.ObjectId,
      occupationArea: [
        {
          state: {
            type: String,
            length: 2,
            uppercase: true,
            // required: false
          },
          city: {
            type: String,
            // required: false
          }
        }
      ],
      status: {
        type: String,
        enum: [
          'pre-registration',
          'sent-document',
          'commission-pending',
          'complete'
        ],
        default: 'pre-registration'
      },
      isActive: {
        type: Boolean,
        default: true
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      advanceInstallments: {
        type: Boolean,
        // required: false,
        default: false
      },
      advanceInstallmentsConfig: {
        type: {
          _userCreationId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true
          },
          percentageAmount: [
            {
              installment: Number,
              value: Number
            }
          ],
          maxAmountMonthly: {
            type: Number,
            required: true
          }
        },
        // required: false
      },
      email: {
        type: String,
        lowercase: true
      },
      _recipientId: {
        type: String,
        // required: false
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed
      },
      orderPlan: {
        name: {
          type: String
        },
        orderPlanId: {
          type: mongoose.SchemaTypes.ObjectId
        }
      },
      _preRegisterId: mongoose.SchemaTypes.ObjectId
    },
    indexes: [
      {
        fields: {
          'documents.rg.code': 1,
          'documents.rg.dispatcher': 1
        },
        options: {
          unique: 'Já existe um parceiro com este RG'
        }

      },
      {
        fields: {
          name: 'text'
        },
        options: {}
      }
    ]
  }
}

Partners.database.pre = {
  save: [
    Partners.functions.addSettingsPartner
  ]
};

module.exports = Partners;
