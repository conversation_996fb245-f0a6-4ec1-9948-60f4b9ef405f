let PartnerCompanies = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PartnerCompanies',
        fields: {
            archives:   {
                cnpj: {
                    type: String
                },
                socialContract: {
                    type: String
                }
            },
            cnpj: {
                type  : String,
                length: 14,
                unique: 'Cnpj já cadastrado',
                required: true
            },
            socialName: {
                type    : String,
                required: true
            },
            fantasyName: {
                type: String
            },
            status: {
                type: String,
                enum: [
                    'approved',
                    'refused',
                    'analysis'
                ],
                default: 'analysis'
            },
            description: String,
            addresses: {
                street: {
                    type: String,
                    required: true
                },
                number: {
                    type: Number,
                    required: true
                },
                zone: {
                    type: String,
                    required: true
                },
                city: {
                    type: String,
                    required: true
                },
                zip: {
                    type: String,
                    required: true,
                    length: 8
                },
                complement: {
                    type: String,
                    // required: false
                },
                state: {
                    type: String,
                    required: true,
                    length: 2
                }
            },
            isActive: {
                type: Boolean,
                default: false
            },
            _cpf: {
                type: String,
                required: true,
                length: 11
            }
        }
    }
};

module.exports = PartnerCompanies;
