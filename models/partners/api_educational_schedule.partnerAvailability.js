const mongoose = require('mongoose');

let PartnerAvailability = {
    functions: {},
    database: {
        collection: 'PartnerAvailability',
        connection: 'database_piaget',
        fields    : {
            _partnerId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            state     : {
                type    : String,
                required: true,
                index   : true
            },
            city      : {
                type    : String,
                required: true,
                index   : true
            },
            address   : {
                street    : {
                    type    : String,
                    required: true
                },
                number    : {
                    type    : Number,
                    required: true
                },
                complement: String,
                zone      : {
                    type    : String,
                    required: true
                },
                zip       : {
                    type    : Number,
                    required: true
                }
            },
            date      : {
                type    : Date,
                required: true,
                index   : true
            },
            hours     : [
                {
                    start: {
                        type    : String,
                        required: true
                    },
                    end  : {
                        type    : String,
                        required: true
                    }
                }
            ],
            isActive  : {
                type   : Boolean,
                default: true
            }
        }
    }
};

module.exports = PartnerAvailability;
