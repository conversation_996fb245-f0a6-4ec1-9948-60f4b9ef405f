const mongoose = require('mongoose');

let Partners = {
  database: {
    collection: 'PartnersPreRegisters',
    connection: 'database_piaget',
    fields: {
      status: {
        type: String,
        // required: false,
        default: 'pending',
        enum: ['pending', 'accepted', 'rejected', 'expired']
      },
      name: {
        type: String,
        required: true
      },
      howDidIKnow: {
        type: String,
        // required: false
      },
      worksOnEducation: {
        type: Boolean,
        // required: false
      },
      havePartnership: {
        type: Boolean,
        // required: false
      },
      type: {
        type: String,
        enum: ['physical', 'juridical'],
        required: true
      },
      cpf: {
        type: String,
        // required: false,
        unique: 'cpf_already_registered',
        sparse: true
      },
      cnpj: {
        type: String,
        // required: false,
        unique: 'cnpj_already_registered',
        sparse: true
      },
      responsible: {
        type: String,
        // required: false,
      },
      state: {
        type: String,
        required: true
      },
      city: {
        type: String,
        required: true
      },
      email: {
        type: String,
        required: true,
        unique: 'email_already_registered'
      },
      phone: {
        type: String,
        required: true
      },
      phone2: {
        type: String,
        // required: false
      },
      instagram: {
        type: String,
        // required: false
      },
      linkedin: {
        type: String,
        // required: false
      },
      observations: {
        type: String,
        // required: false
      },
      contract: {
        type: mongoose.SchemaTypes.Mixed
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed
      },
    },
    indexes: [
      {
        fields: {
          'cpf': 1
        },
        options: {
          unique: 'cpf_already_registered',
          sparse: true
        }
      },
      {
        fields: {
          'cnpj': 1
        },
        options: {
          unique: 'cnpj_already_registered',
          sparse: true
        }
      },
      {
        fields: {
          'email': 1
        },
        options: {
          unique: 'email_already_registered'
        }
      },
    ]
  }
}

module.exports = Partners;
