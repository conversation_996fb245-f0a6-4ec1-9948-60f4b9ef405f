let INSSDeclarations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'INSSDeclarations',
        fields: {
            cpf: {
                type: String,
                required: true
            },
            archive: {
                type: String,
                required: true
            },
            value: {
                type: Number,
                // required: false
            },
            isMaxValue: {
                type: Boolean,
                default: false
            },
            startDate: {
                type: Date,
                // required: false
            },
            endDate: {
                type: Date,
                // required: false
            },
            status: {
                type: String,
                enum:
                    [
                        'analysis',
                        'waiting_sent',
                        'conclusion',
                        'refused'
                    ],
                default: 'analysis'
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = INSSDeclarations;
