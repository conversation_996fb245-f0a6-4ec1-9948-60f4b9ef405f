const mongoose = require('mongoose');

let PartnersUsers = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PartnersUsers',
        fields    : {
            name          : {
                type    : String,
                required: true
            },
            email         : {
                type     : String,
                required : true,
                lowercase: true
            },
            password      : {
                type     : String,
                minlength: 5,
                required : true
            },
            _partnerId       : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            code                  : {
                type    : String,
                unique  : 'Código já cadastrado',
                // required: false
            },
            avatar        : {
                type    : String,
                // required: false,
                default : null
            },
            permissions      : {
                type   : [String],
                required: true
            },
            settings: {
                dailyLead: {
                  type: Number,
                  default: 0
                },
                customLimit: {
                  type: Number
                },
                isCustomLimit: {
                  type: Boolean,
                  default: false
                }
            },
            isEnabled     : {
                type    : Boolean,
                // required: false,
                default : true
            },
            typeEmployee: {
                type: String
            }
        }
    }
};

module.exports = PartnersUsers;
