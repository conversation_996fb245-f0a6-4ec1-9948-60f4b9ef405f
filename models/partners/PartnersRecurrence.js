const mongoose = require('mongoose');

let PartnerRecurrences = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PartnerRecurrences',
        fields: {
            cpf: {
                type: String,
                required: true
            },
            _cardTokenId: {
                type: mongoose.Types.ObjectId,
                required: true
            },
            _orderId: {
                type: mongoose.Types.ObjectId,
                required: true,
                unique: 'already_in_recurrence'
            },
            isActive: {
                type: Boolean,
                // required: false,
                default: true
            },
            nextAt: {
                type: Date,
                // required: false,
                default: null
            },
            history: {
                type: [
                    {
                        _id: false,
                        occurrence: {
                            type: String,
                            required: true
                        },
                        date: {
                            type: Date,
                            // required: false,
                            default: new Date()
                        },
                        message: {
                            type: String,
                            required: true
                        },
                        metadata: mongoose.SchemaTypes.Mixed
                    }
                ],
                // required: false,
                default: [
                    {
                        occurrence: 'created',
                        message: 'Recorrência criada com sucesso',
                        date: new Date()
                    }
                ]
            },
            lastOccurrence: {
                type: {
                    occurrence: {
                        type: String,
                        required: true
                    },
                    date: {
                        type: Date,
                        // required: false,
                        default: new Date()
                    },
                    message: {
                        type: String,
                        required: true
                    },
                    metadata: mongoose.SchemaTypes.Mixed
                },
                // required: false,
                default: {
                    occurrence: 'created',
                    message: 'Recorrência criada com sucesso',
                    date: new Date()
                }
            },
            inQueue: {
                type: Boolean,
                // required: false,
                default: false
            },
            chargesQueue: {
                type: [
                    {
                        _id: false,
                        _chargeId: {
                            type: mongoose.Types.ObjectId,
                            required: true
                        },
                        tries: {
                            type: Number,
                            // required: false,
                            default: 0
                        },
                        lastTryAt: Date
                    }
                ],
                // required: false,
                default: []
            }
        }
    }
};

module.exports = PartnerRecurrences;
