const mongoose = require('mongoose');

let PartnerSite = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'PartnerSite',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _partnerUserId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                maxLength: 11,
                minLength: 11,
                index: true
            },
            whatsapp: {
                type: String,
                // required: false
            },
            phone: {
                type: String,
                // required: false
            },
            email: {
                type: String,
                lowercase: true,
                // required: false
            },
            facebook: {
                type: String,
                // required: false
            },
            instagram: {
                type: String,
                // required: false
            },
            youtube: {
                type: String,
                // required: false
            },
            linkedin: {
                type: String,
                // required: false
            },
            twitter: {
                type: String,
                // required: false
            },
            deletedAt: {
                type: Date,
                // required: false,
            },
        }
    }
};

module.exports = PartnerSite;
