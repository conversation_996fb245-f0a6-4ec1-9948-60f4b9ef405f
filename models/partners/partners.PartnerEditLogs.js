const {SchemaTypes} = require('mongoose');

let Partners = {
  database: {
    collection: 'PartnerEditLogs',
    connection: 'database_piaget',
    fields: {
      _partnerId: {
        type: SchemaTypes.ObjectId,
        required: true
      },
      change: {
        type: String,
        required: true
      },
      before: {
        type: SchemaTypes.Mixed,
        required: true
      },
      after: {
        type: SchemaTypes.Mixed,
        required: true
      },
      user: {
        type: {
          _id: {
            type: SchemaTypes.ObjectId,
            required: true
          },
          name: {
            type: String,
            required: true
          }
        },
        required: true
      },
    }
  }
}

module.exports = Partners;
