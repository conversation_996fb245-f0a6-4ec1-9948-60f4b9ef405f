const mongoose = require('mongoose');

let Contracts = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Contracts',
        fields    : {
            startDate             : {
                type    : Date,
                // required: false,
                default : null
            },
            validateMonth         : {
                type    : Number,
                required: true
            },
            content               : {
                type    : String,
                // required: false,
                default : null
            },
            status                : {
                type    : String,
                required: true,
                enum    : [
                    'waiting_approval',
                    'approved',
                    'refused',
                    'signed',
                    'canceled',
                    'expired'
                ],
                default : 'waiting_approval'
            },
            _templateContentAlias : {
                type    : String,
                required: true
            },
            minRateEnrolment      : {
                type    : Number,
                // required: false,
                default : 5000
            },
            commissionType        : {
                type    : String,
                enum    : [
                    'priceRange',
                    'courseSubcategory',
                    'commission_by_modality'
                ],
                // required: false,
                default : 'courseSubcategory'
            },
            subcategoryCommissions: [
                {
                    percentage            : {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    percentageToLinkThirdPartyPolo: {
                        type: Number
                    },
                    _certifierName        : {
                        type: String
                    },
                    _courseTypeName       : {
                        type: String
                    },
                    _courseSubcategoryName: {
                        type: String
                    },
                    exceptions: {
                        type: [
                            {
                                installment: {
                                    type: Number
                                },
                                commission: {
                                    type: Number
                                },
                            }
                        ],
                        // required: false,
                        default: undefined,
                    }
                }
            ],
            bonus                 : {
                creditCard    : {
                    maxInstallment: {
                        type   : Number,
                        default: 0
                    },
                    value         : {
                        type   : Number,
                        default: 0
                    }
                },
                boleto        : {
                    maxInstallment: {
                        type   : Number,
                        default: 0
                    },
                    value         : {
                        type   : Number,
                        default: 0
                    }
                },
                debitCard     : {
                    maxInstallment: {
                        type   : Number,
                        default: 0
                    },
                    value         : {
                        type   : Number,
                        default: 0
                    }
                },
                cardRecurrence: {
                    maxInstallment: {
                        type   : Number,
                        default: 0
                    },
                    value         : {
                        type   : Number,
                        default: 0
                    }
                },
                pix           : {
                    maxInstallment: {
                        type   : Number,
                        default: 0
                    },
                    value         : {
                        type   : Number,
                        default: 0
                    }
                }
            },
            commissions           : [
                {
                    percentage     : {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    percentageToLinkThirdPartyPolo: {
                        type: Number
                    },
                    _certifierName : {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    },
                    exceptions: {
                        type: [
                            {
                                installment: {
                                    type: Number
                                },
                                commission: {
                                    type: Number
                                },
                            }
                        ],
                        // required: false,
                        default: undefined,
                    }
                }
            ],
            commissionMedium      : [
                {
                    percentage     : {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    percentageToLinkThirdPartyPolo: {
                        type: Number
                    },
                    _certifierName : {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }
            ],
            commissionMinimum     : [
                {
                    percentage     : {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    percentageToLinkThirdPartyPolo: {
                        type: Number
                    },
                    _certifierName : {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }
            ],
            taxEnrolments         : [
                {
                    percentage     : {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    percentageToLinkThirdPartyPolo: {
                        type: Number
                    },
                    _certifierName : {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }
            ],
            rates                 : {
                loot                     : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                },
                boletoLiquidation        : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                },
                pixLiquidation           : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                },
                creditCardLiquidation    : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                },
                debitCardLiquidation     : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                },
                cardRecurrenceLiquidation: {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number,
                        // required: false
                    }
                }
            },
            _cpf                  : {
                type    : String,
                required: true,
                length  : 11
            },
            uuid                  : {
                type    : String,
                // required: false
            },
            metadata              : {
                name      : {
                    type    : String,
                    // required: false
                },
                email     : {
                    type    : String,
                    // required: false
                },
                ip        : {
                    type    : String,
                    // required: false
                },
                dateSigned: {
                    type    : Date,
                    // required: false
                }
            },
            link                  : {
                type    : String,
                // required: false
            },
            typeContract: {
                type: String,
                enum: [
                    'renewedContract',   // Contrato com renovação de acordo com a validade de meses
                    'perpertual',       // Contrato que não sofre expiração e nem renovação
                    'withExpire',      // Contrato que expira ao final do tempo (sem renovação)
                    'polo'            // Contrto de polos
                ],
                default: 'renewedContract'
            },
            archive: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                allowNull: true
            },
            polo: {
                type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                }
            }
        }
    }
};

module.exports = Contracts;
