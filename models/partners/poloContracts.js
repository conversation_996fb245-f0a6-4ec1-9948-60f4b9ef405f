const mongoose = require('mongoose');

const PoloContracts = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'PoloContracts',
    fields: {
      name: {
        type: String,
        required: true,
      },
      status: {
        type: String,
        enum: [
          'expired',
          'contract-pending',
          'signed',
        ],
        default: 'contract-pending',
      },
      file: {
        type: String,
        default: 'active',
      },
      partner: {
        type: {
          _id: mongoose.SchemaTypes.ObjectId,
          _cpf: String,
          name: String,
        },
      },
      userName: {
        type: String,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      signDate: {
        type: Date,
      },
      signIp: {
        type: String,
      },
      expiredDate: {
        type: Date,
      },
      isActive: {
        type: Boolean,
        required: true,
        default: true,
      },
      isVisible: {
        type: Boolean,
        required: true,
        default: true,
      },
      typeContract: {
        type: String,
        enum: ['contrato', 'aditivo'],
        default: 'contrato',
      },
      _poloContractId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      _poloId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      certifier: {
        type: {
          _id: mongoose.SchemaTypes.ObjectId,
          name: String,
          alias: String,
        },
      },
      metadata: {
        type: mongoose.SchemaTypes.Mixed,
      },
    },
  },
};

module.exports = PoloContracts;
