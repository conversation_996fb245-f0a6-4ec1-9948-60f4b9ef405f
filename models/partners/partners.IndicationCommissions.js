let IndicationCommissions = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'IndicationCommissions',
        fields    : {
            _certifierName : {
                type     : String,
                required : true,
                lowercase: true
            },
            _courseTypeName: {
                type     : String,
                required : true,
                lowercase: true
            },
            master         : {
                type    : String,
                required: true
            },
            level1         : {
                type    : String,
                required: true
            },
            max            : {
                master: {
                    type    : Number,
                    required: true
                },
                level1: {
                    type    : Number,
                    required: true
                }
            },
            med            : {
                master: {
                    type    : Number,
                    required: true
                },
                level1: {
                    type    : Number,
                    required: true
                }
            },
            min            : {
                master: {
                    type    : Number,
                    required: true
                },
                level1: {
                    type    : Number,
                    required: true
                }
            },
            isActive       : {
                type   : Boolean,
                default: true
            }
        }
    }
};

module.exports = IndicationCommissions;
