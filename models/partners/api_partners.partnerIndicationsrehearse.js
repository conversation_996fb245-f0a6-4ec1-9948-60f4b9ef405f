const mongoose = require('mongoose');

let PartnerIndicationsRehearse = {
    functions: {},
    database: {
        collection: 'PartnerIndicationsRehearse',
        connection: 'database_piaget',
        fields    : {
            cpf           : {
                type    : String,
                required: true
            },
            amount        : {
                type    : Number,
                required: true
            },
            year          : {
                type    : Number,
                required: true
            },
            month         : {
                type    : Number,
                required: true
            },
            minSales      : {
                type    : Number,
                required: true
            },
            sales         : {
                type    : Number,
                required: true
            },
            success       : {
                type    : Boolean,
                required: true
            },
            _futureCharges: {
                type    : [mongoose.SchemaTypes.ObjectId],
                // required: false,
                default : []
            }
        }
    }
};

module.exports = PartnerIndicationsRehearse;
