const mongoose = require('mongoose');

let WebOrdersPlans = {
    functions: {
        changeGenerateCharges: function (order) {
            if (order.status === 'canceled' || order.status === 'blocked' || order.status === 'expired') {
                order.generateCharges = false;
            }

            return order.save();
        }
    },
    database: {
        collection: 'WebOrdersPlans',
        connection: 'database_piaget',
        fields: {
            partner: {
                cpf: {
                    type: String,
                    required: true,
                    maxLength: 11,
                    minLength: 11,
                    index: true
                },
                name: {
                    type: String,
                    required: true
                }
            },
            credentials: {
                user: String,
                password: String
            },
            sites: [
                {
                    _id: false,
                    creationDate: {
                        type: Date,
                    },
                    url: {
                        type: String
                    },
                    certifier: {
                        type: String
                    },
                    webOrderId: mongoose.SchemaTypes.ObjectId
                }
            ],
            creationDate: Date,
            isSigned: {
                type: Boolean,
                default: false
            },
            contract: String,
            status: {
                type: String,
                enum: [
                    'solicitated',
                    'awaiting_signature',
                    'waiting_payment',
                    'active',
                    'canceled',
                    'blocked',
                    'expired',
                    'contacted'
                ],
                default: 'solicitated',
                index: true
            },
            expiredIn: {
                type: Date
            },
            generateCharges: {
                type: Boolean,
                default: true
            },
            plan: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId
                },
                name: {
                    type: String
                },
                timeDurationMonths: {
                    type: Number,
                    default: 12
                },
                services: {
                    type:  {
                        name: {
                            type: String
                        },
                        alias: {
                            type: String
                        },
                        description: {
                            type: String
                        }
                    }
                },
                amount: {
                    type: Number,
                },
                maxAmount: {
                    type: Number,
                },
                percent : {
                    type: Number,
                },
                rates: {
                    loot: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    },
                    boletoLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    },
                    pixLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    },
                    creditCardLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    },
                    debitCardLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    },
                    cardRecurrenceLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                        }
                    }
                },
                employeeLimit: {
                    freeQuantity: {
                        type: Number
                    },
                    valueByEmployee: {
                        type: Number
                    },
                    useEmployeeLimit: {
                        type: Boolean,
                        default: false
                    }
                },
                yearlyAmount: {
                    type: Number
                },
                configSites: {
                    hasWebSites: {
                        type: Boolean
                    },
                    freeQuantity: {
                        type: Number
                    },
                    valueBySite: {
                        type: Number
                    }
                }
            },
            observacao: {
                type: String,
            },
            signature: {
                ip: {
                    type: String
                },
                signatureDate: {
                    type: Date
                },
                signName: {
                    type: String
                },
            },
            numberOfEmployees: {
                type: Number
            },
            numberOfSites: {
                type: Number
            },
            amount: {
                type: Number
            },
            choosePaymentPlan: {
                typePayment: {
                    type: String
                },
                methodPayment: {
                    type: String
                },
                parcel: {
                    type: Number
                }
            },
            cardToken: {
                type: mongoose.SchemaTypes.ObjectId
            }
        },
        indexes: [
            {
                fields: {
                    'partner.cpf': 1,
                    status: 1
                },
                options: {
                    unique: 'already_web_order_plan',
                    name: 'dup_web_order_plan'
                }
            }
        ],
        options: {
            timestamp: true
        },
        post: {}
    }
};

WebOrdersPlans.database.post = {
    save: [
        WebOrdersPlans.functions.changeGenerateCharges,
    ],
    updateOne: [
        WebOrdersPlans.functions.changeGenerateCharges,
    ],
};

module.exports = WebOrdersPlans;
