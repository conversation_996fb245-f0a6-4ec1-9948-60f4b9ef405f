const mongoose = require('mongoose');

let PartnersHistory = {
    functions: {},
    database: {
        collection: 'PartnersHistory',
        connection: 'database_piaget',
        fields    : {
            name             : {
                type    : String,
                // required: false,
                default: undefined
            },
            code             : {
                type    : String,
                // required: false,
                default: undefined
            },
            archives         : {
                cpf    : {
                    type: String
                },
                rg     : {
                    type: String
                },
                address: {
                    type: String
                },
                inss   : {
                    type: String
                },
                pis    : {
                    type: String
                },
            },
            documents        : {
                cpf      : {
                    type    : String,
                    // required: false,
                    length  : 11,
                    default: undefined
                },
                rg       : {
                    dispatcher: {
                        type     : String,
                        required : false,
                        uppercase: false,
                        default: undefined
                    },
                    code      : {
                        type    : String,
                        // required: false,
                        default: undefined
                    },
                },
                numberPis: {
                    type: String,
                    default: undefined
                }
            },
            birthDate        : {
                type: Date,
                default: undefined
            },
            motherName       : {
                type: String,
                default: undefined
            },
            civilStatus      : {
                type    : String,
                // required: false,
                enum    : [
                    'single',
                    'married',
                    'divorced',
                    'widower'
                ],
                default: undefined
            },
            gender           : {
                type    : String,
                // required: false,
                enum    : [
                    'f',
                    'm'
                ],
                default: undefined
            },
            contacts         : {
                phone      : {
                    type    : String,
                    // required: false,
                    default: undefined
                },
                secondPhone: {
                    type: String,
                    default: undefined
                }
            },
            addresses        : {
                state     : {
                    type    : String,
                    // required: false,
                    length  : 2
                },
                city      : {
                    type    : String,
                    // required: false
                },
                zone      : {
                    type    : String,
                    // required: false
                },
                street    : {
                    type    : String,
                    // required: false
                },
                number    : {
                    type    : Number,
                    // required: false
                },
                complement: {
                    type    : String,
                    // required: false
                },
                zip       : {
                    type    : Number,
                    // required: false,
                    length  : 8
                },
                lat       : {
                    type: String
                },
                lng       : {
                    type: String
                },
            },
            commissionType: {
                type: String,
                enum: ['priceRange', 'courseSubcategory'],
                // required: false,
                default: undefined
            },
            minRateEnrolment      : {
                type    : Number,
                // required: false,
                default: undefined
            },
            subcategoryCommissions:  {
                type: {
                    percentage: {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    _certifierName: {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    },
                    _courseSubcategoryName: {
                        type: String
                    },
                    
                },
                default: undefined
            },
            bonus: {
                type: {
                    creditCard: {
                        maxInstallment: {
                            type: Number,
                            default: undefined
                        },
                        value: {
                            type: Number,
                            default: undefined
                        }
                    },
                    boleto: {
                        maxInstallment: {
                            type: Number,
                            default: undefined
                        },
                        value: {
                            type: Number,
                            default: undefined
                        }
                    },
                    debitCard: {
                        maxInstallment: {
                            type: Number,
                            default: undefined
                        },
                        value: {
                            type: Number,
                            default: undefined
                        }
                    },
                    cardRecurrence: {
                        maxInstallment: {
                            type: Number,
                            default: undefined
                        },
                        value: {
                            type: Number,
                            default: undefined
                        }
                    },
                    pix: {
                        maxInstallment: {
                            type: Number,
                            default: undefined
                        },
                        value: {
                            type: Number,
                            default: undefined
                        }
                    },
                },
                default: undefined
            },
            commissions: {
                    type: [{
                        percentage: {
                            type: Number
                        },
                        percentageToLinkPolo: {
                            type: Number
                        },
                        _certifierName: {
                            type: String
                        },
                        _courseTypeName: {
                            type: String
                        }
                    }],
                    default: undefined
            },
            commissionMedium: {
                type: [{
                    percentage: {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    _certifierName: {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }],
                default: undefined
            },
            commissionMinimum: {
                type: [{
                    percentage: {
                        type: Number
                    },
                    percentageToLinkPolo: {
                        type: Number
                    },
                    _certifierName: {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }],
                default: undefined
            },
            rates                 : {
                type: {
                    loot                     : {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            // required: false
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    },
                    boletoLiquidation        : {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            // required: false
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    },
                    pixLiquidation           : {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            // required: false
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    },
                    creditCardLiquidation    : {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            // required: false
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    },
                    debitCardLiquidation     : {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            // required: false
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    },
                },
                default: undefined
            },
            _contractsId     : mongoose.SchemaTypes.ObjectId,
            occupationArea   : {
                type: [
                    {
                        state: {
                            type     : String,
                            length   : 2,
                            uppercase: true,
                            required : false
                        },
                        city : {
                            type    : String,
                            // required: false
                        }
                    }
                ],
                default: undefined
            },
            updatePartner: {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            status           : {
                type   : String,
                default: 'pre-registration'
            },
            isActive         : {
                type   : Boolean
            },
            _userId          : {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            advanceInstallmentsContract : {
                type    : Boolean,
                // required: false,
                default: undefined
            },
            advanceInstallmentsConfig: {
                type:{
                    _userCreationId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    percentageAmount: [
                        {
                            installment: Number,
                            value: Number
                        }
                    ],
                    maxAmountMonthly: {
                        type: Number,
                        required: true
                    }
                },
                // required: false
            },
            description: {
                type: String,
                // required: false
            },
            employer: {
                _userId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _userType: {
                    type: String,
                    required: true
                },
                _userName: {
                    type: String,
                    required: true
                }
            }
        }
    }
}

module.exports = PartnersHistory;
