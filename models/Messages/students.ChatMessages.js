const mongoose = require('mongoose');

let ChatMessages = {
    functions: {},
    database: {
        collection: 'ChatMessages',
        fields    : {
            message     : {
                type    : String,
                required: true
            },
            _studentId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            _destinyStudent: {
                type: mongoose.SchemaTypes.ObjectId
            },
            date: {
                type: Date,
                required: true
            },
            certifier: {
                type: String,
                required: true
            },
            area: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = ChatMessages;
