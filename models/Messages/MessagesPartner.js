const mongoose = require('mongoose');

let MessagesPartner = {
    functions: {},
    database: {
        collection: 'MessagesPartner',
        connection: 'database_piaget',
        fields    : {
            _userId       : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userType     : {
                type    : String,
                enum    : [
                    'student',
                    'partner',
                    'teacher',
                    'employer'
                ],
                required: true
            },
            _certifierName: {
                type    : String,
                // required: false
            },
            title         : {
                type    : String,
                required: true
            },
            message       : {
                type    : String,
                required: true
            },
            sender        : {
                _userId         : mongoose.SchemaTypes.ObjectId,
                _userName       : {
                    type    : String,
                    required: true
                },
                _departamentName: {
                    type    : String,
                    required: true
                }
            },
            metadata      : mongoose.SchemaTypes.Mixed,
            isRead        : {
                type    : Boolean,
                // required: false,
                default : false
            }
        }
    }
};

module.exports = MessagesPartner;