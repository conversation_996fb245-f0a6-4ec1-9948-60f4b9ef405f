const mongoose = require('mongoose');

let DiplomaInternalLots = {
  database: {
    connection: 'database_piaget',
    collection: 'DiplomaInternalLots',
    fields: {
      description: {
        type: String,
        required: true,
      },
      number: {
        type: Number,
        required: true,
      },
      year: {
        type: Number,
        required: true,
      },
      isOpen: {
        type: Boolean,
        default: true,
        // required: false,
      },
      history: [
        {
          isOpen: {
            type    : Boolean,
            required: true
          },
          description: String,
          _userId: {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _userName: {
            type    : String,
            required: true
          },
          _userType: {
            type      : String,
            required  : true,
            enum      : ['employer', 'computer']
          },
          launchedAt: {
            type    : Date,
            required: true,
            default : new Date()
          }
        }
      ],
    }
  }
};

module.exports = DiplomaInternalLots;
