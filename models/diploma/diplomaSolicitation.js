const mongoose = require('mongoose');
const moment = require('moment');

let DiplomaSolicitations = {
  functions: {
    generateUniqueId: function(next) {

      if (this.idDiploma) return next();

      const _getRandomNumber = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
      const currentYear = moment().year();
      const numbers = currentYear.toString().split(''); // Inicializa o array com ano atual. Ex.: ['2', '0', '2', '4']

      while (numbers.length < 44) numbers.push(_getRandomNumber(0, 9));

      this.idDiploma = numbers.join('');

      return next();
    }
  },
  database: {
    connection: 'database_piaget',
    collection: 'DiplomaSolicitations',
    fields: {
      idDiploma: {
        type    : String,
        unique  : true,
        minlength: 44,
        maxlength: 44,
        validate: /[0-9]{44}/,
      },
      _enrolmentId: {
        type    : mongoose.SchemaTypes.ObjectId,
        required: true
      },
      type: {
        type: String
      },
      dataStudent: {
        name: {
          type    : String,
          required: true
        },
        cpf: {
          type    : String,
          required: true
        },
        studentRegistrationCode: {
          type    : Number,
          // required: false
        },
      },
      dataCourse: {
        certifier: {
          type    : String,
          required: true
        },
        courseType: {
          type    : String,
          required: true
        },
        course: {
          type    : String,
          required: true
        },
      },
      status: {
        type: String,
        enum: [
          'pending', // Pendente
          'waiting_signature', // Aguardando Assinaturas
          'waiting_register', // Aguardando Registro
          'sent_register', // Enviado a Registradora
          'approved' // Finalizado
        ],
        required: true,
        default : 'pending'
      },
      internalLot: {
        // required: false,
        type: {
          _diplomaInternalLotId: {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          description: {
            type    : String,
            required: true
          },
          number: {
            type    : Number,
            required: true
          },
          year: {
            type    : Number,
            required: true
          },
        }
      },
      registerLot: {
        // required: false,
        type: {
          _diplomaRegisterLotId: {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          description: {
            type    : String,
            required: true
          },
          number: {
            type    : Number,
            required: true
          },
          year: {
            type    : Number,
            required: true
          },
        }
      },
      history: [
        {
          status: {
            type    : String,
            required: true
          },
          description: String,
          _userId: {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _userName: {
            type    : String,
            required: true
          },
          _userType: {
            type      : String,
            required  : true,
            enum      : ['student', 'employer', 'computer']
          },
          launchedAt: {
            type    : Date,
            required: true,
            default : new Date()
          }
        }
      ],
      codigoValidacao: {
        type: String,
      },
      userIp: String,
      metadata: mongoose.SchemaTypes.Mixed
    }
  }
};

DiplomaSolicitations.database.pre = {
  save: [
    DiplomaSolicitations.functions.generateUniqueId,
  ],
};

module.exports = DiplomaSolicitations;
