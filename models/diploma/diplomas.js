const mongoose = require('mongoose');

let Diplomas = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'Diplomas',
    fields: {
      _diplomaSolicitationId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
        unique: true,
      },
      doc: {
        type: String,
        required: true
      },
      dip: {
        type: String,
        required: true
      },
      docFileUrl: {
        type: String,
        required: true
      },
      dipFileUrl: {
        type: String,
        required: true
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      _userName: {
        type: String,
        required: true
      },
      _userType: {
        type: String,
        required: true,
        enum: ['employer', 'computer']
      },
    }
  }
};

module.exports = Diplomas;
