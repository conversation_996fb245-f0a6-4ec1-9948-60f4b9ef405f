const mongoose = require('mongoose');

let DocumentacaoAcademicaRegistro = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'DocumentacaoAcademicaRegistro',
    fields: {
      _diplomaSolicitationId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      xml: {
        type: String,
        required: true
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      _userName: {
        type: String,
        required: true
      },
      _userType: {
        type: String,
        required: true,
        enum: ['student', 'employer', 'computer']
      },
    }
  }
};

module.exports = DocumentacaoAcademicaRegistro;
