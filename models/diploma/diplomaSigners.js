let DiplomaSigners = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'DiplomaSigners',
    fields: {
      isActive: {
        type: Boolean,
        required: true,
        default: true,
      },
      order: {
        type: Number,
        required: true
      },
      section: {
        type: String,
        required: true,
        enum: ['DadosDiploma', 'DocumentacaoAcademicaRegistro']
      },
      name: {
        type: String,
        required: true
      },
      document: {
        type: String,
        required: true
      },
      filename: {
        type: String,
        required: true
      },
      password: {
        type: String,
        required: true
      },
    }
  }
};

module.exports = DiplomaSigners;
