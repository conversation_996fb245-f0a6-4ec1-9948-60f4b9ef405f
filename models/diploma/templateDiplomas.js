let TemplateDiplomas = {
  database: {
    connection: 'database_piaget',
    collection: 'TemplateDiplomas',
    fields: {
      isActive: {
        type: Boolean,
        default: true,
        // required: false,
      },
      background: {
        type: String, // imagem jpg em base64
        required: true,
      },
      front: {
        type: String, // template ejs
        required: true,
      },
      back: {
        type: String, // template ejs
        required: true,
      },
      configurations: {
        pageWidth: {
          type: Number,
          default: 297,
          required: true,
        },
        pageHeight: {
          type: Number,
          default: 210,
          required: true,
        },
        dpi: {
          type: Number,
          default: 72,
          required: true,
        },
      },
      history: [
        {
          _userName: {
            type: String,
            required: true
          },
          _userType: {
            type: String,
            required: true,
            enum: ['employer', 'computer']
          },
          launchedAt: {
            type: Date,
            required: true,
            default: new Date(),
          }
        }
      ],
    }
  }
};

module.exports = TemplateDiplomas;
