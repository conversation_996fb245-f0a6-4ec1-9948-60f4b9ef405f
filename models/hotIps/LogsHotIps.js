const {SchemaTypes} = require('mongoose');

let LogsHotIps = {
  database: {
    collection: 'LogsHotIps',
    connection: 'database_piaget',
    fields: {
      _hotIpId: {
        type: SchemaTypes.ObjectId,
        required: false,
        default: null,
      },
      action: {
        type: String,
        required: true
      },
      before: {
        type: SchemaTypes.Mixed,
        required: false,
        default: null,
      },
      after: {
        type: SchemaTypes.Mixed,
        required: true
      },
      date: {
        type: Date,
        required: true
      },
      user: {
        type: {
          _id: {
            type: SchemaTypes.ObjectId,
            required: true
          },
          name: {
            type: String,
            required: true
          }
        },
        required: true
      },
    }
  }
}

module.exports = LogsHotIps;
