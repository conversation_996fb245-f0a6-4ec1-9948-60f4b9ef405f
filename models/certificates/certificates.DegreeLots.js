const mongoose = require('mongoose');

let DegreeLots = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'DegreeLots',
        fields    : {
            lotNumber: {
                type    : Number,
                required: true
            },
            _certifierAlias: {
                type: String,
                required: true
            },
            _shippingId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            isOpen: {
                type    : Boolean
            }
        }
    }
};

module.exports = DegreeLots;