let TemplateCertificates = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'TemplateCertificates',
    fields    : {
      _certifierName: {
        type: String,
        required: true
      },
      _courseTypeName: {
        type: String,
        required: true
      },
      contentFront: {
        type: String,
        required: true
      },
      contentBack: {
        type: String,
        required: true
      },
      pageWidth: {
        type: Number,
        required: true
      },
      pageHeight: {
        type: Number,
        required: true
      },
      frontSizes: {
        borderTop: {
          type: Number,
          required: true
        },
        borderRight: {
          type: Number,
          required: true
        },
        borderBottom: {
          type: Number,
          required: true
        },
        borderLeft: {
          type: Number,
          required: true
        }
      },
      backSizes: {
        borderTop: {
          type: Number,
          required: true
        },
        borderRight: {
          type: Number,
          required: true
        },
        borderBottom: {
          type: Number,
          required: true
        },
        borderLeft: {
          type: Number,
          required: true
        }
      },
      image: {
        type: String
      },
      signatureImage: {
        type: String
      },
      signatureOwner: {
        type: String
      },
      isActive: {
        type: Boolean,
        default: false
      },
      contentSignature: {
        type: String,
        required:false
      },
      headerSignaturePage: {
        type:String,
        required:false
      },
      imageOfSignature: {
        type:String,
        required:false
      },
      digital: {
        type: Boolean,
        required: true,
        default: false
      }
    }
  }
};

module.exports = TemplateCertificates;
