let TemplateKeys = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TemplateKeys',
        fields    : {
            name     : {
                type     : String,
                minlength: 1,
                index    : true
            },
            description: {
                type: String,
                lowercase: true,
                required: true
            },
            type: {
                type: String,
                enum: [
                    'static',
                    'iterative'
                ]
            },
            isActive: {
                type   : Boolean,
                default: true
            }
        }
    }
};

module.exports = TemplateKeys;
