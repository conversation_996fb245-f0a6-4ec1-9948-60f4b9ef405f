const mongoose = require('mongoose');

let Resolutions = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'Resolutions',
    fields: {
      certifier: {
        type: [String],
        required: true,
      },
      courseType: {
        type: [String],
        required: true,
      },
      degree: {
        type: [String],
        required: false,
        default: [],
      },
      modality: {
        type: [String],
        required: false,
        default: [],
      },
      dateStart: {
        type: Date,
        required: true,
      },
      dateEnd: {
        type: Date,
        required: false,
      },
      isActive:{
        type:Boolean,
        default: true
      },
      _courseId: {
        type: [mongoose.SchemaTypes.ObjectId],
        required: false,
        default: [],
      },
      _classId:{
        type: [mongoose.SchemaTypes.ObjectId],
        required: false,
        default: [],
      },
      type:{
        type: String,
        required: true,
      },
      content: {
        type: String,
        required: true,
      },
      order: {
        type: Number,
        required: true,
      },
      accumulations: {
        type: [mongoose.SchemaTypes.ObjectId],
        required: false,
        default: [],
      },
      history: [
        {
          _id: false,
          data: mongoose.SchemaTypes.Mixed,
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          _userName: {
            type: String,
            required: true,
          },
          _userType: {
            type: String,
            required: true,
            enum: [
              'student',
              'employer',
              'computer',
            ]
          },
          launchedAt: {
            type: Date,
            required: true,
            default: new Date(),
          }
        }
      ],
    },
    indexes: [
      {
        // fields : {
        //   'certifier': 1,
        //   'courseType': 1,
        // },
        // options: {
        //   unique: true,
        // }
      },
    ]
  }
};

module.exports = Resolutions;
