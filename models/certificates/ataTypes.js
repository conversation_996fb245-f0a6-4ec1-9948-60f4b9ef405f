let AtaTypes = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'AtaTypes',
        fields    : {
            name: {
                type    : String,
                unique  : 'Este nome de ata já existe',
                required: true
            },
            alias : {
                type     : String,
                required : true,
                maxlength: 50,
                lowercase: true,
                trim     : true,
                unique   : 'Já existe uma ata com este alias',
                index    : true
            },
            isActive: {
                type: Boolean,
                required: true,
                default: true
            }
        }
    }
};

module.exports = AtaTypes;
