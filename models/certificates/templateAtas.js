let TemplateAtas = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TemplateAtas',
        fields    : {
            content: {
                type    : String,
                required: true
            },
            pageWidth   : {
                type    : Number,
                required: true
            },
            pageHeight : {
                type: Number,
                required: true
            },
            header: {
                type     : String,
                maxlength: 255,
                lowercase: true
            },
            footer: {
                type: String
            },
            signature: {
                type: String,
                maxlength: 255,
                lowercase: true
            },
            _ataTypeAlias: {
                type    : String,
                required: true
            },
            _certifierAlias: {
                type     : String,
                lowercase: true,
                required : true
            },
            _courseTypeAlias: {
                type: String,
                lowercase: true,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true,
                required: true
            }
        }
    }
};

module.exports = TemplateAtas;
