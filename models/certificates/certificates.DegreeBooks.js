let DegreeBooks = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'DegreeBooks',
        fields: {
            bookNumber: {
              type: Number,
              required: true
            },
            lastRegistry: {
                type: Number,
                required: true,
                default: 0
            },
            endDate: {
                type: Date
            },
            year: {
                type: Number,
                required: true
            },
            city: {
                type    : String,
                required: true
            },
            state: {
                type: String,
                required: true
            },
            _certifierAlias : {
                type    : String,
                required: true
            },
            _courseTypeAlias: {
                type    : String,
            },
            isOpen: {
                type: Boolean,
            }
        }
    }
};

module.exports = DegreeBooks;