const mongoose = require('mongoose');

let Lots = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Lots',
        fields    : {
            lotNumber: {
                type    : Number,
                required: true
            },
            _certifierAlias: {
                type: String,
                required: true
            },
            _shippingId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            isOpen: {
                type    : Boolean
            }
        }
    }
};

module.exports = Lots;