const mongoose = require('mongoose');

let DegreeStatus = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'DegreeStatus',
        fields    : {
            status: {
                type: String,
                enum: [
                    'waiting_printer',
                    'waiting_signature',
                    'waiting_shipping',
                    'sent_student',
                    'digitalized',
                    'not_exists_teacher_default',
                    'not_exists_master_teacher',
                    'teacher_published_more_than_2_times',
                    'teacher_specialist_more_than_2_times'
                ],
                required: true
            },
            metadata: mongoose.SchemaTypes.Mixed,
            _degreeId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userType: {
                type: String,
                enum: [
                    'student',
                    'partner',
                    'teacher',
                    'employer',
                    'computer'
                ],
                required: true
            },
            _userName: {
                type: String,
                required: true
            }
        }
    }
};

module.exports = DegreeStatus;