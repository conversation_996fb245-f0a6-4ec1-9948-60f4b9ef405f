const mongoose = require('mongoose');

let Certificates = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'Certificates',
    fields: {
      _certifierAlias: {
        type: String,
        required: true,
        lowercase: true
      },
      _courseTypeAlias: {
        type: String,
        required: true,
        lowercase: true
      },
      _requestId: {
        type: mongoose.SchemaTypes.ObjectId
      },
      _templateCertificateId: {
        type: mongoose.SchemaTypes.ObjectId
      },
      _lotNumber: {
        type: Number
      },
      _termId: {
        type: mongoose.SchemaTypes.ObjectId
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      dataStudent: {
        studentRegistrationCode: {
          type: Number,
          default: 0,
        },
        ip: {
          type: String,
        },
        name: {
          type: String,
          required: true
        },
        rg: {
          type: String
        },
        cpf: {
          type: String,
          required: true
        },
        birthDate: {
          type: Date
        },
        naturalessCity: {
          type: String
        },
        naturalessState: {
          type: String
        },
        nationality: {
          type: String
        },
        address: {
          street: {
            type: String
          },
          number: {
            type: String
          },
          complement: {
            type: String
          },
          zone: {
            type: String
          },
          zip: {
            type: String
          },
          city: {
            type: String
          },
          uf: {
            type: String
          }
        }
      },
      dataCourse: {
        certifier: {
          type: String
        },
        courseType: {
          type: String
        },
        course: {
          type: String
        },
        workload: {
          type: Number
        },
        theoreticalWorkload: {
          type: Number,
        },
        practicalWorkload: {
          type: Number,
        },
        extensionWorkload: {
          type: Number,
        },
        startDate: {
          type: Date,
          required: true
        },
        endDate: {
          type: Date,
          required: true,
        },
        emissionDate: {
          type: Date,
        },
        category: {
          type: String,
          enum: [
            'Lato Sensu',
            'Stricto Sensu'
          ]
        },
        knowledgeArea: {
          type: String
        },
        objective: {
          type: String,
          default: null
        },
        program: {
          type: String,
          default: null
        },
        apostille: {
          type: String,
        }
      },
      bookRegistry: {
        book: {
          type: Number
        },
        sheet: {
          type: Number
        },
        registry: {
          type: Number
        },
        code: {
          type: String
        }
      },
      disciplines: [
        {
          discId: {
            type: mongoose.SchemaTypes.ObjectId
          },
          _name: {
            type: String
          },
          workload: {
            type: Number
          },
          frequency: {
            type: Number,
            default: 100
          },
          grade: {
            type: Number
          },
          teacherMode: {
            type: String
          },
          teacher: {
            type: String
          },
          titration: {
            type: String
          }
        }
      ],
      tcc: {
        typeTcc: {
          type: String,
          enum: [
            'article',
            'monography'
          ],
        },
        title: {
          type: String
        },
        grade: {
          type: Number
        }
      },
      locale: {
        city: {
          type: String
        },
        state: {
          type: String
        }
      },
      status: {
        type: String,
        enum: [
          'digitalized',
          'waiting_printer',
          'waiting_signature',
          'waiting_shipping',
          'sent_student',
          'returned',
          'not_exists_teacher_default',
          'teacher_published_more_than_2_times',
          'not_exists_master_teacher',
          'teacher_specialist_more_than_2_times',
          'canceled'
        ],
        required: true,
        default: 'waiting_printer'
      },
      type: {
        type: String,
        enum: [
          'digital',
          'print'
        ],
        default: 'print'
      },
      linkStorage: { // Atributo apenas para certificados digital
        type: String
      },
      baseCertificate: {
        type: String
      },
      dataShipping: {
        shippingType: {
          type: String,
          enum: [
            'common',
            'sedex',
            'in_hands',
            'common_registry'
          ]
        },
        trackingCode: String,
        labelCode: String,
        service: {
          _code: {
            type: String
          },
          _serviceId: {
            type: mongoose.SchemaTypes.ObjectId
          },
          name: {
            type: String
          }
        },
        amount: {
          type: Number
        },
        deadline: {
          type: Number
        },
        delivery: {
          saturday: {
            type: Boolean
          },
          residence: {
            type: Boolean
          }
        }
      },
      accessCode: {
        type: String,
        index: { unique: true, sparse: true },
        unique: 'access_code_already_exists'
      },
      portaria: {
        type: String,
      },
      observations: {
        type: String,
      },
      resolution: {
        type: String,
      },
      metadata: mongoose.SchemaTypes.Mixed
    },
    indexes: [
      {
        fields: {
          'bookRegistry.book': 1,
          'bookRegistry.sheet': 1,
          'bookRegistry.registry': 1,
          'metadata.manualUpdate': 1,
          'type': 1,
          '_certifierAlias': 1
        },
        options: {
          unique: 'certificate_already_exists',
          name: 'certificate_already_exists',
          sparse: true
        }
      }
    ]
  }
};

module.exports = Certificates;
