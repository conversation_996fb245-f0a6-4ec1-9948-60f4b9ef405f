const mongoose = require('mongoose');

let DegreeSolicitations = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'DegreeSolicitations',
    fields: {
      _enrolmentId: {
        type    : mongoose.SchemaTypes.ObjectId,
        required: true
      },
      dataStudent: {
        name: {
          type    : String,
          required: true
        },
        cpf: {
          type    : String,
          required: true
        },
        ip: {
          type    : String,
        },
        rg: {
          type    : String,
        },
        studentRegistrationCode: {
          type    : Number,
        },
        naturalessCity: {
          type    : String,
        },
        naturalessState: {
          type    : String,
        },
        nationality: {
          type    : String,
        },
        birthDate: {
          type    : Date,
        },
        address: {
          street      : String,
          number      : String,
          complement  : String,
          zone        : String,
          zip         : String,
          city        : String,
          uf          : String
        }
      },
      dataCourse: {
        certifier: {
          type    : String,
          required: true
        },
        courseType: {
          type    : String,
          required: true
        },
        course: {
          type    : String,
          required: true
        },
        knowledgeArea: {
          type    : String,
        },
        workload: {
          type    : Number,
        },
        startDate: {
          type    : Date,
        },
        endDate: {
          type    : Date,
        },
        objective: {
          type: String,
          default: null
        },
        program: {
          type: String,
          default: null
        }
      },
      status: {
        type: String,
        enum: [
          'waiting_approval',
          'approved',
          'canceled',
        ],
        required: true,
        default : 'waiting_approval'
      },
      type: {
        type: String,
        enum: [
          'digital',
          'print'
        ]
      },
      dataShipping: {
        type: {
          shippingType: {
            type: String,
            enum: [
              'common',
              'sedex',
              'in_hands',
              'common_registry'
            ]
          },
          labelCode: String,
          service: {
            _code: {
              type: String
            },
            _serviceId: {
              type: mongoose.SchemaTypes.ObjectId
            },
            name: {
              type: String
            }
          },
          amount: {
            type: Number
          },
          deadline: {
            type: Number
          },
          delivery: {
            saturday: {
              type: Boolean
            },
            residence: {
              type: Boolean
            }
          }
        },
      },
      disciplines: [
        {
          _name: {
            type: String
          },
          workload: {
            type: Number
          },
          frequency: {
            type: Number,
            default: 100
          },
          grade: {
            type: Number
          },
          teacher: {
            type: String
          },
          titration: {
            type: String
          }
        }
      ],
      fileUploads: {
        type: [
          {
            name: {
              type: String,
              required: true
            },
            path: {
              type: String,
              required: true
            }
          }
        ],
      },
      tcc: {
  
        type: {
          typeTcc: {
            type: String,
            enum: [
              'article',
              'monography'
            ],
          },
          title: {
            type: String
          },
          grade: {
            type: Number
          }
        },
      },
      bookRegistry: {
        type: {
          book: {
            type: Number
          },
          sheet: {
            type: Number
          },
          registry: {
            type: Number
          }
        },
      },
      history: [
        {
          status: {
            type    : String,
            required: true
          },
          description: String,
          _userId: {
            type    : mongoose.SchemaTypes.ObjectId,
            required: true
          },
          _userName: {
            type    : String,
            required: true
          },
          _userType: {
            type      : String,
            required  : true,
            enum      : ['student', 'employer', 'computer']
          },
          launchedAt: {
            type    : Date,
            required: true,
            default : new Date()
          }
        }
      ],
      metadata: mongoose.SchemaTypes.Mixed
    }
  }
};

module.exports = DegreeSolicitations;
