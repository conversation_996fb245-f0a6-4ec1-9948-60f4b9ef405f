const mongoose = require('mongoose');

let WebPlans = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebPlans',
        fields: {
            name: {
                type: String,
                required: true,
                index: true
            },
            services: {
                type: {
                    _id: false,
                    name: {
                        type: String,
                        required: true
                    },
                    alias: {
                        type: String,
                        required: true
                    },
                    _servicesId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    description: {
                        type: String,
                        required: true
                    }
                },
                required: true
            },
            amount: {
                type: Number,
                default: 0
            },
            _webTemplateContract: {
                _id: false,
                _webTemplateId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                alias: {
                    type: String,
                    required: true
                }
            },
            timeDurationMonths: {
                type: Number,
                default: 12
            },
            maxAmount: {
                type: Number,
                default: 0
            },
            percent: {
                type: Number,
                default: 0
            },
            isActive: {
                type: Boolean,
                default: true
            },
            employeeLimit: {
                freeQuantity: {
                    type: Number
                },
                valueByEmployee: {
                    type: Number
                },
                useEmployeeLimit: {
                    type: Boolean,
                    default: false
                },
                defaultEmployers: {
                    type: Number
                }
            },
            configApi: {
                freeQuantity: {
                    type: Number
                },
                valueByApi: {
                    type: Number
                },
                hasApi: {
                    type: Boolean,
                    default: false
                }
            },
            rates : {
                loot                     : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                },
                boletoLiquidation        : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                },
                pixLiquidation           : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                },
                creditCardLiquidation    : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                },
                debitCardLiquidation     : {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                },
                cardRecurrenceLiquidation: {
                    type : {
                        type    : String,
                        enum    : [
                            'value',
                            'percentage'
                        ],
                        required: true
                    },
                    value: {
                        type    : Number
                    }
                }
            },
            yearlyAmount: {
                type: Number,
                required: true
            },
            typesOfPayments: {
                type: [String],
                required: true
            },
            typesOfPaymentsYearly: {
                type: [String],
                required: true
            },
            configSites: {
                hasWebSites: {
                    type: Boolean
                },
                freeQuantity: {
                    type: Number
                },
                valueBySite: {
                    type: Number
                }
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = WebPlans;
