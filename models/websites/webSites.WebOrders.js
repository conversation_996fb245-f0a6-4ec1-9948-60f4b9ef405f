const mongoose = require('mongoose');

let WebOrders = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebOrders',
        fields: {
            name: {
                type: String,
                required: true
            },
            cpf: {
                type: String,
                required: true,
                maxLength: 11,
                minLength: 11,
                index: true
            },
            _webPlanId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _certifierName: {
                type: String,
                required: true
            },
            service: {
                type: [String],
                required: true
            },
            credentials: {
                user: String,
                password: String
            },
            urlSite: String,
            creationDate: Date,
            isSigned: {
                type: Boolean,
                default: false
            },
            contract: String,
            status: {
                type: String,
                enum: [
                    'solicitated',
                    'in_dev',
                    'active',
                    'canceled',
                    'blocked',
                    'expired',
                    'contacted'
                ],
                default: 'solicitated'
            },
            observacao: {
                type: String,
                // required: false
            },
            _orderPlan: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = WebOrders;
