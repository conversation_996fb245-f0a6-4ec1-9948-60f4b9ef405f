const mongoose = require('mongoose');

let WebBillings = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebBillings',
        fields: {
            _webChargeId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _billingId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            payment: {
                metadata: mongoose.SchemaTypes.Mixed,
                type: {
                    type: String,
                    required: true
                },
                operator: {
                    type: String,
                    required: true
                }
            },
            amount: {
                type: Number,
                required: true
            },
            amountPaid: Number,
            paymentDate: Date,
            status: {
                type: String,
                enum: [
                    'waiting_payment',
                    'paid'
                ],
                default: 'waiting_payment'
            }
        },
        options: {
            timestamp: true
        }
    }
}

module.exports = WebBillings;
