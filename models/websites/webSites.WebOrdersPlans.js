const mongoose = require('mongoose');

let WebOrdersPlans = {
    functions: {
        changeGenerateCharges: async function(order) {
            if (order && order.status && (order.status === 'canceled' || order.status === 'expired')) {
                order.generateCharges = false;
        
                return order.save();
            }
        }
    },
    database: {
        collection: 'WebOrdersPlans',
        connection: 'database_piaget',
        fields: {
            partner: {
                cpf: {
                    type: String,
                    required: true,
                    maxLength: 11,
                    minLength: 11,
                    index: true
                },
                name: {
                    type: String,
                    required: true
                }
            },
            credentials: {
                user: String,
                password: String
            },
            sites: [
                {
                    _id: false,
                    creationDate: {
                        type: Date,
                        // required: false
                    },
                    url: {
                        type: String
                    },
                    certifier: {
                        type: String
                    },
                    webOrderId: mongoose.SchemaTypes.ObjectId
                }
            ],
            api: {
                _id: false,
                creationDate: {
                    type: Date,
                    // required: false
                },
                url: {
                    type: String
                },
                webOrderId: mongoose.SchemaTypes.ObjectId
            },
            creationDate: Date,
            isSigned: {
                type: Boolean,
                default: false
            },
            contract: String,
            status: {
                type: String,
                enum: [
                    'solicitated',
                    'awaiting_signature',
                    'waiting_payment',
                    'active',
                    'canceled',
                    'blocked',
                    'expired',
                    'contacted'
                ],
                default: 'solicitated',
                index: true
            },
            expiredIn: {
                type: Date
            },
            generateCharges: {
                type: Boolean,
                default: false
            },
            plan: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    index: true
                },
                name: {
                    type: String
                },
                timeDurationMonths: {
                    type: Number,
                    default: 12
                },
                services: {
                    type: {
                        name: {
                            type: String
                        },
                        alias: {
                            type: String
                        },
                        description: {
                            type: String
                        }
                    },
                    required: true
                },
                amount: {
                    type: Number,
                    // required: false
                },
                maxAmount: {
                    type: Number,
                    // required: false
                },
                percent: {
                    type: Number,
                    // required: false
                },
                rates: {
                    loot: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type: Number,
                            // required: false
                        }
                    },
                    boletoLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type: Number,
                            // required: false
                        }
                    },
                    pixLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type: Number,
                            // required: false
                        }
                    },
                    creditCardLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type: Number,
                            // required: false
                        }
                    },
                    debitCardLiquidation: {
                        type: {
                            type: String,
                            enum: [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type: Number,
                            // required: false
                        }
                    },
                    cardRecurrenceLiquidation: {
                        type : {
                            type    : String,
                            enum    : [
                                'value',
                                'percentage'
                            ],
                            required: true
                        },
                        value: {
                            type    : Number,
                            // required: false
                        }
                    }
                },
                employeeLimit: {
                    freeQuantity: {
                        type: Number
                    },
                    valueByEmployee: {
                        type: Number
                    },
                    useEmployeeLimit: {
                        type: Boolean,
                        default: false
                    },
                    defaultEmployers: {
                        type: Number
                    }
                },
                yearlyAmount: {
                    type: Number
                },
                configSites: {
                    hasWebSites: {
                        type: Boolean
                    },
                    freeQuantity: {
                        type: Number
                    },
                    valueBySite: {
                        type: Number
                    }
                },
                configApi: {
                    freeQuantity: {
                        type: Number
                    },
                    valueByApi: {
                        type: Number
                    },
                    hasApi: {
                        type: Boolean,
                        default: false
                    }
                },
                typesOfPayments: {
                    type: [String],
                    required: true
                },
                typesOfPaymentsYearly: {
                    type: [String],
                    required: true
                }
            },
            observacao: {
                type: String,
                // required: false
            },
            signature: {
                ip: {
                    type: String
                },
                signatureDate: {
                    type: Date
                },
                signName: {
                    type: String
                },
                // required: false
            },
            numberOfEmployees: {
                type: Number
            },
            numberOfSites: {
                type: Number
            },
            defaultEmployers: {
                type: Number
            },
            amount: {
                type: Number
            },
            choosePaymentPlan: {
                typePayment: {
                    type: String
                },
                methodPayment: {
                    type: String
                },
                parcel: {
                    type: Number
                }
            },
            cardToken: {
                type: mongoose.SchemaTypes.ObjectId
            },
            additional: {
                employee: {
                    type: Number
                },
                sites: {
                    type: Number
                },
                api: {
                    type: Boolean
                },
                value: {
                    type: Number
                },
                launchAdditional: {
                    type: Boolean
                },
                generateCharge:  {
                    type: Boolean
                },
                dateLaunchedAdditional: {
                    type: Date
                }
            }
        },
        indexes: [
            {
                fields: {
                    'partner.cpf': 1,
                    status: 1,
                    'plan._id': 1
                },
                options: {
                    unique: 'already_web_order_plan',
                    name: 'dup_web_order_plan'
                }
            }
        ],
        options: {
            timestamp: true
        },
    }
};

WebOrdersPlans.database.post = {
    findOneAndUpdate: [
        WebOrdersPlans.functions.changeGenerateCharges,
    ],
    updateOne: [
        WebOrdersPlans.functions.changeGenerateCharges,
    ],
};

module.exports = WebOrdersPlans;
