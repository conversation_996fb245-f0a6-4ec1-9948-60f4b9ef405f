const mongoose = require('mongoose');

let WebTemplateContracts = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebTemplateContracts',
        fields: {
            name: {
                type: String,
                required: true
            },
            alias: {
                type: String,
                required: true
            },
            header: {
                type: String,
                required: true
            },
            content: {
                type: String,
                required: true
            },
            footer:{
                type: String
            },
            isEnabled:{
                type: Boolean,
                default:true
            },
            metadata: mongoose.SchemaTypes.Mixed
        },
        indexes   : [
            {
                fields : {
                    'alias': 1
                },
                options: {
                    unique: 'Contrato já cadastrado.'
                }
            }
        ],
        options   : {
            timestamps: true
        }
    }
};

module.exports = WebTemplateContracts;
