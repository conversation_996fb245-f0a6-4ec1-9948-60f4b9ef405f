let WebServices = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebServices',
        fields: {
            _name: {
                type: String,
                required: true,
                uppercase:true,
                trim: true
            },
            alias: {
                type: String,
                required: true,
                unique: 'Serviço já cadastrado.',
                trim: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            description: {
                type: String
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = WebServices;
