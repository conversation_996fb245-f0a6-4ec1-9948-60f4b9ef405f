const mongoose = require('mongoose');

let WebAssignors = {
    functions: {},
    database: {
        collection: 'WebAssignors',
        connection: 'database_piaget',
        fields: {
            assignor: {
                type: String,
                unique: 'assignor_already_exists',
                required: true
            },
            agency: {
                type: String,
                required: true
            },
            bank: {
                type: String,
                required: true
            },
            operator: {
                type: String,
                required: true,
                lowerCase: true
            },
            company: {
                cnpj: {
                    type: String,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                }
            },
            interest: {
                interestType: {
                    type: String,
                    required: true,
                    enum: [
                        'VALOR_POR_DIA',
                        'TAXA_MENSAL',
                        'ISENTO'
                    ]
                },
                amount: {
                    type: String,
                    required: true
                }
            },
            mulct: {
                percentage: {
                    type: String,
                    required: true
                }
            },
            afterExpiration: {
                action: {
                    type: String,
                    required: true,
                    enum: [
                        'PROTESTAR',
                        'DEVOLVER'
                    ]
                },
                numberOfDays: {
                    type: String,
                    required: true
                }
            },
            payment: {
                paymentType: {
                    type: String,
                    required: true,
                    enum: [
                        'ACEITA_QUALQUER_VALOR',
                        'ACEITA_VALORES_ENTRE_MINIMO_MAXIMO',
                        'NAO_ACEITA_VALOR_DIVERGENTE',
                        'SOMENTE_VALOR_MINIMO'
                    ]
                },
                quantityAllowed: {
                    type: String,
                    required: true,
                    min: '1',
                    max: '99',
                    default: '1'
                }
            },
            dueDateDays: {
              type: Number,
              required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            metadata: mongoose.SchemaTypes.Mixed,
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = WebAssignors;
