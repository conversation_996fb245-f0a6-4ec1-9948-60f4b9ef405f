const mongoose = require('mongoose');

let WebCharges = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebCharges',
        fields: {
            _webOrderId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            reference: {
                month: {
                    type: String,
                    required: true,
                    index: true
                },
                year: {
                    type: String,
                    required: true,
                    index: true
                }
            },
            amount: {
                type: Number,
                required: true
            },
            amountCommission: {
                type: Number,
                required: true
            },
            toPayment: {
                type: Number,
                required: true
            },
            dueDate: {
                type: Date,
                required: true
            },
            installment: {
                type: Number
            },
            type: {
                type:String,
                enum: [
                    'yearly',
                    'monthly',
                    'additional',
                    'api'
                ]
            },
            paymentDate: Date,
            status: {
                type: String,
                enum: [
                    'waiting_payment',
                    'paid',
                    'canceled',
                    'free'
                ],
                default: 'waiting_payment'
            }
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = WebCharges;
