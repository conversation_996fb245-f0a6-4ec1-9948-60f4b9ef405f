let WebChargeTypes = {
    functions: {},
    database: {
        collection: 'WebChargeTypes',
        fields: {
            name: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            alias: {
                type: String,
                required: true,
                unique: true,
                index: true
            },
            description: {
                type: String,
                required: true
            }
        },
        indexes: [
            {
                fields: {
                    'alias': 1
                },
                options: {
                    unique: 'already_web_charge_type',
                    name: 'dup_web_charge_type'
                }
            }
        ],
        options: {
            timestamp: true
        }
    }
};

module.exports = WebChargeTypes;
