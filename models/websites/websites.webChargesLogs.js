const mongoose = require('mongoose');

let WebChargesLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'WebChargesLogs',
        fields: {
            _chargeId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            before: {
                _id: false,
                _webOrderId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                reference: {
                    month: {
                        type: String,
                        required: true,
                        index: true
                    },
                    year: {
                        type: String,
                        required: true,
                        index: true
                    }
                },
                amount: {
                    type: Number,
                    required: true
                },
                amountCommission: {
                    type: Number,
                    required: true
                },
                toPayment: {
                    type: Number,
                    required: true
                },
                dueDate: {
                    type: Date,
                    required: true
                },
                paymentDate: Date,
                metadata: {
                    type: mongoose.SchemaTypes.Mixed
                },
                status: {
                    type: String,
                    enum: [
                        'waiting_payment',
                        'paid',
                        'canceled',
                        'free'
                    ],
                    default: 'waiting_payment'
                }
            },
            after: {
                _id: false,
                _webOrderId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                reference: {
                    month: {
                        type: String,
                        required: true,
                        index: true
                    },
                    year: {
                        type: String,
                        required: true,
                        index: true
                    }
                },
                amount: {
                    type: Number,
                    required: true
                },
                amountCommission: {
                    type: Number,
                    required: true
                },
                toPayment: {
                    type: Number,
                    required: true
                },
                dueDate: {
                    type: Date,
                    required: true
                },
                paymentDate: Date,
                metadata: {
                    type: mongoose.SchemaTypes.Mixed
                },
                status: {
                    type: String,
                    enum: [
                        'waiting_payment',
                        'paid',
                        'canceled',
                        'free'
                    ],
                    default: 'waiting_payment'
                }
            },
            _newChargeId:{
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userType    : {
                type    : String,
                required: true,
                enum    : [
                    'student',
                    'partner',
                    'teacher',
                    'employer',
                    'computer'
                ]
            },
            _userName: {
                type: String,
                required: true
            },
            description: String,
            metadata: mongoose.SchemaTypes.Mixed
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = WebChargesLogs;
