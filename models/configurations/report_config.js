let ReportConfigs = {
    functions: {},
    database: {
        connection: 'piaget',
        collection: 'ReportConfigs',
        fields: {
            title: {
                type: String,
                required: true,
                maxlength: 255
            },
            icon: {
                type: String,
                maxlength: 255,
                lowercase: true
            },
            description: {
                type: String,
            },
            enable: {
                type: Boolean,
                default: true
            },
            background_color: {
                type: String,
                default: '#20a8d8'
            },
            background_color_hover: {
                type: String,
                default: '#20d8c3'
            },
            options: [
                {
                label: {
                    type: String,
                    required: true
                },
                value: {
                    type: String,
                    required: true
                },
                enable: {
                    type: Boolean,
                    default: true
                },
                createAt: {
                    type: Date,
                    default: Date.now()
                },
                deleteAt: {
                    type: Date,
                }
              }
            ]
        }
    }
};

module.exports = ReportConfigs;
