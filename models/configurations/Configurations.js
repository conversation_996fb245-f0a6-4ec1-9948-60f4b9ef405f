const mongoose = require('mongoose');

 let Configurations = {
    database:{
        collection: 'Configurations',
        connection: 'database_piaget',
        fields    : {
            name: {
                type    : String,
                unique  : 'Este nome de configuração já existe',
                required: true
            },
            value: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            description: {
                type: String,
                required: true
            },
            certifierAlias: {
                type: String,
            },
            isActive   : {
                type   : Boolean,
                default: true
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
            }
        }
    }
}

module.exports =  Configurations;
