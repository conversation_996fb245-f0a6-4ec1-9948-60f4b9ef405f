/**
 * Todos os fields criados para configuração deverá obrigatoriamente
 * possuir valores default devido a criação de configuração ser aplicada ao parceiro
 * sempre que um novo parceiro é criado.
 */
 
 let Settings = {
     functions: {},
     database: {
        connection: 'database_piaget',
        collection: 'Settings',
        fields    : {
            _cpf       : {
                type    : String,
                unique  : true,
                required: true
            },
            integration: {
                paymentsPer: {
                    type    : String,
                    default : 'in',
                    enum    : [
                        'in',
                        'out'
                    ]
                }
            }
        }
    }
};

module.exports = Settings;
