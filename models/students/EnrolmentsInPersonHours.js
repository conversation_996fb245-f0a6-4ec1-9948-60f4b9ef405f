const mongoose = require("mongoose");

let EnrolmentsInPersonHours = {
    database: {
        collection: 'EnrolmentsInPersonHours',
        connection: 'database_piaget',
        fields: {
            typeActivity: {
                type: 'string',
                default: 'exam'
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            minutesActivity: {
                type: Number,
                required: true
            },
            _presentialEvaluationScheduleId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            _presentialEvaluationId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            _activityId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = EnrolmentsInPersonHours;