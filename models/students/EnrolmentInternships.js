

const mongoose = require('mongoose');
let EnrolmentInternships = {
  database: {
    collection: 'EnrolmentInternships',
    connection: 'database_piaget',
    fields: {
      cpf: {
        type: String,
        required: true,
      },
      internshipAreas: {
        type: [{
          _id: mongoose.SchemaTypes.ObjectId,
          name: String
        }],
        // required: false,
      },
      title: String,
      course: {
        certifier: {
          type: String,
          required: true,
        },
        type: {
          type: String,
          required: true,
        },
        areas: {
          type: [String],
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        workload: {
          type: Number,
          required: true,
        },
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      theme: {
        type: String,
        // required: false,
      },
      status: {
        type: String,
        enum: [
          "waiting",
          "waiting-conclusion",
          "waiting_approved",
          "in-development",
          "approved",
          "rejected",
          'waiting-correction'
        ],
        // required: false,
        default: "waiting_approved",
      },
      requestNewRevision: {
        type: Boolean,
        // required: false,
        default: true,
      },
      workload: {
        type: Number,
        // required: false,
        default: null,
      },
      solicitedWorkload: {
        type: Number,
        // required: false,
        default: null,
      },
      archive: {
        type: [String],
        // required: false,
      },
      teacher: {
        type: String,
        // required: false,
      },
      typeInternship: {
        type: String,
        enum: ["required", "optional", 'no-required'],
        // required: false,
        default: "required",
      },
      workloadHistory: [
        {
          _id: false,
          workload: {
            type: Number,
            // required: false,
            default: null,
          },
          archive: {
            type: [String],
          },
          launchedAt: {
            type: Date,
            // required: false,
          },
          _userName: {
            type: String,
            // required: false,
          },
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
          },
        },
      ],
      submitions: [
        {
          _id: false,
          date: {
            type: Date,
            required: true,
          },
          archive: {
            type: [String],
            required: true,
          },
          observation: {
            type: String,
            // required: false
          },
        },
      ],
      observation: {
        type: String,
        // required: false
      },
      revisions: [
        {
          _id: false,
          number: {
            type: Number,
            required: true,
          },
          archive: {
            type: [String],
            required: true,
          },
          date: {
            type: Date,
            required: true,
          },
          action: {
            type: String
          },
          step: {
            type: {
              isRequired: Boolean,
              typeInternship: String,
              title: String,
              action: String,
              description: String,
              nextStatus: String,
              reviewBy: String,
              visibleIn: String,
              originalFile: [String],
              file: [String],
              requestedBy: {
                _userName: String,
                _userId: mongoose.SchemaTypes.ObjectId,
              },
              observation: String,
              internalAnalisy: Boolean,
              approvedInternalAnalisy: Boolean,
              typeLimitCorrect: String,
              dateLimit: Date,
              order: Number,
              limitCorrect: Number,
              multiFile: Boolean
            },
            // required: false
          },
          comments: [
            {
              _id: false,
              content: {
                type: String,
                // required: false,
              },
              step: {
                type: {
                  isRequired: Boolean,
                  typeInternship: String,
                  title: String,
                  action: String,
                  description: String,
                  nextStatus: String,
                  reviewBy: String,
                  visibleIn: String,
                  originalFile: [String],
                  file: [String],
                  requestedBy: {
                    _userName: String,
                    _userId: mongoose.SchemaTypes.ObjectId,
                  },
                  observation: String,
                  internalAnalisy: Boolean,
                  approvedInternalAnalisy: Boolean,
                  typeLimitCorrect: String,
                  dateLimit: Date,
                  limitCorrect: Number,
                  multiFile: Boolean
                },
                // required: false
              },
              date: {
                type: Date,
                required: true,
              },
              author: {
                type: {
                  type: String,
                  enum: ["teacher", "employer", "student"],
                  required: true,
                },
                name: {
                  type: String,
                  // required: false,
                },
                _authorId: {
                  type: mongoose.SchemaTypes.ObjectId,
                  // required: false,
                },
              },
            },
          ],
        },
      ],
      internshipCompany: {
        _id: mongoose.SchemaTypes.ObjectId,
        name: String,
        cnpj: String,
      },
      letterFile: {
        type: String,
        default: false
      },
      curriculumMatrixInternshipId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      tutor: {
        _id: mongoose.SchemaTypes.ObjectId,
        name: String
      },
      historySteps: {
        type: [{
          _id: mongoose.SchemaTypes.ObjectId,
          isRequired: Boolean,
          typeInternship: String,
          title: String,
          action: String,
          description: String,
          nextStatus: String,
          reviewBy: String,
          visibleIn: String,
          originalFile: [String],
          internalAnalisy: Boolean,
          approvedInternalAnalisy: Boolean,
          multiFile: Boolean,
          typeLimitCorrect: String,
          dateLimit: Date,
          limitCorrect: Number,
          concluded: Boolean,
          requestedBy: {
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId,
          },
          approvedBY: {
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId,
          },
          aprovedDate: Date,
          file: [String],
          observation: String,
          company: {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            fantasyName: String,
            cnpj: String
          },
          skipStep: Boolean,
          date: Date
        }],
        // required: false
      },
      approvedSteps: {
        type: [{
          _id: mongoose.SchemaTypes.ObjectId,
          isRequired: Boolean,
          typeInternship: String,
          title: String,
          action: String,
          description: String,
          nextStatus: String,
          reviewBy: String,
          visibleIn: String,
          originalFile: [String],
          internalAnalisy: Boolean,
          approvedInternalAnalisy: Boolean,
          multiFile: Boolean,
          typeLimitCorrect: String,
          dateLimit: Date,
          limitCorrect: Number,
          concluded: Boolean,
          requestedBy: {
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId,
          },
          approvedBY: {
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId,
          },
          aprovedDate: Date,
          file: [String],
          observation: String,
          company: {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            fantasyName: String,
            cnpj: String
          },
          skipStep: Boolean,
          date: Date
        }],
      },
      actualStep: {
        type: {
          _id: mongoose.SchemaTypes.ObjectId,
          isRequired: Boolean,
          typeInternship: String,
          title: String,
          action: String,
          description: String,
          nextStatus: String,
          reviewBy: String,
          visibleIn: String,
          originalFile: [String],
          internalAnalisy: Boolean,
          approvedInternalAnalisy: Boolean,
          multiFile: Boolean,
          typeLimitCorrect: String,
          dateLimit: Date,
          limitCorrect: Number,
          file: [String],
          requestedBy: {
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId,
          },
          observation: String,
          company: {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            fantasyName: String,
            cnpj: String
          },
          date: Date
        },
        // required: false
      },
      _enrolmentInternshipId: mongoose.SchemaTypes.ObjectId,
      isVisible: {
        type: Boolean,
        default: true
      },
      metadata: mongoose.SchemaTypes.Mixed,
      nextStep: String,
    }
  }
};

module.exports = EnrolmentInternships;
