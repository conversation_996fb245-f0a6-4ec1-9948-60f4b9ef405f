const mongoose = require('mongoose');
const FileSchema = {
  name: {
    type: String,
    enum: ["academic_history", "syllabus", "other"],
    required: true
  },
  url: {
    type: String,
    required: true,
  },
  uploadDate: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: mongoose.SchemaTypes.Mixed
  },
}

const beforeAfterSchema = {
  createdAt: {
    type: Date,
    default: Date.now
  },
  origin: {
    type: String,
    default: 'Portal do Aluno'
  },
  archives: {
    type: [FileSchema],
    required: true,
  },
  reason: {
    type: String,
  },
  dispensationTypes: {
    type: String,
  },
  observation: {
    type: String,
  },
  _userId: {
    type: mongoose.SchemaTypes.ObjectId,
    required: true,
  },
  _userType: {
    type: String,
    enum: ["student", "partner", "teacher", "employer", "computer"],
    required: true,
  },
  _userName: {
    type: String,
    required: true,
  }
};

let DispensationSolicitations = {
  database: {
    collection: "DispensationSolicitations",
    fields: {
      origin: {
        type: String,
        default: 'Portal do Aluno'
      },
      archives: {
        type: [FileSchema],
        required: true,
      },
      status: {
        type: String,
        enum: ["waiting", "review", "approved", "refused"],
        default: "waiting",
      },
      reason: {
        type: String,
      },
      dispensationTypes: {
        type: String,
      },
      observation: {
        type: String,
      },
      disciplines: {
        type: [
          {
            name: {
              type: String,
              required: true,
            },
            institution: {
              type: String,
              required: true,
            },
            course: {
              type: String,
              required: true,
            },
            grade: {
              type: String,
              required: true,
            },
            teacher: {
              type: String,
              required: true,
            },
            endDate: {
              type: Date,
              required: true,
            },
            discId: mongoose.SchemaTypes.ObjectId,
            workload: Number
          },
        ],
        // required: false,
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userType: {
        type: String,
        enum: ["student", "partner", "teacher", "employer", "computer"],
        required: true,
      },
      _userName: {
        type: String,
        required: true,
      },
      _userResponsibleId: {
        type: mongoose.SchemaTypes.ObjectId,
      },
      referenceMatrix: {
        _id: mongoose.SchemaTypes.ObjectId,
        course: String
      },
      reanalysis: {
        type: [
          {
            _id: false,
            date: {
              type: Date,
              default: Date.now
            },
            before: beforeAfterSchema,
            after: beforeAfterSchema
          }
        ],
        required: false
      },
      curriculumAnalysisStorageURL: {
        type: String,
      },
      discountByDispensationSolicitation: {
        type: {
          percentByDispensation: Number,
          valueOfDiscount: Number,
          _dispensationId: mongoose.SchemaTypes.ObjectId,
          _chargeId: mongoose.SchemaTypes.ObjectId,
        }
      },
    },
    options: {
      timestamps: true,
    },
  },
};
module.exports = DispensationSolicitations;