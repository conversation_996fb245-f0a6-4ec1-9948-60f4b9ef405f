const mongoose = require('mongoose');

let ScheduledTccs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ScheduledTccs',
        fields    : {
            status: {
              type: String,
              // required: false,
              enum: ['waiting_approval', 'approved', 'canceled'],
              default: 'waiting_approval'
            },
            cancelReason: String,
            checkIn: {
                type: Boolean,
                // required: false,
                default: null
            },
            isActive: {
                type: Boolean,
                // required: false,
                default: true
            },
            date: {
                type: Date,
                required: true
            },
            _enrolmentTccId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            link: {
                type: String,
                required: true
            },
            history: [
              {
                date: Date,
                status: {
                  type: String,
                  // required: false,
                  enum: ['waiting_approval', 'approved', 'canceled'],
                  default: 'waiting_approval'
                },
                _userName: String,
                _userId: mongoose.SchemaTypes.ObjectId,
                _userType: String
              }
            ]
        }
    }
};

module.exports = ScheduledTccs;
