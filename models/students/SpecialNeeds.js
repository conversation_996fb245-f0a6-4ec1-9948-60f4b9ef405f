let SpecialNeeds = {
    functions: {},
    database: {
        collection: 'SpecialNeeds',
        connection: 'database_piaget',
        fields: {
            name    : {
                type     : String,
                required : true,
                maxlength: 150,
                unique   : 'special_need_already_exists'
            },
            status  : {
                type     : Boolean,
                required : true
            },
            description : {
                type     : String,
                required : true
            },
            icd: { // International Classification of Diseases
                type     : String,
                required : false
            }
        }
    }
};

module.exports = SpecialNeeds;
