let OpenActivities = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'OpenActivities',
        fields    : {
            _enrolmentId : {
                type     : String,
                required : true,
                lowercase: true
            },
            _disciplineId: {
                type     : String,
                required : true,
                lowercase: true
            },
            _activityId  : {
                type     : String,
                required : true,
                lowercase: true
            },
            status       : {
                type    : String,
                enum    : [
                    'open',
                    'finished'
                ],
                // required: false,
                default : 'open'
            },
            type         : {
                type    : String,
                enum    : [
                    'regular',
                    'recuperation'
                ],
                required: true
            },
            modality     : {
                type    : String,
                enum    : [
                    'online',
                    'presential'
                ],
                required: true
            },
            model        : {
                type    : String,
                enum    : [
                    'evaluation',
                    'upload',
                    'participation'
                ],
                required: true
            },
            isFinalTest  : {
                type    : Boolean,
                required: true
            },
            date         : {
                type    : Date,
                // required: false,
                default : null
            },
            upload       : {
                type    : {
                    requestNewRevision: {
                        type    : Boolean,
                        // required: false,
                        default : true
                    }
                },
                required: function () {
                    return this.model === 'upload';
                }
            },
            student      : {
                name                   : {
                    type    : String,
                    required: true
                },
                cpf                    : {
                    type    : String,
                    required: true
                },
                email                  : {
                    type    : String,
                    required: true
                },
                studentRegistrationCode: {
                    type    : String,
                    required: true
                }
            },
            course       : {
                name      : {
                    type    : String,
                    required: true
                },
                workload  : {
                    type    : Number,
                    required: true
                },
                certifier : {
                    type    : String,
                    required: true
                },
                type      : {
                    type    : String,
                    required: true
                },
                discipline: {
                    type    : String,
                    required: true
                }
            }
        }
    }
};

module.exports = OpenActivities;
