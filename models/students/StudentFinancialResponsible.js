const mongoose = require('mongoose');

let StudentFinancialResponsible = {
  database: {
    collection: 'StudentFinancialResponsible',
    connection: 'database_piaget',
    fields: {
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      _studentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      type: {
        type: String,
        required: true,
        enum: ['pf', 'pj'],
      },
      cpfCnpj: {
        type: String,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      companyName: {
        type: String,
        required: function () {
          return this.type === 'pj';
        },
      },
      email: {
        type: String,
        required: true,
      },
      phone: {
        type: String,
        required: true,
      },
      address: {
        street: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado'
        },
        number: {
          type: String,
          required: false,
          allowNull: true,
          default: 'SN'
        },
        complement: {
          type: String,
          required: false,
          allowNull: true,
          default: 'Não informado'
        },
        zone: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado'
        },
        zip: {
          type: String,
          required: false,
          allowNull: false,
          default: '00000000'
        },
        city: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado'
        },
        ibgeCityCode: {
          type: Number,
          required: false,
          allowNull: false,
        },
        uf: {
          type: String,
          required: false,
          allowNull: false,
          default: 'NA'
        }
      }
    },
    options: {
      timestamps: true
    }
  }
};

module.exports = StudentFinancialResponsible;
