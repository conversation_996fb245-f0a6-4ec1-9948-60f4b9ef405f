const mongoose = require('mongoose');

let EnrolmentClassEntry = {
    functions: {},
    database: {
        collection: 'EnrolmentClassEntry',
        connection: 'database_piaget',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _studentCpf: {
                type: String,
                required: true
            },
            _studentName: {
                type: String,
                required: true
            },
            _courseId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _certifierName: {
                type: String,
                required: true
            },
            _courseName: {
                type: String,
                required: true
            },
            _typeName: {
                type: String,
                required: true
            },
            _subCategory: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            workload: {
                type: Number,
                // required: false,
                allowNull: true,
                default: null
            },
            acronym: {
                type: String,
                // required: false,
                allowNull: true,
                default: null
            },
            class: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            polo: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            selectiveProcess: {
                type: {
                    _selectiveProcessId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false
                    },
                    _selectiveProcessStudentsId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false
                    },
                    _selectiveProcessCourseId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false
                    },
                },
                // required: false,
                allowNull: true
            },
            approved: {
                type: Boolean,
                required: true,
                default: true
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    _enrolmentId: 1
                },
                options: {
                    unique: 'already_enrolled',
                    name: 'dup_enrol'
                }
            }
        ]
    }
};

module.exports = EnrolmentClassEntry;
