const mongoose = require('mongoose');

let Solicitations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Solicitations',
        fields    : {
            _enrolmentId : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _solicitationTypeName: {
                type    : String,
                required: true
            },
            _cpf: {
                type    : String,
                required: true,
                index   : true
            },
            _certifierName : {
                type    : String,
                required: true
            },
            _courseTypeName: {
                type    : String,
                required: true
            },
            isDeclaration: {
                type: Boolean,
                default: false
            },
            description: {
                type: String
            },
            archive: {
                type: String
            },
            status : {
                type    : String,
                enum    : [
                    'waiting',
                    'done',
                    'free',
                    'waiting_signature',
                    'sent_student',
                    'refund',
                    'in_progress',
                    'canceled',
                    'waiting_approval'
                ],
                required: true,
                default: 'waiting'
            },
            metadata             : {
                _coursewares      : {
                    type    : [mongoose.SchemaTypes.ObjectId],
                },
                _chargeId         : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    // required: false
                },
                _disciplineId: {
                    type    : mongoose.SchemaTypes.ObjectId
                },
                _disciplineName: {
                    type    : String
                },
                shippingAmount: {
                    type    : Number,
                    // required: false
                },
                _tccId: {
                    type    : mongoose.SchemaTypes.ObjectId
                },
                shippingType: {
                    type: String,
                    enum    : [
                        'common',
                        'sedex',
                        'in_hands',
                        'common_registry'
                    ]
                },
                address              : {
                    street    : {
                        type     : String,
                        required : false
                    },
                    number    : {
                        type     : String,
                        required : false
                    },
                    complement: {
                        type     : String,
                        required : false
                    },
                    zone      : {
                        type     : String,
                        required : false
                    },
                    zip       : {
                        type     : String,
                        required : false
                    },
                    city      : {
                        type     : String,
                        required : false
                    },
                    uf        : {
                        type     : String,
                        required : false
                    }
                }
            },
            history    : [
                {
                    status : {
                        type    : String,
                        enum    : [
                            'waiting',
                            'done',
                            'free',
                            'waiting_signature',
                            'sent_student',
                            'refund',
                            'in_progress',
                            'canceled',
                            'waiting_approval'
                        ],
                        required: true
                    },
                    _userId   : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    _userName : {
                        type    : String,
                        required: true
                    },
                    metadata: mongoose.SchemaTypes.Mixed,
                    launchedAt: {
                        type    : Date,
                        required: true
                    }
                }
            ],
            visibleForStudent: {
              type: Boolean,
              default: true
            },
        }
    }
};

module.exports = Solicitations;
