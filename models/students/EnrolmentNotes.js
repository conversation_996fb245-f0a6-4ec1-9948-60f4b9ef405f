const mongoose = require("mongoose");

const archiveSchema = {
    _id: false,
    name: {
        type: String,
        required: true
    },
    url: {
        type: String,
        required: true
    }
};

let EnrolmentNotes = {
    database: {
        collection: 'EnrolmentNotes',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            description: {
                type: String,
                required: true
            },
            archive: String,
            archives: {
                type: [archiveSchema]
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userName: {
                type: String,
                required: true
            },
            applyOnCombo: {
                type: Boolean,
                // required: false,
                default: false,
                allowNull: true
            }
        }
    }
};

module.exports = EnrolmentNotes;