const mongoose = require('mongoose');

let ConclusionSolicitations = {
  database: {
    collection: 'ConclusionSolicitations',
    connection: 'database_piaget',
    fields: {
      enrolment: {
        type: {
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          createdAt: {
            type: Date,
            required: true,
          },
        },
        required: true,
      },
      student: {
        type: {
          name: {
            type: String,
            required: true,
          },
          cpf: {
            type: String,
            required: true,
          },
          cellPhone: {
            type: String,
            required: true,
          },
          whatsApp: {
            type: String,
            // required: false,
          },
        },
        required: true,
      },
      course: {
        type: {
          name: {
            type: String,
            required: true,
          },
          typeName: {
            type: String,
            required: true,
          },
          certifier: {
            type: String,
            required: true,
          },
        },
        required: true,
      },
      class: {
        type: {
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          name: {
            type: String,
            required: true,
          }
        },
        // required: false,
      },
      status: {
        type: String,
        enum: [
          'pending',
          'approved',
          'canceled',
        ],
        default: 'pending',
      },
    },
    options: {
      timestamps: true,
    },
  },
};

module.exports = ConclusionSolicitations;
