const mongoose = require('mongoose');

const courseSchema = {
    disciplines: [
        {
            isOnRecuperation: {
                type: Boolean,
                default: false
            },
            grade: {
                type: Number,
                default: null
            },
            gradeType: {
                type: String,
                default: 'standard'
            },
            gradeHistory: [
                {
                    _userName: {
                        type: String,
                        required: true
                    },
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    grade: {
                        type: Number,
                        // required: true
                    },
                    gradeType: {
                        type: String,
                        enum: ['dispensation', 'exploitation', 'standard', 'manual'],
                        default: 'standard'
                    },
                    reason: {
                        type: String,
                        // required: false,
                        default: null
                    },
                    launchedAt: {
                        type: Date,
                        required: true
                    }
                }
            ],
            _name: {
                type: String,
                required: true
            },
            sagahDiscipline: {
                type: String
            },
            type: {
                type: String,
                enum: ['required', 'optional']
            },
            description: {
                type: String,
                required: false
            },
            workload: {
                type: Number,
                // required: false,
                allowNull: true,
                default: null
            },
            activities: [
                {
                    type        : {
                        type    : String,
                        required: true
                    },
                    modality    : {
                        type    : String,
                        required: true
                    },
                    maxDuration : {
                        type    : Number,
                        // required: false
                    },
                    model       : {
                        type    : String,
                        required: true
                    },
                    isFinalTest : {
                        type    : Boolean,
                        required: true
                    },
                    modelMeta   : {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId
                        },
                        numQuestions: {
                            type    : Number,
                            // required: false
                        },
                        enunciation : {
                            type    : String,
                            // required: false
                        },
                        ltiUrl: {
                            type: String,
                            // required: false
                        },
                        ltiTitle: {
                            type: String,
                            // required: false
                        }
                    },
                    attempts    : {
                        type    : Number,
                        // required: false,
                        default : 0
                    },
                    date        : {
                        type    : Date,
                        // required: false,
                        default : null
                    },
                    timeSpent   : {
                        type    : Number,
                        // required: false,
                        default : 0
                    },
                    evaluation  : {
                        _coursewareEvaluationId: {
                            type    : mongoose.SchemaTypes.ObjectId,
                            // required: false
                        },
                        questions              : [
                            {
                                _id        : false,
                                _questionId: {
                                    type    : mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                },
                                _response  : {
                                    type    : mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                },
                                _correct   : {
                                    type    : mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                }
                            }
                        ]
                    },
                    upload      : {
                        archive           : {
                            type    : String,
                            // required: false,
                            default : null
                        },
                        requestNewRevision: {
                            type    : Boolean,
                            // required: false,
                            default : true
                        },
                        revisions         : [
                            {
                                _id      : false,
                                _userName: String,
                                _userId  : mongoose.SchemaTypes.ObjectId,
                                archive  : {
                                    type    : String,
                                    required: true
                                },
                                date     : {
                                    type    : Date,
                                    required: true
                                }
                            }
                        ]
                    },
                    grade       : {
                        type   : Number,
                        default: null
                    },
                    gradeType   : {
                        type    : String,
                        enum    : [
                            'standard',
                            'manual'
                        ],
                        required: true,
                        default : 'standard'
                    },
                    gradeHistory: [
                        {
                            _userName : {
                                type    : String,
                                required: true
                            },
                            _userId   : {
                                type    : mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            grade     : {
                                type    : Number,
                                // required: true
                            },
                            reason    : {
                                type    : String,
                                // required: false,
                                default : null
                            },
                            gradeType : {
                                type   : String,
                                enum   : [
                                    'standard',
                                    'manual'
                                ],
                                required: true,
                                default: 'standard'
                            },
                            launchedAt: {
                                type    : Date,
                                required: true
                            }
                        }
                    ]
                }
            ],
            _coursewares: [mongoose.SchemaTypes.ObjectId]
        }
    ],
    _name: {
        type: String,
        required: true
    },
    _typeName: {
        type: String,
        required: true
    },
    _certifierName: {
        type: String,
        required: true
    },
    _knowledgeAreaName: {
        type: String,
        required: true
    },
    _categoryName: {
        type: String,
        // required: false,
        default: null
    },
    _areaNames: {
        type: [String],
        required: true
    },
    acronym: {
        type: String,
        required: true
    },
    workload: {
        type: Number,
        // required: false,
        allowNull: true
    },
    linkEMec: {
        type: String,
        required: true
    },
    approvalPercentage: {
        type: Number,
        required: true
    },
    minimalFrequency: {
        type: Number,
        required: true
    },
    description: {
        type: String,
        required: false
    },
    isTccRequired: {
        type: Boolean,
        required: true
    },
    isInternshipRequired: {
        type: Boolean,
        required: true
    },
    minMonthsToComplete: {
        type: Number,
        required: true
    }
};

let EnrolmentSwaps = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'EnrolmentSwaps',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userName: {
                type: String,
                required: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            fromCourse: courseSchema,
            toCourse: courseSchema
        }
    }
};

module.exports = EnrolmentSwaps;
