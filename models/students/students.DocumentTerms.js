let DocumentTypes = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'DocumentTerms',
    fields: {
      cpf: {
        type: String,
        index: true,
        required: true
      },
      documents: {
        type: [String],
        index: true,
        required: true
      },
      isAuthenticatedByCertifiers: {
        type: Boolean,
        required: true,
      },
      term: {
        type: String,
        required: true
      },
      termUrl: {
        type: String,
        required: true
      },
    },
  }
};

module.exports = DocumentTypes;
