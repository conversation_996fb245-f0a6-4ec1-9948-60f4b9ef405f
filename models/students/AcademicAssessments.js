const mongoose = require('mongoose');
let AcademicAssessments = {
    database: {
        collection: "AcademicAssessments",
        connection: 'database_piaget',
        fields: {
            assessments: {
                type: String,
                required: true
            },
            condition: {
                type: String
            },
            year: {
                type: Number,
                required: true
            },
            aplicationDate: {
                type: Date,
                // required: false
            },
            observation: {
                type: String,
                // required: false
            },
            situation: {
                type: String,
                // required: false
            },
            grade: {
                type: Number,
                // required: false
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            name: {
                type: String,
                // required: false
            },
            cpf: {
                type: String,
                // required: false
            },
            dateStart: {
                type: Date,
                // required: false
            },
            course: {
                type: String,
                // required: false
            },
            typeName: {
                type: String,
                // required: false
            },
            email: {
                type: String,
                // required: false
            },
            cellPhone: {
                type: String,
                // required: false
            },
            whatsApp: {
                type: String,
                // required: false
            },
            phone: {
                type: String,
                // required: false
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            certifier: {
                type: String,
                // required: false
            },
            acronym: {
                type: String,
                // required: false
            },
            combo: {
                type: [mongoose.SchemaTypes.ObjectId],
                // required: false
            },
            studentId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            histories: {
                type: [
                    {
                        _userId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        _userName: {
                            type: String,
                            required: true
                        },
                        observation: {
                            type: String
                        },
                        date: {
                            type: Date,
                            required: true
                        },
                        situation: {
                            type: String
                        },
                        grade: {
                            type: Number
                        },
                        year: {
                            type: Number
                        },
                        assessments: {
                            type: String
                        }
                    }
                ],
                required: true
            }
        },
        options: {
            timestamps: true,
        },
    },
};

module.exports = AcademicAssessments;
