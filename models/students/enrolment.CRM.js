const mongoose = require('mongoose');

let EnrolmentCRM = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'EnrolmentCRM',
        fields: {
            crm: {
                enrolment: {
                    type: String,
                    required: true
                },
                concourse: {
                    type: String,
                    required: true
                },
                certifier: {
                    type: String,
                    required: true
                },
                email: {
                    type: String,
                    required: true
                }
            },
            dataCrm: {
                login: String,
                password: String,
                links: [
                    {
                        url: String
                    }
                ]
            },
            enrolments: mongoose.SchemaTypes.Mixed,
            metadata: mongoose.SchemaTypes.Mixed,
            student: {
                _schoolEducationAlias: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                name: {
                    type: String,
                    required: true
                },
                cpf: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                identity: {
                    type: String,
                    // required: false
                },
                dispatcher: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                genre: {
                    type: String,
                    enum: [
                        'M',
                        'F'
                    ],
                    // required: false
                },
                phone: {
                    type: String,
                    // required: false,
                    allowNull: true
                },
                cellPhone: {
                    type: String,
                    required: true,
                    allowNull: false
                },
                whatsApp: {
                    type: String,
                    // required: false,
                    allowNull: true
                },
                address: {
                    street: {
                        type: String,
                        required: true,
                        allowNull: false
                    },
                    number: {
                        type: String,
                        required: true,
                        allowNull: false
                    },
                    complement: {
                        type: String,
                        // required: false,
                        allowNull: true
                    },
                    zone: {
                        type: String,
                        required: true,
                        allowNull: false
                    },
                    zip: {
                        type: String,
                        required: true,
                        allowNull: false
                    },
                    city: {
                        type: String,
                        required: true,
                        allowNull: false
                    },
                    uf: {
                        type: String,
                        required: true,
                        allowNull: false
                    }
                },
                birthDate: {
                    type: Date,
                    // required: false,
                    allowNull: false
                },
                educationInstitution: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                graduationCourse: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                dateConclusion: {
                    type: Date,
                    required: true,
                    allowNull: false
                },
                naturalessCity: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                naturalessState: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                nationality: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                motherName: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                fatherName: {
                    type: String,
                    // required: false,
                    allowNull: false
                }
            },
            status: {
                type: String,
                enum: [
                    'waiting',
                    'error',
                    'pending',
                    'sent'
                ],
                required: true
            },
            history: [
                {
                    status: {
                        type: String,
                        enum: [
                            'waiting',
                            'error',
                            'pending',
                            'sent'
                        ],
                        required: true
                    },
                    _userId: mongoose.Types.ObjectId,
                    _userName: String,
                    description: String,
                    launchedAt: {
                        type: Date,
                        required: true
                    }
                }
            ]
        },
        options: {
            timestamp: true
        }
    }
};

module.exports = EnrolmentCRM;
