const mongoose = require("mongoose");
const {SchemaTypes} = require("mongoose");

let EnrolmentDiscounts = {
    functions: {
        parseEnrolmentId: function (next) {
            try {
                if (Array.isArray(this._enrolmentId)) {
                    this._enrolmentId = this._enrolmentId.map(id => new mongoose.Types.ObjectId(id));
                } else {
                    this._enrolmentId = new mongoose.Types.ObjectId(this._enrolmentId);
                }
                next();
            } catch (e) {
                console.error(e);
                next();
            }
        }
    },
    database: {
        collection: 'EnrolmentDiscounts',
        fields: {
            studentName: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            cpf: {
              type: String,
              index: true,
              required: false
            },
            _studentId: {
              type: SchemaTypes.ObjectId,
              index: true,
              required: function () {
                return !this.cpf;
              }
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
                index: true
            },
            certifierName: {
                type: String,
                required: true
            },
            courseTypeName: {
                type: String,
                required: true
            },
            courseName: {
                type: String,
                required: true
            },
            _discountId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            discountName: {
                type: String,
                required: true
            },
            chargeType: {
                type: [String],
                required: true
            },
            discountType: {
                type: String,
                enum: [
                    'fixedValue',
                    'percentage'
                ],
            },
            value: {
                type: Number,
                required: true
            },
            dueDateDays: {
                type: Number,
                // required: false
            },
            applyAll     : {
                type    : Boolean,
                default : false
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            useInOverdueCharge: {
                type: Boolean,
                requried: false
            },
            discountRule: {
                type: String,
                enum: [
                    'not_applied',
                    'antecipation',
                    'fixed_date'
                ]
            },
            isConfiguration: {
                type: Boolean,
                default: false,
            },
            observation: {
                type: String
            },
            isCombo: {
                type: Boolean,
                default: false
            },
            combosCourses: [
                {
                    _id: mongoose.SchemaTypes.ObjectId,
                    certifierName: String,
                    courseTypeName: String,
                    courseName: String
                }
            ],
            _chargeId: {
                type: mongoose.SchemaTypes.ObjectId,
            }
        }
    }
};

EnrolmentDiscounts.database.pre = {
    save: [
        EnrolmentDiscounts.functions.parseEnrolmentId,
    ]
};

module.exports = EnrolmentDiscounts;
