 
 
const mongoose = require('mongoose');
let PresenceOfEnrolmentsBackup = {
    database: {
        collection: 'PresenceOfEnrolmentsBackup',
        fields: {
            _classId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _className: {
                type: String,
                // required: false,
                allowNull: true
            },
            _disciplineId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _disciplineName: {
                type: String,
                // required: false,
                allowNull: true
            },
            _groupingId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _groupingName: {
                type: String,
                // required: false,
                allowNull: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            enrolments: {
                type: [
                    {
                        _enrolmentId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        studentName: {
                            type: String,
                            required: true
                        },
                        presence: {
                            type: [
                                {
                                    lessonNumber: {
                                        type: Number,
                                        required: true
                                    },
                                    date: {
                                        type: Date,
                                        required: true
                                    },
                                    present: {
                                        type: Boolean,
                                        default: true
                                    }
                                }
                            ],
                            // required: false,
                            allowNull: true
                        }
                    }
                ],
                // required: false,
                allowNull: true
            },
            lessons: {
                type: [
                    {
                        lessonNumber: {
                            type: Number,
                            required: true
                        },
                        date: {
                            type: Date,
                            required: true
                        },
                        content: {
                            type: String,
                            required: true
                        },
                        description: {
                            type: String,
                            // required: false,
                            allowNull: true
                        },
                        file: {
                            type: mongoose.SchemaTypes.Mixed,
                            // required: false,
                            allowNull: true
                        },
                        teacher: {
                            type: {
                                _id: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false,
                                    allowNull: true,
                                    index: true
                                },
                                _name: {
                                    type: String,
                                    // required: false
                                },
                                _userId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false,
                                    allowNull: true,
                                    index: true
                                },
                                _userName: {  // Nome do curso vinculado
                                    type: String,
                                    // required: false
                                }
                            },
                            // required: false,
                            allowNull: true
                        },
                        enrolments: {
                            type: [
                                {
                                    _enrolmentId: {
                                        type: mongoose.SchemaTypes.ObjectId,
                                        required: true
                                    },
                                    studentName: {
                                        type: String,
                                        required: true
                                    },
                                    present: {
                                        type: Boolean,
                                        default: true
                                    }
                                }
                            ],
                            // required: false,
                            allowNull: true
                        }
                    }
                ],
                // required: false,
                allowNull: true
            }
        },
        options: {
            timestamps: true
        },
        indexes: []
    }
};
module.exports = PresenceOfEnrolmentsBackup;
