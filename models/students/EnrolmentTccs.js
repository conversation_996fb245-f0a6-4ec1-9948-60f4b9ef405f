 
 
const mongoose = require('mongoose');
let EnrolmentTccs = {
    database: {
        collection: 'EnrolmentTccs',
        connection: 'database_piaget',
        fields: {

            isUrgency: {
                type: Boolean,
                // required: false,
                default: false
            },
            cpf: {
                type: String,
                required: true
            },
            course: {
                certifier: {
                    type: String,
                    required: true
                },
                type: {
                    type: String,
                    required: true
                },
                areas: {
                    type: [String],
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                workload: {
                    type: Number,
                    required: true
                }
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                unique: 'Já existe um TCC enviado para esse curso!'
            },
            theme: {
                type: String,
                required: true
            },
            ata: {
                type: String,
                // required: false,
                default: null
            },
            status: {
                type: String,
                enum: [
                    'in_development', 'approved', 'waiting_presentation'
                ],
                // required: false,
                default: 'in_development'
            },
            requestNewRevision: {
                type: Boolean,
                // required: false,
                default: true
            },
            grade: {
                type: Number,
                // required: false,
                default: null
            },
            archive: {
                type: String,
                required: true
            },
            teacher: {
                type: String,
                // required: false
            },
            typeTcc: {
                type: String,
                enum: [
                    'article', 'monography'
                ],
                // required: false,
                default: 'article'
            },
            gradeHistory: [
                {
                    _id: false,
                    grade: {
                        type: Number,
                        required: true,
                        default: null
                    },
                    launchedAt: {
                        type: Date,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    }
                }
            ],
            submitions: [
                {
                    _id: false,
                    date: {
                        type: Date,
                        required: true
                    },
                    archive: {
                        type: String,
                        required: true
                    }
                }
            ],
            revisions: [
                {
                    _id: false,
                    number: {
                        type: Number,
                        required: true
                    },
                    archive: {
                        type: String,
                        required: true
                    },
                    date: {
                        type: Date,
                        required: true
                    },
                    comments: [
                        {
                            _id: false,
                            content: {
                                type: String,
                                required: true
                            },
                            date: {
                                type: Date,
                                required: true
                            },
                            author: {
                                type: {
                                    type: String,
                                    enum: [
                                        'teacher', 'employer', 'student'
                                    ],
                                    required: true
                                },
                                name: {
                                    type: String,
                                    required: true
                                },
                                _authorId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    required: true
                                }
                            }
                        }
                    ]
                }
            ],
            withPresentation: {
                type: Boolean,
                default: false,
            },
            approvedAt: {
              type: Date
            }
        }
    }
};

module.exports = EnrolmentTccs;
