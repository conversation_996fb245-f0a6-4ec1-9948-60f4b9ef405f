let ExternalEnrolments = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ExternalEnrolments',
        fields: {
            student: {
                name: {
                    type: String,
                    required: true
                },
                email: {
                    type: String,
                    required: true
                },
                cpf                    : {
                  type     : String,
                  required: function () {
                    return !this.student.foreigner;
                  },
                  unique   : 'Cpf já cadastrado',
                  sparse   : true,
                  default  : function () {
                    return this.document;
                  },
                  allowNull: false
                },
                rg                    : {
                  type     : String,
                  required: false,
                },
                documentCountryOrigin  : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  },
                  default : 'Brasil'
                },
                documentType           : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  },
                  default : 'CPF'
                },
                document               : {
                  type    : String,
                  required: function () {
                    return this.student.foreigner;
                  }
                },
                foreigner              : {
                  type   : Boolean,
                  default: false
                },
                cellPhone: String,
                dateConclusion: Date,
                address: {
                    type: {
                        street: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        number: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        complement: {
                            type: String,
                            // required: false,
                            allowNull: true
                        },
                        zone: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        zip: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        city: {
                            type: String,
                            required: true,
                            allowNull: false
                        },
                        uf: {
                            type: String,
                            required: true,
                            allowNull: false
                        }
                    },
                    // required: false
                }
            },
            course: {
                system: {
                    type: String,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                certifier: {
                    type: String,
                    required: true
                },
                type: {
                    type: String,
                    required: true
                },
                payment: {
                    enrolment: {
                        method: {
                            type: String,
                            enum: [
                                'creditCard',
                                'debitCard',
                                'boleto'
                            ],
                            required: true
                        },
                        dueDate: {
                            type: Date,
                            required: true
                        },
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        },
                        paid: {
                            type: Boolean,
                            // required: false,
                            default: false
                        }
                    },
                    course: {
                        paymentDay: {
                            type: Number,
                            required: true
                        },
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                }
            },
            status: {
                type: String,
                enum: [
                    'waiting_payment',
                    'paid',
                    'sent'
                ],
                // required: false,
                default: 'waiting_payment'
            },
            indication: {
                type: [
                    {
                        level: {
                            type: String,
                            enum: [
                                'master',
                                'level1',
                                'level2',
                                'indicator'
                            ]
                        },
                        cpf: String,
                        userType: String,
                        commissionMonthly: Number,
                        commissionEnrolment: Number
                    }
                ],
                // required: false,
                default: []
            }
        }
    }
};

module.exports = ExternalEnrolments;
