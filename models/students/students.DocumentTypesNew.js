let DocumentTypesNew = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'DocumentTypesNew',
    fields: {
      name: {
        type: String,
        index: true,
        required: true
      },
      certifiers: {
        type: [String],
        required: true,
      },
      courseTypes: {
        type: [String],
        required: true,
      },
      applicableOn: {
        type: String,
        required: true,
        enum: [
          'students',
        ],
      },
      diplomaDoc: {
        type: String,
        enum: [
          'AtoNaturalizacao',
          'CertidaoCasamento',
          'CertidaoNascimento',
          'ComprovacaoEstagioCurricular',
          'DocumentoIdentidadeDoAluno',
          'HistoricoEscolar',
          'ProvaColacao',
          'ProvaConclusaoEnsinoMedio',
          'TermoResponsabilidade',
          'TituloEleitor',
          'Outros',
        ],
      },
      genreRequired: {
        type: [{
          type: String,
          enum: [
            'M',
            'F'
          ]
        }],
        required: true,
        default: [
          'M',
          'F',
        ]
      },
    },
    options: {
      timestamp: true
    }
  }
};

module.exports = DocumentTypesNew;
