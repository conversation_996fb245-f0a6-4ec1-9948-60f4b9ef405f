const mongoose = require('mongoose');
const moment = require('moment');
const mathjs = require('mathjs');
const { _getModels } = require('../../services/Utils');
const request = require('request-promise');
const utilsFunctionToObjectId = require('../../helpers/UtilsFunctionToObjectId');

//import CheckPillars from '../../services/ENROLMENT/checkPillars.service';
// TODO: Falta desenvolver o fechamento de diario, sendo necessario uma campo para armazenar o total de aulas dadas e o status da disciplina
// const checkPresence = (enrolment) => {
//   const { presence, minimalFrequency } = enrolment.registryCourse.course;
//   const { numberPresence, numberLack } = presence;
//   const total = numberPresence + numberLack;
//   const percentage = numberPresence / total * 100;
//   return percentage >= minimalFrequency;
// }

/*
 * Start
 * AUXILIARY FUNCTIONS
 * ############################################################################
 * */
const notNullOrUndefined = (value) => {
  return value !== null && value !== undefined && value !== '';
};

const getActivitiesByOptions = (activities = [], options = {}) => {
  const { requireGrade = false, types = [] } = options;

  return activities.filter((activity) => {
    const matchesType = types.includes(activity.type);
    const hasGrade = notNullOrUndefined(activity.grade);
    return (!types.length || matchesType) && (!requireGrade || hasGrade);
  });
};

const getReferencedIds = (activities, modelKey, idKey = '') => {
  // Temp
  if (idKey === 'directStudyId' || idKey === 'directedStudyId') {
    return activities
      .filter((a) => a.model === modelKey)
      .map((a) => (a['directStudyId'] ?? a['directedStudyId'] ?? '').toString())
      .filter((id) => !!id);
  }
  return activities
    .filter((a) => a.model === modelKey)
    .map((a) => a[idKey].toString())
    .filter((id) => !!id);
};

const filterByTypeAndExclude = (items, type, excludeIds = [], idKey = '') => {
  // Temp
  if (idKey === 'directStudyId' || idKey === 'directedStudyId') {
    return (items || []).filter(
      (item) =>
        !!item?.type &&  item?.type === type &&
        !excludeIds.includes(
          (item['directStudyId'] ?? item['directedStudyId'] ?? '').toString(),
        ),
    );
  }
  return (items || []).filter(
    (item) =>
      item?.type === type && !excludeIds.includes(((item ?? {})[idKey] ?? 'a').toString()),
  );
};

const getRegularActivities = (disc) => {
  const regularActivities = getActivitiesByOptions(disc.activities, {types: ['regular']});

  const regularForumIds = getReferencedIds(regularActivities, 'forum', 'forumId');
  const regularDirectedStudyIds = getReferencedIds(regularActivities, 'directedStudy', 'directedStudyId');

  const evaluationForums = filterByTypeAndExclude(disc.forums, 'avaliativo', regularForumIds, 'forumId');
  const evaluationDirectedStudies = filterByTypeAndExclude(disc.directedStudies, 'avaliativo', regularDirectedStudyIds, 'directedStudyId');

  const allRegularActivities = [
    ...regularActivities,
    ...evaluationForums,
    ...evaluationDirectedStudies,
  ];

  return { regularActivities, allRegularActivities };
};

const averageGrade = (activities, pow) => {
  const hasRecuperationComplete = activities.find(item => item.type === 'recuperation' && notNullOrUndefined(item.grade));

  if (hasRecuperationComplete) return hasRecuperationComplete.grade;

  const validGrades = activities
    .map((a) => a?.grade)
    .filter((g) => notNullOrUndefined(g));
  if (!validGrades.length) return 0;
  const sum = validGrades.reduce((a, b) => a + b, 0);
  return sum / mathjs.pow(activities.length, pow);
};

const computeAverage = (disc, activities, isModularSum, createdAt, pow, referenceDate) => {
  const createdAfterLimit = moment(createdAt).startOf('day').isAfter(referenceDate.endOf('day'));
  const hasRecuperationGrade = activities.some(a => notNullOrUndefined(a?.grade) && a.type === 'recuperation');

  if (createdAfterLimit && notNullOrUndefined(disc?.grade)) {
    if (isModularSum && hasRecuperationGrade) {
      const regularGrades = getActivitiesByOptions(disc.activities, { types: ['regular'], requireGrade: true })
        .map(a => a.grade);

      const recuperationGrades = getActivitiesByOptions(disc.activities, { types: ['recuperation'], requireGrade: true })
        .map(a => a.grade);

      const totalSum = [...regularGrades, ...recuperationGrades].reduce((a, b) => a + b, 0);
      return totalSum / 2;
    }
  }

  return averageGrade(activities, pow);
};

const setMinAndMaxDateToCompleteFields = (enrolment, enrolmentRef) => {
  const { dateStart } = enrolment.registryCourse;

  const {
    minMonthsToComplete,
    maxMonthsToComplete,
  } = enrolment.registryCourse.course;

  if (
    dateStart &&
    minMonthsToComplete &&
    maxMonthsToComplete
  ) {
    const minDateToComplete = moment(dateStart)
      .add(minMonthsToComplete, 'months')
      .toDate();
    const maxDateToComplete = moment(dateStart)
      .add(maxMonthsToComplete, 'months')
      .toDate();

    enrolmentRef.set({
      'registryCourse.course.minDateToComplete': minDateToComplete,
      'registryCourse.course.maxDateToComplete': maxDateToComplete,
    });
  }
};
/*
 * End
 * AUXILIARY FUNCTIONS
 * ############################################################################
 * */

const modelName = 'Enrolments';

const _getCourseNumber = async (models, e, id = true, extraFields = []) => {
  let find = {
    _certifierName: e.registryCourse.course._certifierName,
    _typeName: e.registryCourse.course._typeName,
    _name: e.registryCourse.course._name,
  };
  if (id) {
    find = {
      _id: utilsFunctionToObjectId.convert(
        e.registryCourse.course._courseId.toString(),
      ),
    };
  }
  if (extraFields && Array.isArray(extraFields) && extraFields.length) {
    extraFields.forEach((e) => {
      find[e.field] = e.value;
    });
  }
  const courseObj = await models.Courses.findOne(find)
    .then(async (r) => r)
    .catch(async (e) => {
      console.log(e);
      return null;
    });
  if (courseObj && typeof courseObj === 'object' && courseObj.number) {
    return courseObj.number;
  } else {
    return 0;
  }
};

const reducedPaymentPlanSchema = {
  installment: {
    type: Number,
    // required: false
  },
  value: {
    type: Number,
    // required: false
  },
  totalInstallments: {
    type: Number,
  },
};

const reEnrolmentSchema = {
  _id: {
    type: mongoose.SchemaTypes.ObjectId,
    required: true,
  },
  term: {
    isRequired: {
      type: Boolean,
      required: true,
    },
    wasAccepted: {
      type: Boolean,
      required: true,
    },
    responseDate: {
      type: Date,
      // required: false
    },
    termStorageUrl: {
      type: String,
      default: undefined,
    },
  },
  dateEnd: {
    type: Date,
    required: true,
  },
  generateBilling: {
    type: Boolean,
    required: true,
    default: false,
  },
  wasBlocked: {
    type: Boolean,
    default: false,
  },
  paymentPlan: reducedPaymentPlanSchema,
  generatedChargesByCron: {
    type: Boolean,
    default: false,
  },
  paymentPlanCharges: {
    type: [mongoose.SchemaTypes.ObjectId],
    required: false,
    ref: 'Charges',
  },
  charge: {
    type: {
      _id: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      amount: {
        type: Number,
        required: true,
      },
      dueDate: {
        type: Date,
        required: true,
      },
      status: {
        type: String,
        required: true,
        default: 'waiting_payment',
      },
    },
    // required: false
  },
};

const extraCoursewaresSchema = {
  _id: {
    type: mongoose.SchemaTypes.ObjectId,
  },
  title: {
    type: String,
    index: true,
  },
  alias: {
    type: String,
    index: true,
  },
  type: {
    type: String,
    // required: false,
    default: 'pdf',
  },
  file: {
    type: String,
    // required: false,
    allowNull: true,
    default: null,
    index: true,
  },
  content: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  description: {
    type: String,
    allowNull: true,
    default: null,
    // required: false
  },
  contentPortal: {
    type: String,
    // required: false,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  chapter: {
    type: Number,
  },
  page: {
    type: Number,
  },
  teacher: {
    type: String,
  },
};

// noinspection JSUnusedGlobalSymbols,JSUnusedLocalSymbols
const Enrolments = {
  schemas: {
    reEnrolment: function () {
      return {
        schema: reEnrolmentSchema,
        isArray: true,
        virtual: {
          status: async function () {
            let model = null;
            if (
              this.generateBilling &&
              this.charge?._id &&
              this.parent() &&
              typeof this.parent() === 'object' &&
              this.parent()._id
            ) {
              if (
                this.parent().constructor &&
                ['function'].includes(typeof this.parent().constructor) &&
                this.parent().constructor.modelName &&
                this.parent().constructor.modelName === modelName
              ) {
                model = this.parent().constructor;
              }
            }
            if (
              model?.$parent?.models &&
              'Charges' in model.$parent.models
            ) {
              const charge = await model.$parent.models.Charges.findOne({
                _id: this.charge._id,
              }).exec();
              if (
                charge &&
                typeof charge === 'object' &&
                charge._id &&
                charge.status &&
                charge.status !== this.charge.status
              ) {
                this.charge.status = charge.status;
                await this.parent().save();
              }
            }
            if (
              (!this.term?.isRequired ||
                !!this.term?.wasAccepted) &&
              (!this.generateBilling ||
                ['paid', 'free'].includes(
                  (this.charge?.status || '').toString().toLowerCase(),
                ))
            ) {
              return 'complete';
            } else {
              return 'pending';
            }
          },
        },
        path: ['registryCourse', 'course', 'class', 'type'],
        prop: 'reEnrolments',
      };
    },
  },

  functions: {
    toAsyncObject: async function (options) {
      const obj = this.toObject(options);
      if ((((this || {}).constructor || {}).schema || {}).virtuals) {
        const virtuals = this.constructor.schema.virtuals;
        for (const prop in virtuals) {
          const virtual = virtuals[prop];
          if (virtual.getters[0].constructor.name === 'AsyncFunction') {
            obj[prop] = await this[prop];
          }
        }
      }
      if ((this?.registryCourse?.course?.class?.reEnrolments || []).length) {
        for (
          let i = 0;
          i < this.registryCourse.course.class.reEnrolments.length;
          i++
        ) {
          if (
            'toAsyncObject' in this.registryCourse.course.class.reEnrolments[i]
          ) {
            obj.registryCourse.course.class.reEnrolments[i] =
              await this.registryCourse.course.class.reEnrolments[
                i
              ].toAsyncObject();
          }
        }
      }
      return obj;
    },

    getClassId: function () {
      return this?.registryCourse?.course?.class?._id || null;
    },

    checkActivitiesGradeStudents: async function (enrolment) {
      if (enrolment.registryCourse.course.class?.closeDiary) {
        return enrolment;
      }

      const evaluationMethod = (
        enrolment.registryCourse.course.class?.evaluationMethod ||
        enrolment.registryCourse.course.evaluationMethod
      ).toLowerCase();
      const methodIsModularSum = ['modular_sum'].includes(evaluationMethod);
      const referenceDate = moment('2023-08-19');
      const method = methodIsModularSum
        ? { maxGrade: 100, pow: 0 }
        : { maxGrade: 10, pow: 1 };
      const minToApprove =
        method.maxGrade *
        (enrolment.registryCourse.course.approvalPercentage / 100);

      const onlyWithoutGradeOrLessThenMinToApprove = (discipline) =>
        !discipline.grade || discipline.grade < minToApprove;

      const onlyRecuperation = (activity) => activity.type === 'recuperation';

      const onlyRegular = (activity) => activity.type === 'regular';

      const onlyWithGrade = (v) => v.grade !== null && v.grade !== undefined;

      // Pode atualizar a nota quando já estiver lançado em outro momento
      // Isso acontece quanto repetir o lançamento (Para cobrir os casos de lançamentos anteriores incorretos)
      enrolment.registryCourse.course.disciplines
        .filter(onlyWithGrade)
        .map((discipline) => {
          const isOnRecuperation =
            discipline.isOnRecuperation ||
            discipline.activities.find(
              (da) => !!da.grade && da.type === 'recuperation',
            );
          const { allRegularActivities } = getRegularActivities(discipline);

          // Apenas atividades regulares para atualizar a nota no caso de repetir lançamento
          const filteredActivities = isOnRecuperation
            ? discipline.activities.filter(onlyRecuperation)
            : discipline.activities.filter(onlyRegular);
          const allGraded = filteredActivities.every((a) =>
            notNullOrUndefined(a?.grade),
          );

          // Calcula novamente a média caso esteja lançando uma nova nota
          if (
            allGraded &&
            (filteredActivities.length === allRegularActivities.length ||
              isOnRecuperation)
          ) {
            const avg = computeAverage(
              discipline,
              filteredActivities,
              methodIsModularSum,
              enrolment.createdAt,
              method.pow,
              referenceDate,
            );
            // Alterar a média somente se estiver diferente
            if (avg !== discipline.grade) {
              // discipline.gradeType !== "manual" Lançamentos do tipo edição estava sendo sobrescrevido por esta trigger
              if (discipline.gradeType === 'manual') {
                discipline = Object.assign(
                  discipline,
                  (function getUpdatedGradeFlags() {
                    return {
                      grade: avg,
                      isOnRecuperation: avg < minToApprove,
                      status: avg < minToApprove ? 'recuperation' : 'approved',
                    };
                  })(),
                );
              } else {
                discipline = Object.assign(
                  discipline,
                  (function getUpdatedGradeFlags() {
                    return {
                      grade: avg,
                      isOnRecuperation: avg < minToApprove,
                      status: avg < minToApprove ? 'recuperation' : 'approved',
                      gradeType: 'standard',
                    };
                  })(),
                );

                discipline.gradeHistory.push({
                  _userName: 'Computer - Only direct Model (263)',
                  _userId: utilsFunctionToObjectId.convert(
                    '5fc921ea85488e43d1d34d2b',
                  ),
                  grade: avg,
                  gradeType: 'standard',
                  launchedAt: new Date(),
                });
              }
            }
          }

          return discipline;
        });

      // Apenas disciplinas que a nota é inferior ao minimo
      // Adiciona em recuperação caso a nota seja baixa
      // Calcula a média da disciplina de acordo com a nota
      enrolment.registryCourse.course.disciplines
        .filter(onlyWithoutGradeOrLessThenMinToApprove)
        .map((discipline) => {
          const isOnRecuperation =
            discipline.isOnRecuperation ||
            discipline.activities.find(
              (da) => !!da.grade && da.type === 'recuperation',
            );
          const { allRegularActivities } = getRegularActivities(discipline);
          const filteredActivities = isOnRecuperation
            ? discipline.activities.filter(onlyRecuperation)
            : discipline.activities.filter(onlyRegular);
          const allGraded = filteredActivities.every((a) =>
            notNullOrUndefined(a?.grade),
          );

          if (
            allGraded &&
            (filteredActivities || []).length &&
            (filteredActivities.length === allRegularActivities.length ||
              isOnRecuperation)
          ) {
            const avg = computeAverage(
              discipline,
              filteredActivities,
              methodIsModularSum,
              enrolment.createdAt,
              method.pow,
              referenceDate,
            );

            // Alterar a média somente se estiver diferente
            if (avg !== discipline.grade) {
              discipline = Object.assign(
                discipline,
                (function getUpdatedGradeFlags() {
                  return {
                    grade: avg,
                    isOnRecuperation: avg < minToApprove,
                    status: avg < minToApprove ? 'recuperation' : 'approved',
                    gradeType: 'standard',
                  };
                })(),
              );

              discipline.gradeHistory.push({
                _userName: 'Computer Only Model (326)',
                _userId: utilsFunctionToObjectId.convert(
                  '5fc921ea85488e43d1d34d2b',
                ),
                grade: avg,
                gradeType: 'standard',
                launchedAt: new Date(),
              });
            }
          }
          return discipline;
        });
      return await enrolment.save();
    },

    checkActivitiesGrade: async function (
      enrolment,
      isOnRecuperationParam = null,
      force = false,
      discId = '',
    ) {
      const referenceDate = moment('2023-08-19');

      const course = enrolment.registryCourse.course;
      const classInfo = course.class || {};

      if (classInfo?.closeDiary) return enrolment;
      if (discId) force = false;

      const evaluationMethod = (
        classInfo?._id
          ? classInfo.evaluationMethod
          : course.evaluationMethod || ''
      )
        .toString()
        .toLowerCase();
      const isModularSum = evaluationMethod === 'modular_sum';
      const method = isModularSum
        ? { maxGrade: 100, pow: 0 }
        : { maxGrade: 10, pow: 1 };
      const minToApprove = method.maxGrade * (course.approvalPercentage / 100);

      const disciplines = course.disciplines.filter((disc) => {
        const discMatches =
          discId &&
          (discId.toString() === (disc.disciplineClass?._id || '').toString() ||
            discId.toString() === disc._id.toString());
        const noGradeOrFail = !disc.grade || disc.grade < minToApprove;
        return discMatches || (!discId && (noGradeOrFail || force));
      });

      disciplines.forEach((disc) => {
        let isOnRecuperation =
          typeof isOnRecuperationParam === 'boolean'
            ? isOnRecuperationParam
            : disc.isOnRecuperation ||
              disc.activities?.some(
                (a) =>
                  a.type === 'recuperation' && notNullOrUndefined(a?.grade),
              );

        let activities = getActivitiesByOptions(disc.activities, {
          types: ['recuperation'],
        });
        const { regularActivities, allRegularActivities } =
          getRegularActivities(disc);

        //recheck activities
        if (isOnRecuperation) {
          const avgRegular = averageGrade(regularActivities, method.pow);
          if (avgRegular >= minToApprove) {
            isOnRecuperation = false;
            activities = regularActivities;
          }
        } else {
          activities = allRegularActivities;
        }

        if (
          isModularSum &&
          isOnRecuperation &&
          moment(enrolment.createdAt).isAfter(referenceDate)
        ) {
          activities = (disc.activities || []).filter((a) =>
            notNullOrUndefined(a?.grade),
          );
        }

        const allGraded = activities.every((a) => notNullOrUndefined(a?.grade));
        if (
          !allGraded ||
          !activities.length ||
          (activities.length !== allRegularActivities.length &&
            !isOnRecuperation)
        )
          return;

        //fixme foi adicionado duas condicionais o aluno ter sido cadastrado depois daquela data (referenceDate) e ter nota.
        const avg = computeAverage(
          disc,
          activities,
          isModularSum,
          enrolment.createdAt,
          method.pow,
          referenceDate,
        );
        const status = avg < minToApprove ? 'recuperation' : 'approved';

        if (avg !== disc.grade) {
          disc.grade = avg;
          disc.isOnRecuperation = status === 'recuperation';
          disc.status = status;
          disc.gradeType = 'standard';

          disc.gradeHistory.push({
            _userName: 'Computer Only Model (404)',
            _userId: utilsFunctionToObjectId.convert(
              '5fc921ea85488e43d1d34d2b',
            ),
            grade: avg,
            gradeType: 'standard',
            launchedAt: new Date(),
          });
        }
      });

      return await enrolment.save();
    },

    checkRecuperation: async function (
      enrolment,
      discId = '',
      dependecyLimit = 0,
    ) {
      if ((enrolment.registryCourse.course.class || {}).closeDiary) {
        return enrolment;
      }

      const evaluationMethod = (enrolment.registryCourse.course.class || {})._id
        ? ((enrolment.registryCourse.course.class || {}).evaluationMethod || '')
          .toString()
          .toLowerCase()
        : (enrolment.registryCourse.course.evaluationMethod || '')
          .toString()
          .toLowerCase();
      const method = ['modular_sum'].includes(evaluationMethod)
        ? { maxGrade: 100, pow: 0 }
        : { maxGrade: 10, pow: 1 };
      const minToApprove =
        method.maxGrade *
        (enrolment.registryCourse.course.approvalPercentage / 100);
      const isDisciplinaIsolada =
        enrolment.registryCourse.course._typeName === 'Disciplina Isolada';

      enrolment.registryCourse.course.disciplines
        .filter(
          (_d) =>
            (_d.grade && _d.grade > -1) ||
            (discId &&
              (discId.toString() ===
                ((_d.disciplineClass || {})._id || '').toString() ||
                discId.toString() === _d._id.toString())),
        )
        .forEach((_disc) => {
          const hasRecuperationActivite = (((_disc || {}).activities || []).filter(item => item.type === 'recuperation'));
          const forumsActivities = ((_disc || {}).forums || []).filter(
            (item) => item.type === 'avaliativo',
          );
          const directedStudieActivities = (
            (_disc || {}).directedStudies || []
          ).filter((item) => item.type === 'avaliativo');
          const forumsActivitiesWithoutGrade = ((_disc || {}).activities || [])
            .filter((item) => item.forumId)
            .filter((item) =>
              forumsActivities.find(
                (forum) => forum.forumId.toString() === item.forumId.toString(),
              ),
            );
          const directedStudieActivitiesWithoutGrade = (
            (_disc || {}).activities || []
          )
            .filter((item) => item.directedStudyId)
            .filter((item) =>
              directedStudieActivities.find(
                (directedStudy) =>
                  item.directedStudyId.toString() ===
                  directedStudy.directedStudyId.toString(),
              ),
            );

          let hasCalcStatus = true;
          if (forumsActivities.length !== forumsActivitiesWithoutGrade.length)
            hasCalcStatus = false;
          if (
            directedStudieActivities.length !==
            directedStudieActivitiesWithoutGrade.length
          )
            hasCalcStatus = false;
          if (hasCalcStatus) {
            const recuperationActivity = _disc.activities
              .filter((item) => (item || {}).type === 'recuperation')
              .find((item) => typeof (item || {}).grade === 'number');
            _disc.isOnRecuperation = _disc.grade < minToApprove;
            if (_disc.status !== 'disapproved')
              _disc.status = _disc.isOnRecuperation
                ? 'recuperation'
                : 'approved';

            if (
              dependecyLimit &&
              _disc.status === 'recuperation' &&
              _disc.isDependecy &&
              typeof (recuperationActivity || {}).grade === 'number' &&
              _disc.isOnRecuperation
            )
              _disc.status =
                (_disc.dependecyAttempts || 0) >= dependecyLimit
                  ? 'disapproved'
                  : 'recuperation';

            if (
              isDisciplinaIsolada &&
              _disc.status === 'recuperation' &&
              typeof (recuperationActivity || {}).grade === 'number' &&
              _disc.isOnRecuperation
            ) {
              _disc.status = 'disapproved';
            }

            if (_disc.status === 'recuperation' && !hasRecuperationActivite.length) _disc.status = 'disapproved';
          }
        });

      return await enrolment.save();
    },

    checkDisciplineGrade: async function (
      enrolment,
      discId = '',
      dependecyLimit = 0,
    ) {
      const haveAllGrades =
        enrolment.registryCourse.course.disciplines.filter(
          (_d) =>
            !!_d.grade ||
            (discId &&
              (discId.toString() ===
                ((_d.disciplineClass || {})._id || '').toString() ||
                discId.toString() === _d._id.toString())),
        ).length === enrolment.registryCourse.course.disciplines.length;

      if (haveAllGrades) {
        const evaluationMethod = (enrolment.registryCourse.course.class || {})
          ._id
          ? (
            (enrolment.registryCourse.course.class || {}).evaluationMethod ||
              ''
          )
            .toString()
            .toLowerCase()
          : (enrolment.registryCourse.course.evaluationMethod || '')
            .toString()
            .toLowerCase();
        const method = ['modular_sum'].includes(evaluationMethod)
          ? { maxGrade: 100, pow: 0 }
          : { maxGrade: 10, pow: 1 };
        const minToApprove =
          method.maxGrade *
          (enrolment.registryCourse.course.approvalPercentage / 100);

        let approved = true;
        let disapproved = false;
        const isDisciplinaIsolada = enrolment.registryCourse.course._typeName === 'Disciplina Isolada';
        enrolment.registryCourse.course.disciplines.forEach((_d) => {
          const hasRecuperationActivite = _d.activities.filter(item => item.type === 'recuperation');
          const recuperationActivity = _d.activities
            .filter((item) => (item || {}).type === 'recuperation')
            .find((item) => typeof (item || {}).grade === 'number');

          _d.isOnRecuperation = _d.grade < minToApprove;
          if (_d.status !== 'disapproved') _d.status = _d.isOnRecuperation ? 'recuperation' : 'approved';

          if (discId && _d._id === discId) {
            if (_d.grade < minToApprove) {
              if (!(enrolment.registryCourse.course.class || {}).closeDiary) {
                _d.isOnRecuperation = true;
                _d.status = 'recuperation';
              }
              approved = false;
            } else {
              if (!(enrolment.registryCourse.course.class || {}).closeDiary) {
                _d.status = 'approved';
              }
            }
          } else {
            if (_d.grade < minToApprove) {
              if (!(enrolment.registryCourse.course.class || {}).closeDiary) {
                _d.isOnRecuperation = true;
                _d.status = 'recuperation';
              }
              approved = false;
            } else {
              if (!(enrolment.registryCourse.course.class || {}).closeDiary) {
                _d.status = 'approved';
              }
            }
          }

          if (
            dependecyLimit &&
            _d.status === 'recuperation' &&
            _d.isDependecy &&
            typeof (recuperationActivity || {}).grade === 'number' &&
            _d.isOnRecuperation
          )
            _d.status =
              (_d.dependecyAttempts || 0) >= dependecyLimit
                ? 'disapproved'
                : 'recuperation';

          if (
            isDisciplinaIsolada &&
            _d.status === 'recuperation' &&
            typeof (recuperationActivity || {}).grade === 'number' &&
            _d.isOnRecuperation
          ) {
            _d.status = 'disapproved';
          }

          if (_d.status === 'recuperation' && !hasRecuperationActivite.length) _d.status = 'disapproved';
        });
        const allDisciplinesApproved =
          enrolment.registryCourse.course.disciplines.map(
            (item) => item.status === 'approved',
          );

        const allDisciplinesDisapproved = enrolment.registryCourse.course.disciplines.filter((item) => item.status === 'disapproved');
        if (!allDisciplinesDisapproved.length && enrolment.status === 'disapproved') enrolment.status = 'in_progress';

        if (
          allDisciplinesApproved.length !==
          enrolment.registryCourse.course.disciplines.length
        )
          approved = false;

        if (approved && ((enrolment || {}).pillars || []).length) {
          enrolment.status = 'in_progress';
          enrolment.pillars = (enrolment.pillars || []).map((_p) => {
            return _p.name === 'Avaliação'
              ? {
                name: 'Avaliação',
                isCompleted: true,
              }
              : _p;
          });
        }

        if (allDisciplinesDisapproved.length === enrolment.registryCourse.course.disciplines.length) disapproved = true;
        if (disapproved && !approved) enrolment.status = 'disapproved';

        enrolment.markModified('pillars');

        return await enrolment.save();
      }
      return Promise.resolve(enrolment);
    },

    checkDocuments: async function (enrolment) {
      // let models = null;
      // if (((this.$__ || {}).saveOptions || {}).models) {
      //     models = this.$__.saveOptions.models;
      // } else {
      //     models = httpContext.get('models');
      // }
      if (enrolment) {
        return Promise.resolve(null);
      }
      /*const checkPillarsService = new CheckPillars(null,{models: models});
            return await checkPillarsService.setEnrolment(enrolment).then(
                async () => await checkPillarsService.check(['documents'])
            ).catch((err) => console.error(err));*/
      return Promise.resolve(null);
    },

    auditCommissions: async function (enrolment) {
      const notifyAuditCommission = (reg) => {
        const SLACK_WEBHOOK_URL =
          '*******************************************************************************';
        return request({
          method: 'POST',
          json: true,
          uri: SLACK_WEBHOOK_URL,
          body: {
            blocks: [
              {
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: `Treta ao realizar inscricao: \n\n ${reg.description}`,
                },
              },
            ],
            attachments: [
              {
                color: '#a63636',
                blocks: [
                  {
                    type: 'section',
                    text: {
                      type: 'mrkdwn',
                      text: '```' + JSON.stringify(reg.data, null, 4) + '```',
                    },
                    accessory: {
                      type: 'image',
                      image_url:
                        'https://storage.googleapis.com/prominaserp/slack_notifications/log-error.png',
                      alt_text: 'Erro',
                    },
                  },
                ],
              },
            ],
          },
        });
      };
      let models = null;
      if (((this.$__ || {}).saveOptions || {}).models) {
        models = this.$__.saveOptions.models;
      }
      if (enrolment.indication && enrolment.indication.length > 0) {
        const troubles = [];

        enrolment.indication.forEach((indication) => {
          if (indication.commissionEnrolment > 0) {
            const commissionType =
              'commissionType' in indication
                ? indication.commissionType
                : 'percentage';

            if (commissionType === 'value') {
              if (
                indication.commissionEnrolment >
                enrolment.metadata.prices.enrolment.final
              )
                troubles.push({
                  description:
                    'Valor da comissao taxa de matricula maior do que o permitido',
                  data: {
                    rateEnrolmentValue:
                      enrolment.metadata.prices.enrolment.final,
                    indication,
                    _enrolmentId: enrolment._id,
                  },
                });
            } else if (commissionType === 'percentage') {
              if (indication.commissionEnrolment > 100)
                troubles.push({
                  description:
                    'Valor da comissao taxa de matricula maior do que o permitido',
                  data: {
                    indication,
                    _enrolmentId: enrolment._id,
                  },
                });
            }
          }
          if (indication.commissionMonthly > 0) {
            if (indication.commissionMonthly > 50) {
              troubles.push({
                description:
                  'Valor da comissao mensalidades maior do que o permitido',
                data: {
                  indication,
                  _enrolmentId: enrolment._id,
                },
              });
            }
          }
        });
        if (troubles.length > 0) {
          await Promise.map(troubles, (trouble) =>
            notifyAuditCommission(trouble),
          );
          await models.AuditCommissionLogs.create(troubles);
        }
      }
    },

    checkDateConclusion: async function (enrolment) {
      if (
        enrolment.pillars.filter((_p) => _p.isCompleted).length ===
        enrolment.pillars.length &&
        !enrolment.registryCourse.dateEnd
      ) {
        enrolment.registryCourse.dateEnd = moment().startOf('day');

        return await enrolment.save();
      }

      return Promise.resolve(true);
    },

    setNumber: async function (enrolment, session = null) {
      const saveOptions = {
        validateBeforeSave: false,
        session: session,
        disableTriggers: true,
      };

      const _getYear = (date = undefined, full = true) => {
        return moment(date)
          .startOf('day')
          .format(full ? 'YYYY' : 'YY');
      };

      /*const _getYearDates = (year) => {
                return {
                    start: moment(`${year}-01-01T03:00:00.000Z`).startOf('month').startOf('day').toDate(),
                    end: moment(`${year}-12-01T03:00:00.000Z`).endOf('month').endOf('day').toDate()
                };
            }*/

      const _getSemester = (date = undefined) => {
        const secondSemester = moment(
          `${_getYear(date)}-07-01T03:00:00.000Z`,
        ).startOf('day');
        return moment(date).endOf('day').isBefore(secondSemester) ? 1 : 2;
      };

      /*const _getSemesterDates = (year, semester) => {
                const startMonth = semester === 1 ? '01' : '07';
                const endMonth = semester === 1 ? '06' : '12';
                return {
                    start: moment(`${year}-${startMonth}-01T03:00:00.000Z`).startOf('month').startOf('day').toDate(),
                    end: moment(`${year}-${endMonth}-01T03:00:00.000Z`).endOf('month').endOf('day').toDate()
                };
            }*/

      if (Number.isNaN(session)) session = this.$__?.saveOptions?.session;

      let model = null;
      if (
        this.constructor &&
        ['function'].includes(typeof this.constructor) &&
        this.constructor.modelName &&
        this.constructor.modelName === modelName
      ) {
        model = this.constructor;
      } else if (
        this &&
        ['function'].includes(typeof this) &&
        this.modelName &&
        this.modelName === modelName
      ) {
        model = this;
      } else if (
        this &&
        ['object'].includes(typeof this) &&
        this.model &&
        ['function'].includes(typeof this.model) &&
        this.model.modelName &&
        this.model.modelName === modelName
      ) {
        model = this.model;
      }
      let models = null;
      let connection = null;
      if (model && model.$parent) {
        connection = model.$parent;
        if (connection) {
          models = connection.models;
        }
      }
      if (models && models.Enrolments) {
        const e =
          enrolment &&
            typeof enrolment === 'object' &&
            enrolment._id &&
            !mongoose.Types.ObjectId.isValid(enrolment.toString())
            ? enrolment
            : await models.Enrolments.findOne({
              _id: utilsFunctionToObjectId.convert(enrolment.toString()),
            }).session(session);
        if (e && typeof e === 'object' && e._id) {
          let disableTriggers = false;
          if (((this.$__ || {}).saveOptions || {}).disableTriggers) {
            disableTriggers = true;
          }
          if (disableTriggers) {
            return e;
          }
          if (!(e.enrolmentNumber || {}).number) {
            if (
              models.Courses &&
              models.CourseTypes &&
              models.Certifiers &&
              models.Configurations
            ) {
              const { Enrolments, CourseTypes, Certifiers, Configurations } =
                models;
              const find = {
                'enrolmentNumber.number': {
                  $ne: null,
                },
              };
              let config = await Configurations.findOne({
                name: 'enrolment_number',
                isActive: true,
              })
                .then(async (r) => r)
                .catch(async (e) => {
                  console.error(e);
                  return null;
                });
              if (!(config && typeof config === 'object' && config._id)) {
                return e;
              }
              config = JSON.parse(JSON.stringify((config || {}).value || {}));
              const compose = [
                'certifier',
                'course',
                'courseType',
                'enrolment',
                'semester',
                'year',
              ];
              let itens = [];
              Object.keys(config).forEach((k) => {
                if (
                  compose.includes(k) &&
                  config[k] &&
                  typeof config[k] === 'object' &&
                  !!config[k].enable &&
                  !!config[k].order
                ) {
                  itens.push(
                    Object.assign(
                      {
                        name: k,
                      },
                      config[k],
                    ),
                  );
                }
              });
              itens = itens.sort((a, b) => a.order - b.order);
              let number = 1;
              let fullYear = _getYear(undefined);
              let year = _getYear(undefined, false);
              let semester = _getSemester();
              let certifier = 0;
              let course = 0;
              let courseType = 0;
              let autoIncrement = true;
              const enrolmentNumberEnabled = !!((config || {}).enrolment || {})
                .enable;
              if (
                itens &&
                Array.isArray(itens) &&
                itens.length &&
                enrolmentNumberEnabled &&
                !config.autoIncrement
              ) {
                autoIncrement = false;
                for (let i = 0; i < itens.length; i++) {
                  switch (itens[i].name) {
                  case 'certifier':
                    if (
                      ((e.registryCourse || {}).course || {})._certifierName
                    ) {
                      const certifierObj = await Certifiers.findOne({
                        name: e.registryCourse.course._certifierName,
                      })
                        .then(async (r) => r)
                        .catch(async (e) => {
                          console.log(e);
                          return null;
                        });
                      if (
                        certifierObj &&
                          typeof certifierObj === 'object' &&
                          certifierObj.number
                      ) {
                        certifier = certifierObj.number;
                      }
                    }
                    itens[i].value = certifier;
                    config[itens[i].name].value = certifier;
                    if (itens[i].restart) {
                      find['enrolmentNumber.config.certifier.value'] =
                          certifier;
                    }
                    break;

                  case 'course':
                    if (((e.registryCourse || {}).course || {})._courseId) {
                      course = await _getCourseNumber(models, e);
                    }
                    if (
                      !course &&
                        ((e.registryCourse || {}).course || {})._name
                    ) {
                      course = await _getCourseNumber(models, e, false, [
                        {
                          field: 'acronym',
                          value: e.registryCourse.course.acronym,
                        },
                      ]);
                    }
                    if (
                      !course &&
                        ((e.registryCourse || {}).course || {})._name
                    ) {
                      course = await _getCourseNumber(models, e, false, [
                        {
                          field: 'workload',
                          value: e.registryCourse.course.workload,
                        },
                      ]);
                    }
                    if (
                      !course &&
                        ((e.registryCourse || {}).course || {})._name
                    ) {
                      course = await _getCourseNumber(models, e, false);
                    }
                    itens[i].value = course;
                    config[itens[i].name].value = course;
                    if (itens[i].restart) {
                      find['enrolmentNumber.config.course.value'] = course;
                    }
                    break;

                  case 'courseType':
                    if (((e.registryCourse || {}).course || {})._typeName) {
                      const typeObj = await CourseTypes.findOne({
                        name: e.registryCourse.course._typeName,
                      })
                        .then(async (r) => r)
                        .catch(async (e) => {
                          console.log(e);
                          return null;
                        });
                      if (
                        typeObj &&
                          typeof typeObj === 'object' &&
                          typeObj.number
                      ) {
                        courseType = typeObj.number;
                      }
                    }
                    itens[i].value = courseType;
                    config[itens[i].name].value = courseType;
                    if (itens[i].restart) {
                      find['enrolmentNumber.config.courseType.value'] =
                          courseType;
                    }
                    break;

                  case 'semester':
                    if ((e.registryCourse || {}).dateStart) {
                      fullYear = _getYear(e.registryCourse.dateStart);
                      semester = _getSemester();
                    } else {
                      e.registryCourse.dateStart = moment().toDate();
                    }
                    itens[i].value = semester;
                    config[itens[i].name].value = semester;
                    if (itens[i].restart) {
                      find['enrolmentNumber.config.semester.value'] =
                          semester;
                    }
                    break;

                  case 'year': {
                    if ((e.registryCourse || {}).dateStart) {
                      fullYear = _getYear(e.registryCourse.dateStart);
                      year = _getYear(e.registryCourse.dateStart, false);
                    } else {
                      e.registryCourse.dateStart = moment().toDate();
                    }
                    const yearValue = itens[i].full
                      ? parseInt(fullYear)
                      : parseInt(year);

                    itens[i].value = yearValue;
                    config[itens[i].name].value = yearValue;
                    if (itens[i].restart) {
                      find['enrolmentNumber.config.year.value'] = yearValue;
                    }
                    break;
                  }
                  }
                }
              }
              const lastObj = await Enrolments.findOne(
                find,
                {},
                {
                  sort: {
                    'enrolmentNumber.config.enrolment.value': -1,
                  },
                },
              )
                .then(async (r) => r)
                .catch(async (e) => {
                  console.log(e);
                  return null;
                });
              if (lastObj && typeof lastObj === 'object' && lastObj._id) {
                number =
                  ((
                    ((lastObj.enrolmentNumber || {}).config || {}).enrolment ||
                    {}
                  ).value || 0) + 1;
              }
              while (true) {
                if (enrolmentNumberEnabled) {
                  const field = 'enrolment';
                  itens.find((i) => i.name === field).value = number;
                  config[field].value = number;
                }
                if (autoIncrement) {
                  e.enrolmentNumber = {
                    number: number.toString(),
                    config: {
                      enrolment: {
                        enable: true,
                        length: 0,
                        order: 1,
                        value: number,
                      },
                    },
                  };
                } else {
                  let finalNumber = '';
                  const itemDefaultLength = {
                    year: 0,
                    semester: 0,
                    certifier: 2,
                    courseType: 2,
                    course: 6,
                    enrolment: 6,
                  };
                  itens.forEach((item) => {
                    const defaultLength =
                      item.length || itemDefaultLength[item.name] || 0;
                    item.length = defaultLength;
                    config[item.name].length = defaultLength;
                    if (
                      defaultLength &&
                      item.value.toString().length > defaultLength
                    ) {
                      throw 'max_size_enrolment_number';
                    }
                    let defaultPad = '';
                    if (defaultLength) {
                      for (let j = 0; j < defaultLength; j++) {
                        defaultPad = `${defaultPad}0`;
                      }
                    }
                    finalNumber = `${finalNumber}${defaultLength
                      ? `${defaultPad}${item.value.toString()}`.substr(
                        defaultLength * -1,
                        defaultLength,
                      )
                      : item.value.toString()
                    }`;
                  });

                  e.enrolmentNumber = {
                    number: finalNumber,
                    config: config,
                  };
                }
                const ret = await e
                  .save(saveOptions)
                  .then(async (e) => e)
                  .catch(async (e) => e);
                if (ret && typeof ret === 'object' && ret._id) {
                  return ret;
                } else {
                  if (ret.toString().includes('number_already_exists')) {
                    number++;
                  } else {
                    console.log('Save error', ret);
                    return null;
                  }
                }
              }
            }
            return await e
              .save(saveOptions)
              .then(async (e) => e)
              .catch(async (e) => {
                console.log('Save error', e);
                return null;
              });
          } else {
            return e;
          }
        }
      }
      return null;
    },
    convertClassId: async function (next) {
      if (
        this.enrolment &&
        (((this.enrolment.registryCourse || {}).course || {}).class || {})._id
      ) {
        this.enrolment.registryCourse.course.class._id =
          utilsFunctionToObjectId.convert(
            this.enrolment.registryCourse.course.class._id,
          );
      }

      return next();
    },

    updateStudent: async function (next) {
      try {
        if (!this.documents || this.documents.length === 0) {
          return next();
        }

        const models = _getModels.call(this, 'Enrolments');
        const { Students } = models;

        const student = await Students.findOne(
          this._studentId ? { _id: this._studentId } : { cpf: this.cpf },
          { _id: 1, documents: 1 },
        );
        const documents = this.documents;

        try {
          for (const document of documents) {
            const found = student.documents
              .filter((_d) => !!_d && !!_d._documentTypeId)
              .find(
                (_d) =>
                  _d._documentTypeId.toString() ===
                  document._documentTypeId.toString(),
              );
            const skip =
              found &&
              found.courseTypes.includes(
                this.registryCourse.course._typeName,
              ) &&
              found.status === document.status;

            if (!skip) {
              if (found) {
                found.status = document.status;
                found.createdAt = document.createdAt;
                found.updatedAt = document.updatedAt;
                found.courseTypes = found.courseTypes.concat([
                  this.registryCourse.course._typeName,
                ]);

                await Students.updateOne(
                  { _id: student._id },
                  {
                    $set: {
                      documents: student.documents.map((_d) => {
                        return _d._documentTypeId.toString() ===
                          document._documentTypeId.toString()
                          ? found
                          : _d;
                      }),
                    },
                  },
                );
              } else {
                const parsedDoc = {
                  _documentTypeId: document._documentTypeId,
                  name: document.name,
                  courseTypes: [this.registryCourse.course._typeName],
                  createdAt: document.createdAt,
                  updatedAt: document.updatedAt,
                  status: document.status,
                  _studentDocumentId: document._studentDocumentId,
                };

                await Students.updateOne(
                  { _id: student._id },
                  {
                    $push: {
                      documents: parsedDoc,
                    },
                  },
                );
              }
            }
          }
        } catch (error) {
          console.error(error);
        }

        return next();
      } catch (e) {
        console.log('Erro model: ', e.toString());
      }
    },

    setCademi: async function (next) {
      try {
        const enrolment = this?.enrolment ? this?.enrolment?.toObject() : null;
        if (!enrolment) {
          return next();
        }

        if (
          enrolment?.registryCourse?.course?.useCademi &&
          enrolment?.pillars.find(
            (p) => p.name === 'Financeiro' && p.isCompleted === true,
          )
        ) {
          const models = _getModels.call(this, 'Enrolments');
          const { CademiIntegration, Students } = models;

          const hasSend = enrolment?.metadata?.hasSendCademi
            ? true
            : await CademiIntegration.findOne({
              _enrolmentId: enrolment._id,
              success: true,
            });

          if (hasSend) {
            Object.assign(this.enrolment, {
              metadata: { hasSendCademi: true },
            });
            return next();
          }

          const student = await Students.aggregate([
            {
              $match: {
                cpf: enrolment.cpf,
              },
            },
            {
              $lookup: {
                from: 'Users',
                localField: '_userId',
                foreignField: '_id',
                as: 'users',
              },
            },
            {
              $project: {
                name: 1,
                email: {
                  $first: '$users.email',
                },
                cellPhone: 1,
              },
            },
          ]);

          if (student.length) {
            const body = {
              isCademi: 'cademi',
              name: student[0].name,
              email: student[0].email,
              cellphone:
                student[0].cellPhone ||
                student[0].phone ||
                student[0].whatsApp ||
                '+553399999999',
              product: JSON.stringify(
                enrolment.registryCourse.course.codCademi,
              ),
            };

            await request.post('https://r82nushnh3.us-east-1.awsapprunner.com/cademi-circle/hook', {
              headers: {
                Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NWIwMDUxNjY1NGU4MGQyYTI2YzcxZDIiLCJ1c2VybmFtZSI6InBpbmNlbF9hdG9taWNvIiwiaWF0IjoxNzA2MDM0NDU1fQ.62DfpzfuX208RRmblolDJO3dJOhMB7kDdSpj-Wuv6Nk',
                'Content-Type': 'application/json',
              },
              body: body,
              json: true,
            }).then(async result => {

              await CademiIntegration.insertOne({
                _enrolmentId: enrolment._id,
                _idIntegration: result._id,
                _cpf: this.enrolment.cpf,
                success: true,
                response: result,
                body: body,
              });
              Object.assign(this.enrolment, {metadata: {hasSendCademi: true}});
            }).catch(async error => {
              await CademiIntegration.insertOne({
                _enrolmentId: enrolment._id,
                _cpf: enrolment.cpf,
                success: false,
                erroResponse: JSON.stringify(error),
                response: JSON.stringify(error),
                body: body,
              });
              return request({
                method: 'POST',
                json: true,
                uri: 'https://discord.com/api/webhooks/1395476016247341159/Pa-ayD7DYiDj2OziiAaoDADSt3NnVcjDwvB47MpHEc3NSs5whUwrnPxtzZ2guJrpDPK6',
                body: {
                  username: 'cronBot',
                  avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
                  content: `${enrolment._id} - Erro ` + JSON.stringify(error),
                },
              });
            });
            return next();
          }
        }

        return next();
      } catch (e) {
        return request({
          method: 'POST',
          json: true,
          uri: 'https://discord.com/api/webhooks/1395476016247341159/Pa-ayD7DYiDj2OziiAaoDADSt3NnVcjDwvB47MpHEc3NSs5whUwrnPxtzZ2guJrpDPK6',
          body: {
            username: 'cronBot',
            avatar_url:
              'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
            content:
              ' Erro ' +
              JSON.stringify(e) +
              JSON.stringify(this?.enrolment?._doc || this?.enrolment),
          },
        });
      }
    },

    verifyStatus: async function (next) {
      try {
        if (!(this.enrolment && this.enrolment.cpf)) {
          return next();
        }

        const models = _getModels.call(this, 'Enrolments');
        const { Enrolments } = models;
        const objEnrolment = this.enrolment;

        const objFind = {
          cpf: objEnrolment.cpf,
          status: {
            $nin: [
              'canceled',
              'locked',
              'changed',
              'expired',
              'rejected_by_deferral',
              'blocked_by_reenrolment',
              'rehabilitation',
            ],
          },
          'registryCourse.status': {
            $nin: ['rejected_by_deferral', 'canceled'],
          },
          'registryCourse.course.acronym':
            objEnrolment.registryCourse.course.acronym,
        };

        if (objEnrolment._id) {
          Object.assign(objFind, {
            _id: { $ne: utilsFunctionToObjectId.convert(objEnrolment._id) },
          });
        }

        const enrolment = await Enrolments.findOne(objFind).limit(1);

        if (enrolment) {
          // const erro = new Error("Aluno ja matriculado nessa situação.");
          // Object.assign(erro, {status: 400})
          return next(new Error('Estudante já matriculado neste curso'));
        }

        return next();
      } catch (e) {
        console.log('Erro model: ', e.toString());
      }
    },

    generateConclusionRequest: async function (enrolment, next) {
      if (enrolment.registryCourse.dateEnd) return false;

      if (enrolment && enrolment.pillars && Array.isArray(enrolment.pillars)) {
        try {
          // if (!enrolment.registryCourse.course.class) return next();

          const isAllPillarCompleted = enrolment.pillars.findIndex(
            (pillar) => !pillar.isCompleted,
          );

          if (isAllPillarCompleted > -1) return false;

          const models = _getModels.call(this, 'Enrolments');
          const { ConclusionSolicitations, Students } = models;

          const hasPreviousConclusionSolicitation =
            await ConclusionSolicitations.findOne({
              'enrolment._id': enrolment._id,
              status: {
                $in: ['pending', 'approved'],
              },
            });

          if (hasPreviousConclusionSolicitation) {
            if (
              hasPreviousConclusionSolicitation.status === 'pending' &&
              enrolment.status === 'completed'
            ) {
              hasPreviousConclusionSolicitation.status = 'approved';
              await hasPreviousConclusionSolicitation.save();
            }

            return next();
          }

          const student = await Students.findOne({ cpf: enrolment.cpf });

          const solicitationData = {
            status: enrolment.status === 'completed' ? 'approved' : 'pending',
            enrolment: {
              _id: enrolment._id,
              createdAt: enrolment.createdAt,
            },
            student: {
              name: student.name,
              cpf: student.cpf,
              cellPhone: student.cellPhone,
              whatsApp: student.whatsApp,
            },
            course: {
              name: enrolment.registryCourse.course._name,
              typeName: enrolment.registryCourse.course._typeName,
              certifier: enrolment.registryCourse.course._certifierName,
            },
          };

          if (enrolment.registryCourse.course.class) {
            Object.assign(solicitationData, {
              class: {
                _id: enrolment.registryCourse.course.class._id,
                name: enrolment.registryCourse.course.class.name,
              },
            });
          }

          await ConclusionSolicitations.create(solicitationData);
        } catch (err) {
          console.error(err);
        }
      }

      return true;
    },

    setMinAndMaxDateToCompleteFieldsOnFindOneAndUpdate: async function (next) {
      try {
        const update = this.getUpdate();

        if (
          update?.$set['registryCourse.course.minDateToComplete'] ||
          update?.$set['registryCourse.course.maxDateToComplete'] ||
          update?.$set?.registryCourse?.course?.minDateToComplete ||
          update?.$set?.registryCourse?.course?.maxDateToComplete
        ) return next();

        const models = _getModels.call(this, 'Enrolments');
        const { Enrolments } = models;
        const enrolment = await Enrolments.findOne(this._id);

        setMinAndMaxDateToCompleteFields(enrolment, this);
      } catch (err) {
        console.error(err);
      } finally {
        next();
      }
    },

    setMinAndMaxDateToCompleteFieldsOnSave: async function (next) {
      try {
        if (
          this.isModified('registryCourse.course.minDateToComplete') ||
          this.isModified('registryCourse.course.maxDateToComplete')
        ) return next();

        const models = _getModels.call(this, 'Enrolments');
        const { Enrolments } = models;
        const enrolment = await Enrolments.findOne(this._id ?? this.getQuery());

        if (enrolment?.registryCourse?.course) {
          setMinAndMaxDateToCompleteFields(enrolment, this);
        }

      } catch (err) {
        console.error(err);
      } finally {
        next();
      }
    },
  },
  database: {
    collection: modelName,

    fields: {
      cpf: {
        type: String,
        required: false,
        index: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: false,
        index: true,
      },
      _studentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: function () {
          return !this.cpf;
        },
        index: true,
      },
      paymentDay: {
        type: Number,
        index: true,
        required: true,
      },
      paymentMonth: {
        type: Number,
        index: true,
        required: true,
      },
      creaditCardDueMonthly: {
        type: Date,
      },
      expiredAt: {
        type: Date,
        required: true,
      },
      enrolment: {
        installment: {
          type: Number,
          index: true,
          required: true,
        },
        amount: {
          type: Number,
          index: true,
          required: true,
        },
        dueDate: {
          type: Date,
        },
        interest: {
          // Juros da parcela
          type: Number,
          default: 0,
        },
        scholarshipPercent: {
          // Bolsista desconto sobre a taxa de inscrição
          type: Number,
          default: 0,
        },
        voucher: {
          code: String,
          amountType: {
            type: String,
            enum: ['percentage', 'value'],
          },
          amount: Number,
        },
        isPaidToPartner: {
          type: Boolean,
          default: false,
        },
        notUsedRateEnrolment: {
          type: Boolean,
          default: false,
        },
      },
      indication: {
        type: [
          {
            _id: false,
            level: {
              type: String,
              enum: ['master', 'level1', 'level2', 'indicator'],
              required: true,
            },
            cpf: {
              type: String,
              required: true,
            },
            userType: {
              type: String,
              required: true,
            },
            commissionMonthly: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionEnrolment: {
              type: Number,
              // required: false,
              default: 0,
            },
            commissionType: {
              type: String,
              // required: false,
              default: 'percentage',
              valid: ['value', 'percentage'],
            },
            polo: {
              type: {
                _id: {
                  type: mongoose.SchemaTypes.ObjectId,
                  // required: false,
                },
                name: {
                  type: String,
                  // required: false,
                },
              },
              // required: false
            },
            commissionMonthlyExceptions: {
              type: [
                {
                  _id: false,
                  installment: {
                    type: Number,
                  },
                  commission: {
                    type: Number,
                  },
                },
              ],
              // required: false,
              default: undefined,
            },
          },
        ],
        // required: false,
        default: [],
      },
      contractStorageURL: {
        type: String,
      },
      templateContract: {
        type: String,
      },
      contract: {
        type: String,
      },
      geolocation: {
        ip: {
          type: String,
          // required: false,
          default: '127.0.0.1',
        },
        coords: {
          latitude: Number,
          longitude: Number,
        },
      },
      metadata: mongoose.SchemaTypes.Mixed,
      pillars: [
        {
          _id: false,
          name: {
            type: String,
            required: true,
          },
          isCompleted: {
            type: Boolean,
            default: false,
          },
        },
      ],
      registryCourse: {
        courseAmount: {
          installment: {
            type: Number,
            index: true,
            required: true,
          },
          amount: {
            // Valor em centavos R$
            type: Number,
            index: true,
            required: true,
          },
          amountType: {
            type: String,
            enum: ['normal', 'promotion', 'manual'],
            default: 'normal',
          },
          _promotionId: mongoose.SchemaTypes.ObjectId,
          interest: {
            // Juros da parcela
            type: Number,
            default: 0,
          },
          scholarshipPercent: {
            // Bolsista desconto sobre o valor do curso
            type: Number,
            default: 0,
          },
          voucher: {
            code: String,
            amountType: {
              type: String,
              enum: ['percentage', 'value'],
            },
            amount: Number,
          },
        },
        course: {
          //Esse campo se faz necessário, devido ao reaproveitamento de disciplina, para que um aluno possa fazer uma disciplina que esteja vinculada a outra turma como no caso de reprovação, dessa forma as disciplinas que o mesmo terá acesso sera limitado as disciplinas da sua enrolment.
          class: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
              },
              name: {
                type: String,
                required: true,
              },
              closeDiary: {
                type: Boolean,
                required: true,
                default: false,
              },
              evaluationMethod: {
                type: String,
                enum: [
                  'average', // média
                  'bimonthly_average', // média bimestral
                  'modular_sum', // soma modular
                  'period_last_average', // período da última média
                  'quarterly_sum', // soma trimestral
                ],
                default: 'average',
              },
              reEnrolments: {
                type: [reEnrolmentSchema],
                // required: false
              },
            },
            // required: false,
            default: undefined,
          },
          polo: {
            type: {
              _id: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
              },
              name: {
                type: String,
                // required: false,
              },
            },
            // required: false
          },
          disciplines: [
            {
              discId: {
                type: mongoose.SchemaTypes.ObjectId,
              },
              hasReplace: {
                type: Boolean,
              },
              optionalDisciplineId: {
                type: mongoose.SchemaTypes.ObjectId,
              },
              isOnRecuperation: {
                type: Boolean,
                default: false,
              },
              grade: {
                type: Number,
                default: null,
              },
              gradeType: {
                type: String,
                enum: ['dispensation', 'exploitation', 'standard', 'manual'],
                default: 'standard',
              },
              status: {
                type: String,
                enum: [
                  'in_progress',
                  'approved',
                  'disapproved',
                  'recuperation',
                ],
                default: 'in_progress',
              },
              supportFile: {
                type: String,
                // required: false
              },
              period: {
                type: Number,
                // required: false
              },
              coursewareType: {
                type: String,
              },
              gradeHistory: [
                {
                  _userName: {
                    type: String,
                    required: true,
                  },
                  _userId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                  },
                  grade: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                    default: null,
                  },
                  gradeType: {
                    type: String,
                    enum: [
                      'dispensation',
                      'exploitation',
                      'standard',
                      'manual',
                    ],
                    required: true,
                    default: 'standard',
                  },
                  status: {
                    type: String,
                    enum: [
                      'in_progress',
                      'approved',
                      'disapproved',
                      'recuperation',
                    ],
                    default: 'in_progress',
                  },
                  reason: {
                    type: String,
                    // required: false,
                    default: null,
                  },
                  launchedAt: {
                    type: Date,
                    required: true,
                  },
                },
              ],
              _name: {
                type: String,
                required: true,
              },
              sagahDiscipline: {
                type: String,
              },
              sagahContextId: {
                type: String,
                // required: false,
                allowNull: true,
              },
              type: {
                type: String,
                enum: ['required', 'optional'],
              },
              description: {
                type: String,
                // required: false,
                default: '',
              },
              workload: {
                type: Number,
                // required: false,
                allowNull: true,
                default: null,
              },
              // As mensagens e conteudos irão ficar na tabela de forum
              forums: [
                {
                  forumId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                  },
                  title: {
                    type: String,
                    required: true,
                  },
                  type: {
                    type: String,
                    enum: [
                      'punctuated',
                      'informative',
                      'avaliativo',
                      'informativo',
                    ],
                    required: true,
                  },
                  // Nota máxima que pode ser alcançada
                  grade: {
                    type: Number,
                    default: null,
                  },
                },
              ],
              //Esse campo se faz necessário, devido ao reaproveitamento de disciplina, para que um aluno possa fazer uma disciplina que esteja vinculada a outra turma como no caso de reprovação, dessa forma as disciplinas que o mesmo terá acesso sera limitado as disciplinas da sua enrolment.
              directedStudies: [
                {
                  directedStudyId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false
                  },
                  title: {
                    type: String,
                    required: true,
                  },
                  type: {
                    type: String,
                    enum: [
                      'punctuated',
                      'informative',
                      'avaliativo',
                      'informativo',
                    ],
                    required: true,
                  },
                  // Nota máxima que pode ser alcançada
                  grade: {
                    type: Number,
                    default: null,
                  },
                },
              ],
              disciplineClass: {
                type: {
                  _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                  },
                  code: {
                    type: String,
                  },
                  name: {
                    type: String,
                  },
                  grouping: {
                    type: {
                      _id: {
                        //ID DO agrupmanento
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                      },
                      name: {
                        // nome do agrupamento
                        type: String,
                        required: true,
                      },
                      description: {
                        //Descrição do agrupamento
                        type: String,
                        required: true,
                      },
                    },
                  },
                },
              },
              activities: [
                {
                  type: {
                    type: String,
                    enum: ['regular', 'recuperation'],
                    required: true,
                  },
                  isManual: {
                    type: Boolean,
                  },
                  modality: {
                    type: String,
                    enum: ['online', 'presential'],
                    required: true,
                  },
                  maxDuration: {
                    type: Number,
                    // required: false
                  },
                  model: {
                    type: String,
                    enum: [
                      'form',
                      'evaluation',
                      'upload',
                      'participation',
                      'sagah',
                      'forum',
                      'pathKonwledge',
                      'directedStudy',
                    ],
                    required: true,
                  },
                  isFinalTest: {
                    type: Boolean,
                    required: true,
                  },
                  modelMeta: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                      // default: mongoose.Types.ObjectId()
                    },
                    _activitieGroupingId: {
                      type: mongoose.SchemaTypes.ObjectId,
                      // required: false
                    },
                    numQuestions: {
                      type: Number,
                      // required: false
                    },
                    enunciation: {
                      type: String,
                      // required: false
                    },
                    ltiUrl: {
                      type: String,
                      // required: false
                    },
                    ltiTitle: {
                      type: String,
                      // required: false
                    },
                    activityName: {
                      type: String,
                      // required: false
                    },
                  },
                  attempts: {
                    type: Number,
                    // required: false,
                    default: 0,
                  },
                  date: {
                    type: Date,
                    // required: false,
                    default: null,
                  },
                  timeSpent: {
                    type: Number,
                    // required: false,
                    default: 0,
                  },
                  evaluation: {
                    _coursewareEvaluationId: {
                      type: mongoose.SchemaTypes.ObjectId,
                      // required: false
                    },
                    questions: [
                      {
                        _id: false,
                        _questionId: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _response: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _correct: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _pieceId: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _fixResponse: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                      },
                    ],
                  },
                  upload: {
                    archive: {
                      type: String,
                      // required: false,
                      default: null,
                    },
                    requestNewRevision: {
                      type: Boolean,
                      // required: false,
                      default: true,
                    },
                    revisions: [
                      {
                        _id: false,
                        _userName: String,
                        _userId: mongoose.SchemaTypes.ObjectId,
                        archive: {
                          type: String,
                          required: true,
                        },
                        date: {
                          type: Date,
                          required: true,
                        },
                      },
                    ],
                  },
                  grade: {
                    type: Number,
                    default: null,
                  },
                  gradeType: {
                    type: String,
                    enum: ['standard', 'manual'],
                    required: true,
                    default: 'standard',
                  },
                  maxGrade: {
                    type: Number,
                    required: true,
                    default: 10,
                  },
                  gradeHistory: [
                    {
                      _userName: {
                        type: String,
                        required: true,
                      },
                      _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                      },
                      grade: {
                        type: Number,
                        // required: false,
                        allowNull: true,
                        default: null,
                      },
                      reason: {
                        type: String,
                        // required: false,
                        default: null,
                      },
                      gradeType: {
                        type: String,
                        enum: ['standard', 'manual'],
                        required: true,
                        default: 'standard',
                      },
                      launchedAt: {
                        type: Date,
                        required: true,
                      },
                    },
                  ],
                  dateStart: {
                    type: Date,
                    // required: false,
                  },
                  dateEnd: {
                    type: Date,
                    // required: false,
                  },
                  activityType: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    title: String,
                    selectedActivity: String,
                  },
                  chapter: {
                    type: [
                      {
                        _id: false,
                        coursewareId: mongoose.SchemaTypes.ObjectId,
                        coursewareName: String,
                        number: Number,
                      },
                    ],
                  },
                  forumId: {
                    type: mongoose.SchemaTypes.ObjectId,
                  },
                  directedStudyId: {
                    type: mongoose.SchemaTypes.ObjectId,
                  },
                },
              ],
              chapter: {
                type: [
                  {
                    _id: false,
                    coursewareId: mongoose.SchemaTypes.ObjectId,
                    coursewareName: String,
                    number: Number,
                  },
                ],
              },
              _coursewares: {
                type: [mongoose.SchemaTypes.ObjectId],
                // required: false
              },
              releaseDisciplines: {
                type: {
                  releaseType: String,
                  isAutomatic: Boolean,
                },
              },
              isDependecy: {
                type: Boolean,
              },
              dependecyAttempts: {
                type: Number,
              },
              presence: {
                type: {
                  lessons: {
                    type: Number,
                    required: true,
                  },
                  present: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  absences: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  justified: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  presence: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  approved: {
                    type: Boolean,
                    required: true,
                    default: false,
                  },
                },
                // required: false
              },
              extraCoursewares: {
                type: [extraCoursewaresSchema],
                default: [],
              },
              dependecyConfig: {
                type: {
                  useDependecy: Boolean,
                  activities: [
                    {
                      type: {
                        type: String,
                        enum: ['regular', 'recuperation'],
                        // required: false
                      },
                      modality: {
                        type: String,
                        enum: ['online', 'presential'],
                        // required: false
                      },
                      maxDuration: {
                        type: Number,
                        // required: false
                      },
                      model: {
                        type: String,
                        enum: [
                          'form',
                          'evaluation',
                          'upload',
                          'participation',
                          'sagah',
                          'forum',
                        ],
                        // required: false
                      },
                      isFinalTest: {
                        type: Boolean,
                        // required: false
                      },
                      chapter: {
                        type: [
                          {
                            _id: false,
                            coursewareId: mongoose.SchemaTypes.ObjectId,
                            coursewareName: String,
                            number: Number,
                          },
                        ],
                      },
                      modelMeta: {
                        _id: {
                          type: mongoose.SchemaTypes.ObjectId,
                        },
                        numQuestions: {
                          type: Number,
                          // required: false
                        },
                        enunciation: {
                          type: String,
                          // required: false
                        },
                        ltiUrl: {
                          type: String,
                          // required: false
                        },
                        ltiTitle: {
                          type: String,
                          // required: false
                        },
                      },
                      maxGrade: {
                        type: Number,
                        // required: false,
                        default: 10,
                      },
                    },
                  ],
                  useRate: Boolean,
                  rateAmount: Number,
                  period: Number,
                },
              },
            },
          ],
          optionalDisciplines: [
            {
              discId: {
                type: mongoose.SchemaTypes.ObjectId,
              },
              isOnRecuperation: {
                type: Boolean,
                default: false,
              },
              grade: {
                type: Number,
                default: null,
              },
              gradeType: {
                type: String,
                enum: ['dispensation', 'exploitation', 'standard', 'manual'],
                default: 'standard',
              },
              status: {
                type: String,
                enum: [
                  'in_progress',
                  'approved',
                  'disapproved',
                  'recuperation',
                ],
                default: 'in_progress',
              },
              supportFile: {
                type: String,
                // required: false
              },
              period: {
                type: Number,
                // required: false
              },
              coursewareType: {
                type: String,
              },
              gradeHistory: [
                {
                  _userName: {
                    type: String,
                    required: true,
                  },
                  _userId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                  },
                  grade: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                    default: null,
                  },
                  gradeType: {
                    type: String,
                    enum: [
                      'dispensation',
                      'exploitation',
                      'standard',
                      'manual',
                    ],
                    required: true,
                    default: 'standard',
                  },
                  status: {
                    type: String,
                    enum: [
                      'in_progress',
                      'approved',
                      'disapproved',
                      'recuperation',
                    ],
                    default: 'in_progress',
                  },
                  reason: {
                    type: String,
                    // required: false,
                    default: null,
                  },
                  launchedAt: {
                    type: Date,
                    required: true,
                  },
                },
              ],
              _name: {
                type: String,
                required: true,
              },
              name: {
                type: String,
              },
              sagahDiscipline: {
                type: String,
              },
              sagahContextId: {
                type: String,
                // required: false,
                allowNull: true,
              },
              type: {
                type: String,
                enum: ['required', 'optional'],
              },
              description: {
                type: String,
                required: true,
              },
              workload: {
                type: Number,
                // required: false,
                allowNull: true,
                default: null,
              },
              // As mensagens e conteudos irão ficar na tabela de forum
              forums: [
                {
                  forumId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                  },
                  title: {
                    type: String,
                    required: true,
                  },
                  type: {
                    type: String,
                    enum: [
                      'punctuated',
                      'informative',
                      'avaliativo',
                      'informativo',
                    ],
                    required: true,
                  },
                  // Nota máxima que pode ser alcançada
                  grade: {
                    type: Number,
                    default: null,
                  },
                },
              ],
              //Esse campo se faz necessário, devido ao reaproveitamento de disciplina, para que um aluno possa fazer uma disciplina que esteja vinculada a outra turma como no caso de reprovação, dessa forma as disciplinas que o mesmo terá acesso sera limitado as disciplinas da sua enrolment.
              directedStudies: [
                {
                  directedStudyId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    // required: false
                  },
                  title: {
                    type: String,
                    // required: false
                  },
                  type: {
                    type: String,
                    enum: [
                      'punctuated',
                      'informative',
                      'avaliativo',
                      'informativo',
                    ],
                    required: true,
                  },
                  // Nota máxima que pode ser alcançada
                  grade: {
                    type: Number,
                    default: null,
                  },
                },
              ],
              disciplineClass: {
                type: {
                  _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                  },
                  code: {
                    type: String,
                    // required: false
                  },
                  name: {
                    type: String,
                  },
                  grouping: {
                    type: {
                      _id: {
                        //ID DO agrupmanento
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                      },
                      name: {
                        // nome do agrupamento
                        type: String,
                        required: true,
                      },
                      description: {
                        //Descrição do agrupamento
                        type: String,
                        required: true,
                      },
                    },
                    // required: false
                  },
                },
              },
              activities: [
                {
                  activityType: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    title: String,
                    selectedActivity: String,
                  },
                  type: {
                    type: String,
                    enum: ['regular', 'recuperation'],
                    required: true,
                  },
                  modality: {
                    type: String,
                    enum: ['online', 'presential'],
                    required: true,
                  },
                  maxDuration: {
                    type: Number,
                    // required: false
                  },
                  model: {
                    type: String,
                    enum: [
                      'form',
                      'evaluation',
                      'upload',
                      'participation',
                      'sagah',
                      'forum',
                      'pathKonwledge',
                      'directedStudy',
                    ],
                    required: true,
                  },
                  isFinalTest: {
                    type: Boolean,
                    required: true,
                  },
                  modelMeta: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                      // default: mongoose.Types.ObjectId()
                    },
                    numQuestions: {
                      type: Number,
                      // required: false
                    },
                    enunciation: {
                      type: String,
                      // required: false
                    },
                    ltiUrl: {
                      type: String,
                      // required: false
                    },
                    ltiTitle: {
                      type: String,
                      // required: false
                    },
                    activityName: {
                      type: String,
                      // required: false
                    },
                  },
                  attempts: {
                    type: Number,
                    // required: false,
                    default: 0,
                  },
                  date: {
                    type: Date,
                    // required: false,
                    default: null,
                  },
                  timeSpent: {
                    type: Number,
                    // required: false,
                    default: 0,
                  },
                  evaluation: {
                    _coursewareEvaluationId: {
                      type: mongoose.SchemaTypes.ObjectId,
                      // required: false
                    },
                    questions: [
                      {
                        _id: false,
                        _questionId: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _response: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _correct: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _pieceId: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                        _fixResponse: {
                          type: mongoose.SchemaTypes.ObjectId,
                          // required: false
                        },
                      },
                    ],
                  },
                  upload: {
                    archive: {
                      type: String,
                      // required: false,
                      default: null,
                    },
                    requestNewRevision: {
                      type: Boolean,
                      // required: false,
                      default: true,
                    },
                    revisions: [
                      {
                        _id: false,
                        _userName: String,
                        _userId: mongoose.SchemaTypes.ObjectId,
                        archive: {
                          type: String,
                          required: true,
                        },
                        date: {
                          type: Date,
                          required: true,
                        },
                      },
                    ],
                  },
                  grade: {
                    type: Number,
                    default: null,
                  },
                  gradeType: {
                    type: String,
                    enum: ['standard', 'manual'],
                    required: true,
                    default: 'standard',
                  },
                  maxGrade: {
                    type: Number,
                    required: true,
                    default: 10,
                  },
                  gradeHistory: [
                    {
                      _userName: {
                        type: String,
                        required: true,
                      },
                      _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                      },
                      grade: {
                        type: Number,
                        // required: false,
                        allowNull: true,
                        default: null,
                      },
                      reason: {
                        type: String,
                        // required: false,
                        default: null,
                      },
                      gradeType: {
                        type: String,
                        enum: ['standard', 'manual'],
                        required: true,
                        default: 'standard',
                      },
                      launchedAt: {
                        type: Date,
                        required: true,
                      },
                    },
                  ],
                  dateStart: {
                    type: Date,
                    // required: false,
                  },
                  dateEnd: {
                    type: Date,
                    // required: false,
                  },
                },
              ],
              _coursewares: {
                type: [mongoose.SchemaTypes.ObjectId],
                // required: false
              },
              releaseDisciplines: {
                type: {
                  releaseType: String,
                  isAutomatic: Boolean,
                },
              },
              presence: {
                type: {
                  lessons: {
                    type: Number,
                    required: true,
                  },
                  present: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  absences: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  justified: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  presence: {
                    type: Number,
                    required: true,
                    default: 0,
                  },
                  approved: {
                    type: Boolean,
                    required: true,
                    default: false,
                  },
                },
                // required: false
              },
            },
          ],
          hasCurriculumMatrix: {
            type: Boolean,
          },
          hasUpdateClass: {
            type: Boolean,
          },
          curriculumMatrix: {
            _id: mongoose.SchemaTypes.ObjectId,
            code: String,
            hoursComplementaryActivity: Number,
            internshipWorkload: Number,
            quantityOptionalDisciplines: Number,
            workloadOptionalDisciplines: Number,
            internships: [
              {
                schedulingSent: {
                  type: Boolean,
                },
                shipmentType: {
                  type: String,
                },
                shipmentTypeQuantity: {
                  type: mongoose.SchemaTypes.Mixed,
                },
                shipmentQuantity: {
                  type: Number,
                },
                workload: {
                  type: Number,
                },
                helpFile: {
                  type: String,
                },
                fileModelsInternshipRequired: {
                  type: [
                    {
                      url: String,
                      name: String,
                      typeDocument: String,
                    },
                  ],
                  // required: false
                },
                stepsInternshipRequired: {
                  type: [
                    {
                      isRequired: Boolean,
                      typeInternship: String,
                      title: String,
                      action: String,
                      description: String,
                      nextStatus: String,
                      reviewBy: String,
                      visibleIn: String,
                      originalFile: String,
                      internalAnalisy: Boolean,
                      limitCorrect: Number,
                      typeLimitCorrect: String,
                      dateLimit: Date,
                      order: Number,
                      multiFile: Boolean,
                    },
                  ],
                  // required: false
                },
                area: {
                  type: [
                    {
                      _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                      },
                      name: {
                        type: String,
                        required: true,
                      },
                    },
                  ],
                  // required: false
                },
                tutor: {
                  type: {
                    _id: {
                      type: mongoose.SchemaTypes.ObjectId,
                      required: true,
                    },
                    name: {
                      type: String,
                      required: true,
                    },
                  },
                  // required: false
                },
                title: String,
                _enrolmentInternshipId: mongoose.SchemaTypes.ObjectId,
              },
            ],
          },
          _courseId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
            allowNull: true,
          },
          _name: {
            type: String,
            required: true,
          },
          _typeName: {
            type: String,
            required: true,
          },
          _subCategory: {
            type: String,
            // required: false,
            default: null,
          },
          _certifierName: {
            type: String,
            required: true,
          },
          _knowledgeAreaName: {
            type: String,
            required: true,
          },
          _categoryName: {
            type: String,
            // required: false,
            default: null,
          },
          _areaNames: {
            type: [String],
            required: true,
          },
          acronym: {
            type: String,
            required: true,
          },
          useCademi: {
            type: Boolean,
            default: false,
          },
          codCademi: {
            type: Number,
            default: null,
          },
          workload: {
            type: Number,
            // required: false,
            allowNull: true,
          },
          evaluationMethod: {
            type: String,
            enum: [
              'average', // média
              'modular_sum', // soma modular
            ],
            default: 'average',
          },
          linkEMec: {
            type: String,
            required: true,
          },
          approvalPercentage: {
            type: Number,
            required: true,
          },
          minimalFrequency: {
            type: Number,
            required: true,
          },
          description: {
            type: String,
            required: true,
          },
          isTccRequired: {
            type: Boolean,
            // required: false
          },
          isInternshipRequired: {
            type: Boolean,
            // required: false
          },
          internshipWorkload: {
            type: Number,
            // required: false
          },
          optionalWorkload: {
            type: Number,
            // required: false
          },
          internshipRequiredWorkload: {
            type: Number,
            // required: false
          },
          hoursComplementaryActivity: {
            type: Number,
            // required: false
          },
          hoursComplementaryActivityRequired: {
            type: Number,
            // required: false
          },
          approvedLevelsHoursComplementary: {
            type: [String],
            // required: false
          },
          isRequiredHoursComplementaryActivity: {
            type: Boolean,
            // required: false
          },
          additionalWorkload: {
            type: Number,
            // required: false
          },
          minMonthsToComplete: {
            type: Number,
            required: true,
          },
          maxMonthsToComplete: {
            type: Number,
            // required: false
          },
          minDateToComplete: {
            type: Date,
          },
          maxDateToComplete: {
            type: Date,
          },
          quantityOptionalDisciplines: {
            type: Number,
            // required: false
          },
          totalMinutesPresential: {
            type: Number,
            // required: false
          },
        },
        dateStart: Date,
        dateEnd: Date,
        dateGraduation: Date,
        status: {
          type: String,
          enum: [
            'waiting_confirm', // Aguardando confirmação (Não pagou a taxa de inscrição)
            'matriculate', // Matriculado (Pagou a taxa de inscrição)
            'completed', // Completo (Efetuou todas as atividades avaliativas e aguarda emissão do certificado)
            'awaiting_approval_by_deferral',
            'rejected_by_deferral',
            'canceled',
          ],
          default: 'waiting_confirm',
          index: true,
          required: true,
        },
        certificateCredits: {
          type: Number,
          default: 0,
          // required: false
        },
        creditUsed: {
          type: [
            {
              _certificateId: mongoose.SchemaTypes.ObjectId,
              discipline: {
                type: String,
                // required: false
              },
            },
          ],
          // required: false
        },
      },
      status: {
        type: String,
        enum: [
          'blocked', // Bloqueada (Quando e realizada pelo parceiro que ainda não recebeu a taxa de inscrição)
          'in_progress', // Em progresso (Taxa de inscrição paga e curso em andamento)
          'canceled', // Cancelada (Curso cancelado)
          'changed', // Trocada (Curso foi trocado por outro curso e aproveitou pagamento)
          'locked', // Trancada (Curso trancado)
          'completed', // Completa (Certificado emitido),
          'awaiting_approval_by_deferral', // Aguardando deferimento
          'rejected_by_deferral', // Rejeitado no deferimento
          'expired', // Aluno Bloqueado por tempo sem acessar o AVA
          'rehabilitation', // Aluno bloqueado por tempo máximo de curso excedido
          'blocked_by_reenrolment', // Aluno bloqueado por rematricula nao aceita ou por passar o tempo de aceita-la
          'disapproved', // Aluno Reprovado
        ],
        default: 'in_progress',
        required: true,
      },
      requestedCertificates: {
        type: String,
        // required: false,
        enum: [
          'digital', // Solicitou o certificado online
          'print', // Solicitou o certificado impresso
          'all', // Solicitou ambos os certificados
        ],
      },
      financialResponsibleSet: {
        type: Boolean,
        default: false,
      },
      _studentFinancialResponsibleId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: false,
      },
      contractSigned: {
        type: Boolean,
        default: false,
        // required: false
      },
      contractSignedAt: {
        type: Date,
        // required: false
      },
      // Responsavel financeiro
      convertLead: {
        type: Boolean,
        // required: false
      },
      advanceInstallmentsRate: [
        {
          installment: Number,
          value: Number,
        },
      ],
      comboId: {
        type: String,
      },
      discounts: {
        type: [
          {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            chargeType: String,
            discountType: String,
            value: Number,
            days: Number,
            useInOverdueCharge: Boolean,
            discountRule: {
              type: String,
              enum: ['not_applied', 'antecipation', 'fixed_date'],
            },
            observation: String,
          },
        ],
        // required: false
      },
      scholarships: {
        type: [
          {
            _id: mongoose.SchemaTypes.ObjectId,
            name: String,
            chargeType: String,
            discountType: String,
            value: Number,
            useInOverdueCharge: Boolean,
          },
        ],
      },
      lastAccess: {
        type: Date,
        // required: false,
        allowNull: true,
        default: null,
      },
      enrolmentNumber: {
        type: {
          number: {
            type: String,
            //                         required: false,
            allowNull: true,
            //index: true
          },
          config: {
            type: {
              autoIncrement: {
                type: Boolean,
                // required: false,
                allowNull: true,
              },
              certifier: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                    //index: true
                  },
                },
                // required: false,
                allowNull: true,
              },
              course: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                },
                // required: false,
                allowNull: true,
              },
              courseType: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                    //index: true
                  },
                },
                // required: false,
                allowNull: true,
              },
              enrolment: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                },
                // required: false,
                allowNull: true,
              },
              semester: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                },
                // required: false,
                allowNull: true,
              },
              year: {
                type: {
                  enable: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  length: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  full: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  order: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                  restart: {
                    type: Boolean,
                    // required: false,
                    allowNull: true,
                  },
                  value: {
                    type: Number,
                    // required: false,
                    allowNull: true,
                  },
                },
                // required: false,
                allowNull: true,
              },
            },
            // required: false,
            allowNull: false,
          },
        },
        // required: false,
        allowNull: true,
      },
      name: {
        type: String,
        required: false,
      },
      email: {
        type: String,
        required: false,
        lowercase: true,
      },
      contendtechData: {
        _id: {
          type: mongoose.SchemaTypes.ObjectId,
        },
        course: {
          type: Number,
        },
        enrolmentIdContendtech: {
          type: Number,
        },
      },
      academicAssessments: {
        type: [
          {
            _id: mongoose.SchemaTypes.ObjectId,
            assessments: String,
            year: Number,
            aplicationDate: Date,
            date: Date,
          },
        ],
      },
      terms: {
        type: {
          date: Date,
          signedDocument: String,
          isSigned: Boolean,
        },
      },
      documents: {
        type: [
          {
            _id: false,
            _documentTypeId: {
              type: mongoose.SchemaTypes.ObjectId,
              required: true,
            },
            name: {
              type: String,
              required: true,
              index: true,
            },
            isRequired: {
              type: Boolean,
              required: true,
            },
            createdAt: {
              type: Date,
              required: false,
            },
            updatedAt: {
              type: Date,
              required: false,
            },
            // É ATUALIZADO NO HOOK "updateEnrolments" NA MODELAGEM DE DOCUMENTS
            status: {
              type: String,
              required: true,
              index: true,
            },
            // É ATUALIZADO NO HOOK "updateEnrolments" NA MODELAGEM DE DOCUMENTS
            _studentDocumentId: {
              type: mongoose.SchemaTypes.ObjectId,
            },
          },
        ],
        // required: true, // TODO: MUDAR PRA REQUIRED E HABILITAR VALIDATE POSTERIORMENTE DPS QUE ESTIVER TUDO OK
      },
      discountByDispensationSolicitation: {
        type: {
          percentByDispensation: Number,
          valueOfDiscount: Number,
          _dispensationId: mongoose.SchemaTypes.ObjectId,
          _chargeId: mongoose.SchemaTypes.ObjectId,
        },
      },
    },
    indexes: [
      {
        fields: {
          'enrolmentNumber.number': 1,
        },
        options: {
          unique: 'number_already_exists',
          name: 'enrolmentNumber_1',
          partialFilterExpression: {
            'enrolmentNumber.number': {
              $type: 'string',
            },
          },
        },
      },
      {
        fields: {
          'enrolmentNumber.config.year.value': 1,
          'enrolmentNumber.config.semester.value': 1,
          'enrolmentNumber.config.certifier.value': 1,
          'enrolmentNumber.config.courseType.value': 1,
          'enrolmentNumber.config.course.value': 1,
        },
        options: {
          name: 'enrolmentNumber_2',
        },
      },
      {
        fields: { 'enrolmentNumber.config.year.value': 1 },
        options: { name: 'enrolmentNumberYear' },
      },
      {
        fields: { 'enrolmentNumber.config.semester.value': 1 },
        options: { name: 'enrolmentNumberSemester' },
      },
      {
        fields: { 'enrolmentNumber.config.certifier.value': 1 },
        options: { name: 'enrolmentNumberCertifier' },
      },
      {
        fields: { 'enrolmentNumber.config.courseType.value': 1 },
        options: { name: 'enrolmentNumberCourseType' },
      },
      {
        fields: { 'enrolmentNumber.config.course.value': 1 },
        options: { name: 'enrolmentNumberCourse' },
      },
      { fields: { lastAccess: 1 }, options: { name: 'lastAccess' } },
    ],
  },
};
Enrolments.database.methods = {
  setNumber: Enrolments.functions.setNumber,
};
Enrolments.database.documentMethods = {
  toAsyncObject: Enrolments.functions.toAsyncObject,
  getClassId: Enrolments.functions.getClassId,
};
Enrolments.database.schemas = {
  reEnrolments: Enrolments.schemas.reEnrolment,
};
Enrolments.database.pre = {
  save: [
    Enrolments.functions.convertClassId,
    Enrolments.functions.verifyStatus,
    Enrolments.functions.setCademi,
    // Enrolments.functions.setMinAndMaxDateToCompleteFieldsOnSave,
  ],
  findOneAndUpdate: [
    Enrolments.functions.convertClassId,
    Enrolments.functions.verifyStatus,
    Enrolments.functions.setCademi,
    // A função foi comentada devido a alguns erros que estão acontecendo ao ser chamada campos são setados pela cron: set_enrolments_min_max_dates
    // Enrolments.functions.setMinAndMaxDateToCompleteFieldsOnFindOneAndUpdate,
  ],
};

module.exports = Enrolments;
