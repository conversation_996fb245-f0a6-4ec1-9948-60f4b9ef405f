let DocumentTypes = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'DocumentTypes',
        fields    : {
            name           : {
                type    : String,
                index   : true,
                required: true
            },
            tags           : {
                type    : [ String ],
                required: true,
                index   : true
            },
            isActive       : {
                type    : Boolean,
                // required: false,
                default : true
            },
            isAuthenticatedByCertifiers: {
                type: [String],
                // required: false,
                default: []
            },
            isRequiredFor:{
                type: {
                    genre: {
                        type: String,
                        enum: ['ALL','M','F'],
                        // required: false,
                        default: 'ALL'
                    }
                },
                // required: false
            },
            diplomaDoc: {
                type: String,
                enum: [
                    'AtoNaturalizacao',
                    'CertidaoCasamento',
                    'CertidaoNascimento',
                    'ComprovacaoEstagioCurricular',
                    'DocumentoIdentidadeDoAluno',
                    'HistoricoEscolar',
                    'ProvaColacao',
                    'ProvaConclusaoEnsinoMedio',
                    'TermoResponsabilidade',
                    'TituloEleitor',
                    'Outros',
                ],
                // required: false,
            },
            icon: {
                type: String,
                // required: false
            }
        },
        // Optional
        options   : {
            timestamp: true
        }
    }
};

module.exports = DocumentTypes;
