 
 
const mongoose = require('mongoose');
let EnrolmentAdditionalWorkloads = {
  database: {
    collection: 'EnrolmentAdditionalWorkloads',
    fields: {
      cpf: {
        type: String,
        required: true,
      },
      studentRegistrationCode: {
        type     : Number,
        required : false
      },
      class: {
        type: {
            _id: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            name: {
                type: String,
                required: true
            },
            closeDiary: {
                type: Boolean,
                required: true,
                default: false
            },
            evaluationMethod: {
                type: String,
                enum: [
                    'average',             // média
                    'bimonthly_average',   // média bimestral
                    'modular_sum',         // soma modular
                    'period_last_average', // período da última média
                    'quarterly_sum'        // soma trimestral
                ],
                default: 'average'
            },
        },
        // required: false,
        default: undefined
      },
      course: {
        certifier: {
          type: String,
          required: true,
        },
        type: {
          type: String,
          required: true,
        },
        areas: {
          type: [String],
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        workload: {
          type: Number,
          required: true,
        },
      },
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true
      },
      level: {
        type: String,
      },
      typeAdditionalWorkload: {
        type: {
            _id  : mongoose.SchemaTypes.ObjectId,
            title: String,
        },
        required: true
      },
      description: {
        type    : String,
        // required: false,
        default : null
      },
      status: {
        type: String,
        enum: ["waiting_approved", "in_development", "approved", "rejected"],
        // required: false,
        default: "waiting_approved",
      },
      requestNewRevision: {
        type: Boolean,
        // required: false,
        default: true,
      },
      workload: {
        type: Number,
        // required: false,
        default: null,
      },
      archive: {
        type: [String],
        required: true,
      },
      teacher: {
        type: String,
        // required: false,
      },
      solicitedWorkload: {
        type: Number
      },
      observation: {
        type: String,
        // required: false
      },
      workloadHistory: [
        {
          _id: false,
          workload: {
            type: Number,
            // required: false,
            default: null,
          },
          archive: {
            type: [String],
            // required: false,
          },
          launchedAt: {
            type: Date,
            // required: false,
          },
          _userName: {
            type: String,
            // required: false,
          },
          _userId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false,
          },
          description: {
            type: String,
            // required: false
          }
        },
      ],
      submitions: [
        {
          _id: false,
          date: {
            type: Date,
            required: true,
          },
          archive: {
            type: [String],
            required: true,
          },
          observation: {
            type: String,
            // required: false
          }
        },
      ],
      revisions: [
        {
          _id: false,
          number: {
            type: Number,
            required: true,
          },
          archive: {
            type: [String],
            required: true,
          },
          date: {
            type: Date,
            required: true,
          },
          comments: [
            {
              _id: false,
              content: {
                type: String,
                // required: false,
              },
              date: {
                type: Date,
                // required: false,
              },
              author: {
                type: {
                  type: String,
                  enum: ["teacher", "employer", "student"],
                  // required: false,
                },
                name: {
                  type: String,
                  // required: false,
                },
                _authorId: {
                  type: mongoose.SchemaTypes.ObjectId,
                  // required: false,
                },
              },
            },
          ],
        },
      ],
      polo: {
        type: {
            _id: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
            },
            name: {
                type: String,
                // required: false,
            },
        },
        // required: false
      },
    },
  }
};

module.exports = EnrolmentAdditionalWorkloads;