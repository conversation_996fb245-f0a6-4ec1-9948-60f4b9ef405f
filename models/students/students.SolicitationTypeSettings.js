const {SchemaTypes} = require("mongoose");
let SolicitationTypeSettings = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'SolicitationTypeSettings',
        fields    : {
            _solicitationTypeName: {
                type    : String,
                required: true
            },
            _certifierName : {
                type    : String,
                required: true
            },
            _courseTypeName: {
                type    : String,
                required: true
            },
            info: String,
            reasons: [
                {
                    name: String
                }
            ],
            price: {
                type: Number,
                default: null
            },
            descriptionRequired: {
                type: Boolean,
                default: false
            },
            metadata             : {
                limitDays: {
                    type    : String
                },
                freeAttempts: {
                    type: Number
                },
                maxAttempts: {
                    type: Number
                },
                extraData: SchemaTypes.Mixed
            },
            isDeclaration: {
                type: Boolean,
                default: false
            },
            isFree: {
                type: Boolean,
                default: false
            },
            isActive: {
                type: Boolean,
                default: false
            },
            icon: {
                type: String
            },
            hasApproval: {
                type: Boolean,
                default: false
            },
            quantityFree: {
                type: Number,
                default: 0
            },
            types: [String],
            block: {
              whenCompleted: Boolean,
              whenHaveIncompletePillar: Boolean,
              whenFirstMonthlyIsOpen: Boolean,
              whenRateEnrollmentIsOpen: Boolean,
              whenFinantialPillarIsIncomplete: Boolean,
              whenWaitingDeferral: Boolean,
              whenCertificateIsSolicited: Boolean,
              whenCertificateIsNotSolicited: Boolean,
              whenIsNotInProgress: Boolean,
              whenHaveClass: Boolean,
              whenNotHaveClass: Boolean,
            },
        }
    }
};

module.exports = SolicitationTypeSettings;
