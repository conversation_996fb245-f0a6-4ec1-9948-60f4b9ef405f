let SolicitationTypes = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'SolicitationTypes',
        fields    : {
            name: {
                type: String,
                required:true
            },
            alias: {
                type: String,
                required: true
            },
            isDeclaration: {
                type: Boolean,
                default: false
            },
            isActive: {
                type: Boolean,
                default: false
            }
        }
    }
};

module.exports = SolicitationTypes;