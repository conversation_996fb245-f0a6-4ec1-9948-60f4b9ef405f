const mongoose = require('mongoose');
const { _getModels, sendDiscord } = require('../../services/Utils');

const populateExternalFields = async (models, student, studentRef) => {
  const { Users } = models;
  const user = await Users.findById(student._userId);

  if (student.email !== user.email) {
    studentRef.set({
      email: user.email,
    });
  }
};

const Students = {
  functions: {
    populateExternalFieldsOnFindOneAndUpdate: async function (next) {
      try {
        const models = _getModels.call(this, 'Students');
        const { Students } = models;
        const student = await Students.findOne(this.getQuery());

        await populateExternalFields(models, student, this);
      } catch (err) {
        console.error(err);
      } finally {
        next();
      }
    },
    populateExternalFieldsOnSave: async function (next) {
      try {
        const models = _getModels.call(this, 'Students');
        const student = this;

        await populateExternalFields(models, student, this);
      } catch (err) {
        console.error(err);
      } finally {
        next();
      }
    },
    propagateFieldChanges: async function (student, next) {
      try {
        const models = _getModels.call(this, 'Students');
        const { Enrolments, Charges } = models;

        await Enrolments.updateMany(
          {
            cpf: student.cpf,
          },
          {
            $set: {
              name: student.name,
              email: student.email,
            },
          },
        );

        const enrolments = await Enrolments.find(
          {
            cpf: student.cpf,
          },
          {
            _id: 1,
          },
        );

        await Charges.updateMany(
          {
            _enrolmentId: {
              $in: enrolments.map((e) => e._id),
            },
          },
          {
            $set: {
              student: {
                name: student.name,
                email: student.email,
                cellPhone: student.cellPhone,
                cpf: student.cpf,
                address: student.address,
              },
            },
          },
        );
      } catch (err) {
        console.error(err);
      } finally {
        next();
      }
    },
    setRequiredDocumentsByGenre: async function (student, next) {
      try {
        if (!student.genre || !['M', 'F'].includes(student.genre.toUpperCase()))
          return next();

        student.genre = student.genre.toUpperCase();

        const models = _getModels.call(this, 'Students');

        const enrolments = await models.Enrolments.find(
          { cpf: student.cpf },
          { 'registryCourse.course.acronym': 1 },
        );
        const studentDocuments = await models.Documents.find(
          { cpf: student.cpf },
          { _documentTypeId: 1, status: 1 },
        );
        const studentCourses = await models.Courses.find(
          {
            acronym: {
              $in: enrolments.map(
                (enrolment) => enrolment.registryCourse.course.acronym,
              ),
            },
          },
          {
            acronym: 1,
            documents: 1,
          },
        );
        await (async () => {
          for (const enrolment of enrolments) {
            try {
              const course = studentCourses.find(
                (course) =>
                  course.acronym === enrolment.registryCourse.course.acronym,
              );
              if (!course) continue;

              const documentsNeeded = course.documents.filter(
                (documentNeeded) =>
                  documentNeeded.genreRequired.includes(student.genre),
              );

              const requiredDocuments = documentsNeeded.map(
                (documentNeeded) => {
                  const studentDocument = studentDocuments.find((doc) =>
                    doc._documentTypeId.equals(documentNeeded._documentTypeId),
                  );

                  return {
                    _documentTypeId: documentNeeded._documentTypeId,
                    name: documentNeeded.name,
                    isRequired: documentNeeded.isRequired,
                    status:
                      (studentDocument || {}).status
                        ? studentDocument.status
                        : 'pending',
                    _studentDocumentId: studentDocument
                      ? studentDocument._id
                      : undefined,
                  };
                },
              );

              enrolment.documents = requiredDocuments.filter(
                (_documentTypeId) => !!_documentTypeId,
              );
              await enrolment.save();
            } catch (err) {
              sendDiscord(
                'alerta-required-documents-by-genre',
                `
                    Erro ao tentar atualizar a enrolment.
                    Aluno: ${this.cpf} - Matrícula: ${enrolment._id},
                    erro: ${err.toString()}
                    Documentos Enrolment: ${JSON.stringify(enrolment.documents)}
                  `,
              );
            }
          }
        })();
      } catch (err) {
        console.error('o erro é aqui, setRequiredDocumentsByGenre', err);
      } finally {
        next();
      }
    },
    fixNumberPhone: function (next) {
      try {
        if (this.cellPhone) {
          this.cellPhone = this.cellPhone.replace(/\D/g, '');
        }
        next();
      } catch (e) {
        console.log('erro: ', e.toString());
      }
    },
    fixNumberCep: function (next) {
      try {
        if ((this.address || {}).zip) {
          this.address.zip = (this.address.zip || '')
            .trim()
            .normalize('NFD')
            .replace(/[^0-9a\s]+/g, '')
            .toString();
        }
        next();
      } catch (e) {
        console.log('erro: ', e.toString());
      }
    },
    fixName: function (next) {
      try {
        if (this.name) {
          const name = this.name
            .replace(/\.+$/, '')
            .split(' ')
            .map((word, index) => {
              const workdExceptions = [
                'o',
                'os',
                'a',
                'as',
                'um',
                'uns',
                'uma',
                'umas',
                'ao',
                'aos',
                'à',
                'às',
                'de',
                'do',
                'dos',
                'da',
                'das',
                'em',
                'no',
                'nos',
                'na',
                'nas',
                'numa',
                'por',
                'pelo',
                'pelos',
                'pela',
                'pelas',
                'para',
                'e',
                'PMI',
                'PMBOK',
                'RH',
                'I',
                'II',
                'III',
                'IV',
                'V',
                'VI',
                'VII',
                'VIII',
                'IX',
                'X',
                'SUS',
                'SUAS',
                'TGD',
                'BI',
                'PNL',
                'ISO',
                'PAD',
                'QSMS',
                'QSMS:',
                'IA',
                'S&OP',
                'NCPC',
              ];
              if (
                workdExceptions.includes(word) ||
                workdExceptions.includes((word || '').toLowerCase())
              )
                return (word || '').toLowerCase();

              const wordResult = word
                .replace(/(\r\n|\n|\r)/gm, ' ') // Quebras de linha por espaço
                .trim() // Remove espaços extras nas pontas
                .replace(/\s{2,}/g, ' ') // Remove múltiplos espaços
                .replace(/[^a-zA-ZÀ-ÿ0-9\s]/g, ''); // Remove tudo que não for letra (com acento), número ou espaço

              if (!workdExceptions.includes(wordResult) || index === 0) {
                return (
                  wordResult.charAt(0).toUpperCase() +
                  wordResult.slice(1).toLowerCase()
                );
              }

              return wordResult;
            })
            .join(' ');

          if (name) {
            this.name = name;
          }
        }

        next();
      } catch (e) {
        console.log('erro: ', e.toString());
      }
    },
  },
  database: {
    collection: 'Students',
    connection: 'database_piaget',
    fields: {
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
        index: true,
      },
      studentRegistrationCode: {
        type: Number,
        unique: 'RA já cadastrado',
        required: true,
      },
      _schoolEducationAlias: {
        type: String,
        required: false,
        allowNull: false,
      },
      schoolEducations: {
        type: [
          {
            schoolEducation: {
              type: String,
            },
            educationInstitution: {
              type: String,
            },
            course: {
              type: String,
            },
            dateConclusion: {
              type: Date,
            },
            dateCollation: {
              type: Date,
            },
            state: {
              type: String,
            },
            city: {
              type: String,
            },
          },
        ],
        required: false,
        allowNull: false,
      },
      name: {
        type: String,
        required: true,
      },
      cpf: {
        type: String,
        required: function () {
          console.log(this.foreigner);
          return !this.foreigner;
        },
        unique: 'Cpf já cadastrado',
        sparse: true,
        default: function () {
          return this.document;
        },
        allowNull: false,
      },
      documentCountryOrigin: {
        type: String,
        required: function () {
          return this.foreigner;
        },
        default: 'Brasil',
      },
      documentType: {
        type: String,
        required: function () {
          return this.foreigner;
        },
        default: 'CPF',
      },
      document: {
        type: String,
        required: function () {
          return this.foreigner;
        },
      },
      foreigner: {
        type: Boolean,
        default: false,
      },
      identity: {
        type: String,
        // required: false
      },
      dispatcher: {
        type: String,
        required: false,
        allowNull: false,
      },
      genre: {
        type: String,
        enum: ['M', 'F'],
        // required: false
      },
      phone: {
        type: String,
        required: false,
        allowNull: true,
      },
      cellPhone: {
        type: String,
        required: true,
        allowNull: false,
      },
      whatsApp: {
        type: String,
        required: false,
        allowNull: true,
      },
      address: {
        street: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado',
        },
        number: {
          type: String,
          required: false,
          allowNull: true,
          default: 'SN',
        },
        complement: {
          type: String,
          required: false,
          allowNull: true,
          default: 'Não informado',
        },
        zone: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado',
        },
        zip: {
          type: String,
          required: false,
          allowNull: false,
          default: '00000000',
        },
        city: {
          type: String,
          required: false,
          allowNull: false,
          default: 'Não informado',
        },
        ibgeCityCode: {
          type: Number,
          required: false,
          allowNull: false,
        },
        uf: {
          type: String,
          required: false,
          allowNull: false,
          default: 'NA',
        },
      },
      birthDate: {
        type: Date,
        required: false,
        allowNull: false,
      },
      educationInstitution: {
        type: String,
        required: false,
        allowNull: false,
      },
      graduationCourse: {
        type: String,
        required: false,
        allowNull: false,
      },
      dateDegree: {
        type: Date,
        // required: false
      },
      dateConclusion: {
        type: Date,
        required: false,
        allowNull: false,
      },
      naturalessCity: {
        type: String,
        required: false,
        allowNull: false,
      },
      naturalessState: {
        type: String,
        required: false,
        allowNull: false,
      },
      nationality: {
        type: String,
        required: false,
        allowNull: false,
      },
      motherName: {
        type: String,
        required: false,
        allowNull: false,
      },
      fatherName: {
        type: String,
        required: false,
        allowNull: false,
      },
      specialNeeds: [
        {
          name: {
            type: String,
            required: false,
          },
        },
      ],
      displacement: [
        {
          city: {
            type: String,
            required: false,
            allowNull: true,
          },
          state: {
            type: String,
            required: false,
            allowNull: true,
          },
        },
      ],
      createArchive: {
        type: Boolean,
        default: false,
      },
      signature: {
        font: {
          type: String,
          // required: false,
          default: 'Padrão',
        },
        content: {
          type: String,
          // required: false
        },
        signature_base64: {
          type: String,
          // required: false
        },
      },
      isActive: {
        type: Boolean,
        default: true,
        allowNull: false,
      },
      blockNotification: {
        type: Boolean,
        default: false,
      },
      optin: {
        type: Boolean,
        required: false,
      },
      lastAcess: {
        type: Date,
        required: false,
      },
      email: {
        type: String,
        required: false,
        lowercase: true,
      },
      documents: {
        type: [
          {
            _id: false,
            _documentTypeId: {
              type: mongoose.SchemaTypes.ObjectId,
              required: true,
            },
            name: {
              type: String,
              required: true,
              index: true,
            },
            courseTypes: {
              type: [String],
              required: true,
              index: true,
            },
            createdAt: {
              type: Date,
              required: false,
            },
            updatedAt: {
              type: Date,
              required: false,
            },
            // É ATUALIZADO NO HOOK "updateEnrolments" NA MODELAGEM DE DOCUMENTS
            status: {
              type: String,
              required: true,
              index: true,
            },
            // É ATUALIZADO NO HOOK "updateEnrolments" NA MODELAGEM DE DOCUMENTS
            _studentDocumentId: {
              type: mongoose.SchemaTypes.ObjectId,
            },
          },
        ],
        required: false,
        default: [],
      },
    },
    options: {
      timestamps: true,
    },
  },
};

Students.database.pre = {
  save: [
    Students.functions.fixName,
    Students.functions.populateExternalFieldsOnSave,
    Students.functions.fixNumberPhone,
    Students.functions.fixNumberCep,
  ],
  findOneAndUpdate: [
    Students.functions.fixName,
    Students.functions.populateExternalFieldsOnFindOneAndUpdate,
    Students.functions.fixNumberPhone,
    Students.functions.fixNumberCep,
  ],
};

Students.database.post = {
  save: [
    Students.functions.fixName,
    Students.functions.propagateFieldChanges,
    Students.functions.setRequiredDocumentsByGenre,
    Students.functions.fixNumberCep,
  ],
  findOneAndUpdate: [
    Students.functions.fixName,
    Students.functions.propagateFieldChanges,
    Students.functions.setRequiredDocumentsByGenre,
    Students.functions.fixNumberCep,
  ],
};

module.exports = Students;
