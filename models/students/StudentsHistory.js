const mongoose = require('mongoose');

let StudentsHistory = {
    functions: {},
    database: {
        collection: 'StudentsHistory',
        fields    : {
            user: {
                type   : {
                    _id  : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type    : String,
                        required: true
                    }
                },
                required: true
            },
            newStudent: {
                _userId              : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true,
                    index   : true
                },
                studentRegistrationCode: {
                    type     : Number,
                    required : true
                },
                _schoolEducationAlias: {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                name                 : {
                    type    : String,
                    required: true
                },
                cpf                  : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                identity             : {
                    type    : String,
                    // required: false
                },
                dispatcher           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                genre: {
                    type: String,
                    enum: [
                        'M',
                        'F'
                    ],
                    // required: false
                },
                phone                : {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                cellPhone            : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                whatsApp             : {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                address              : {
                    street    : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    number    : {
                        type     : String,
                        required : false,
                        allowNull: true,
                        default: 'SN'
                    },
                    complement: {
                        type     : String,
                        required : false,
                        allowNull: true,
                        default: 'Não informado'
                    },
                    zone      : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    zip       : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: '00000000'
                    },
                    city      : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    uf        : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'NA'
                    }
                },
                birthDate            : {
                    type     : Date,
                    required : false,
                    allowNull: false
                },
                educationInstitution : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                graduationCourse     : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                dateDegree: {
                    type: Date,
                    // required: false
                },
                dateConclusion       : {
                    type     : Date,
                    required : false,
                    allowNull: false
                },
                naturalessCity       : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                naturalessState      : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                nationality          : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                motherName           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                fatherName           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                specialNeeds: [
                    {
                        name: {
                            type     : String,
                            required : false
                        }
                    }
                ],
                displacement         : [
                    {
                        city : {
                            type     : String,
                            required : false,
                            allowNull: true
                        },
                        state: {
                            type     : String,
                            required : false,
                            allowNull: true
                        }
                    }
                ],
                createArchive: {
                    type: Boolean,
                    default: false
                },
                signature : {
                    font: {
                        type: String,
                        // required: false,
                        default: 'Padrão'
                    },
                    content: {
                        type: String,
                        // required: false
                    },
                    signature_base64: {
                        type: String,
                        // required: false
                    }
                },
                isActive             : {
                    type     : Boolean,
                    default  : true,
                    allowNull: false
                },
                blockNotification    : {
                    type     : Boolean,
                    default  : false
                },
                optin : {
                    type     : Boolean,
                    required : false
                },
                lastAcess: {
                    type     : Date,
                    required : false
                }
            },
            oldStudent: {
                _userId              : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true,
                    index   : true
                },
                studentRegistrationCode: {
                    type     : Number,
                    required : true
                },
                _schoolEducationAlias: {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                name                 : {
                    type    : String,
                    required: true
                },
                cpf                  : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                identity             : {
                    type    : String,
                    // required: false
                },
                dispatcher           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                genre: {
                    type: String,
                    enum: [
                        'M',
                        'F'
                    ],
                    // required: false
                },
                phone                : {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                cellPhone            : {
                    type     : String,
                    required : true,
                    allowNull: false
                },
                whatsApp             : {
                    type     : String,
                    required : false,
                    allowNull: true
                },
                address              : {
                    street    : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    number    : {
                        type     : String,
                        required : false,
                        allowNull: true,
                        default: 'SN'
                    },
                    complement: {
                        type     : String,
                        required : false,
                        allowNull: true,
                        default: 'Não informado'
                    },
                    zone      : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    zip       : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: '00000000'
                    },
                    city      : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'Não informado'
                    },
                    uf        : {
                        type     : String,
                        required : false,
                        allowNull: false,
                        default: 'NA'
                    }
                },
                birthDate            : {
                    type     : Date,
                    required : false,
                    allowNull: false
                },
                educationInstitution : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                graduationCourse     : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                dateDegree: {
                    type: Date,
                    // required: false
                },
                dateConclusion       : {
                    type     : Date,
                    required : false,
                    allowNull: false
                },
                naturalessCity       : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                naturalessState      : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                nationality          : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                motherName           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                fatherName           : {
                    type     : String,
                    required : false,
                    allowNull: false
                },
                specialNeeds: [
                    {
                        name: {
                            type     : String,
                            required : false
                        }
                    }
                ],
                displacement         : [
                    {
                        city : {
                            type     : String,
                            required : false,
                            allowNull: true
                        },
                        state: {
                            type     : String,
                            required : false,
                            allowNull: true
                        }
                    }
                ],
                createArchive: {
                    type: Boolean,
                    default: false
                },
                signature : {
                    font: {
                        type: String,
                        // required: false,
                        default: 'Padrão'
                    },
                    content: {
                        type: String,
                        // required: false
                    },
                    signature_base64: {
                        type: String,
                        // required: false
                    }
                },
                isActive             : {
                    type     : Boolean,
                    default  : true,
                    allowNull: false
                },
                blockNotification    : {
                    type     : Boolean,
                    default  : false
                },
                optin : {
                    type     : Boolean,
                    required : false
                },
                lastAcess: {
                    type     : Date,
                    required : false
                }
            },
            action:{
                type: String,
                required: true
            },
            recurrences: {
                type: [String],
                // required: false
            },
            enrolments: {
                type: [String],
                // required: false
            },
            documents: {
                type: [String],
                // required: false
            },
            wallets: {
                type: [String],
                // required: false
            },
            selectiveProcessStudents: {
                type: [String],
                // required: false
            },
            futureCharges: {
                type: [String],
                // required: false
            },
            transactions: {
                type: [String],
                // required: false
            },
            solicitationCertificates: {
                type: [String],
                // required: false
            },
            certificates: {
                type: [String],
                // required: false
            },
            solicitations: {
                type: [String],
                // required: false
            }
        }
    }
};

module.exports = StudentsHistory;
