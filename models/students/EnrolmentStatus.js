const mongoose = require('mongoose');

const EnrolmentStatus = {
  database: {
    collection: 'EnrolmentStatus',
    fields: {
      _enrolmentId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userType: {
        type: String,
        required: true,
        enum: [
          'student',
          'partner',
          'teacher',
          'employer',
          'computer',
        ],
      },
      _userName: {
        type: String,
        required: true,
      },
      status: {
        type: String,
        enum: [
          'waiting_confirm',
          'waiting_document',
          'matriculate',
          'locked',
          'completed',
          'changed',
          'canceled',
          'blocked',
          'in_progress',
          'expired',
          'rehabilitation',
          'awaiting_approval_by_deferral',
          'rejected_by_deferral',
          'disapproved',
        ],
        default: 'waiting_confirm',
        required: true,
      },
      applyOnCombo: Boolean,
      description: String,
      metadata: mongoose.SchemaTypes.Mixed,
    },
  },
};

module.exports = EnrolmentStatus;
