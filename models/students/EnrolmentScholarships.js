const mongoose = require("mongoose");

let EnrolmentScholarships = {
    functions: {
        parseEnrolmentId: function (next) {
            try {
                if (Array.isArray(this._enrolmentId)) {
                    this._enrolmentId = this._enrolmentId.map(id => new mongoose.Types.ObjectId(id));
                } else {
                    this._enrolmentId = new mongoose.Types.ObjectId(this._enrolmentId);
                }
                next();
            } catch (e) {
                console.error(e);
                next();
            }
        }
    },
    database: {
        collection: 'EnrolmentScholarships',
        fields: {
            studentName: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            cpf: {
                type: String,
                required: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
                index: true
            },
            certifierName: {
                type: String,
                required: true
            },
            courseTypeName: {
                type: String,
                required: true
            },
            courseName: {
                type: String,
                required: true
            },
            _scholarshipId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            scholarshipName: {
                type: String,
                required: true
            },
            chargeType: {
                type: [String],
                required: true
            },
            discountType: {
                type: String,
                enum: [
                    'fixedValue',
                    'percentage'
                ],
            },
            value: {
                type: Number,
                required: true
            },
            useDueDate: {
                type: Boolean,
                default: false
            },
            dateStart: {
                type: Date,
                // required: false
            },
            dateEnd: {
                type: Date,
                // required: false
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            useInOverdueCharge: {
                type: Boolean,
                requried: false,
                default: true
            },
            observation: {
                type: String
            },
            isCombo: {
                type: Boolean,
                default: false
            },
            _chargeId: {
                type: mongoose.SchemaTypes.ObjectId,
            }
        }
    }
};

EnrolmentScholarships.database.pre = {
    save: [
        EnrolmentScholarships.functions.parseEnrolmentId,
    ]
};

module.exports = EnrolmentScholarships;
