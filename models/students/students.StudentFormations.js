const mongoose = require('mongoose');

let StudentFormations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'StudentFormations',
        fields: {
            _studentId  : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            cpf          : {
                type     : String,
                required : true,
                unique   : 'Cpf já cadastrado',
                allowNull: false
            },
            name        : {
                type    : String,
                required: true
            },
            formations   : {
                type     : [{
                    formationLevel: {
                        type     : String,
                        required : true
                    },
                    category: {
                        type     : String,
                        required : true
                    },
                    certifier:  {
                        type     : String,
                        required : true
                    },
                    inputMethod: {
                        type     : String,
                        required : true
                    },
                    course: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    state: {
                        type     : String,
                        required : true
                    },
                    city: {
                        type     : String,
                        required : true
                    },
                    dateStart: {
                        type     : Number,
                        required : false,
                        allowNull: true
                    },
                    dateEnd: {
                        type     : Number,
                        required : false,
                        allowNull: true
                    },
                    dateConclusion: {
                        type     : Date,
                        required : true
                    },
                    workload: {
                        type     : Number,
                        required : false,
                        allowNull: true
                    },
                    concierge: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    observation: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    archive: {
                        type     : String,
                        required : false,
                        allowNull: true
                    }
                }],
                // required: false
            },
            inactiveFormations   : {
                type     : [{
                    _newFormationId: {
                        type     : mongoose.SchemaTypes.ObjectId,
                        required : false
                    },
                    formationLevel: {
                        type     : String,
                        required : true
                    },
                    category: {
                        type     : String,
                        required : true
                    },
                    certifier:  {
                        type     : String,
                        required : true
                    },
                    inputMethod: {
                        type     : String,
                        required : true
                    },
                    course: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    state: {
                        type     : String,
                        required : true
                    },
                    city: {
                        type     : String,
                        required : true
                    },
                    dateStart: {
                        type     : Number,
                        required : false
                    },
                    dateEnd: {
                        type     : Number,
                        required : false,
                        allowNull: true
                    },
                    dateConclusion: {
                        type     : Date,
                        required : true
                    },
                    workload: {
                        type     : Number,
                        required : false,
                        allowNull: true
                    },
                    concierge: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    observation: {
                        type     : String,
                        required : false,
                        allowNull: true
                    },
                    archive: {
                        type     : String,
                        required : false,
                        allowNull: true
                    }
                }],
                // required: false
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = StudentFormations;
