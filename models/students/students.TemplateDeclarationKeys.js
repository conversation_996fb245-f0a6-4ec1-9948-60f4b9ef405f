let TemplateDeclarationKeys = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TemplateDeclarationKeys',
        fields    : {
            name: {
                type: String,
                required:true
            },
            templateKeys: [
                {
                    name: {
                        type: String,
                        minlength: 1,
                        index: true,
                        required: true
                    },
                    description: {
                        type: String,
                        required: true
                    }
                }
            ],
            isActive: {
                type: Boolean,
                default: true
            }
        }
    }
}

module.exports = TemplateDeclarationKeys;
