const mongoose = require('mongoose');
const {SchemaTypes} = require("mongoose");

const reducedPaymentPlanSchema = {
    installment: {
        type: Number,
    },
    value: {
        type: Number,
    }
};

let Classes = {
    functions: {
        parseVacancies: function (next) {
            if (this._update?.$set?.vacancies === -1)
                this._update.$set.vacancies = Infinity;

            return next();
        },
    },
    database: {
        connection: 'database_piaget',
        collection: 'Classes',
        fields: {
            name: { // nome da turma
                type: String,
                required: true,
            },
            certifier: { // nome da turma
                type: String,
                required: true,
            },
            polo: {
                _id: { // id do polo
                    type: mongoose.SchemaTypes.ObjectId,

                },
                name: {// nome do polo
                    type: String,

                },
            },

            modalityName: {  // Ensino a distancia , ead, semi presencial etc
                type: String,
                required: true,
            },

            module: {
                type: Number,
                required: true,//1 ao 10 periodo ou único
            },
            coordinator: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,

                    },
                    name: {
                        type: String,

                    }
                },

            },
            code: {  // Abreviatura para turma
                type: String,
                required: true,
                uppercase: true,
                index: true
            },
            customCode: { // Abreviatura para turma customizada
                type: String,
            },
            dateStart: { // No momento de adicionar no processo seletivo só pode seleecionar turmas que ainda não começaram e pertecem a mesma unidade.
                type: Date,
                required: true,
            },
            dateEnd: {
                type: Date,
                required: true,
            },
            curriculumBaseDate: {
                type: Date,
                required: true,
            },
            dateStartEnrolment: {
                type: Date,
                required: false,
            },
            dateEndEnrolment: {
                type: Date,
                required: false,
            },

            typeName: {  // Gruaduação, capacitação, tecnologo etc (nivel)
                type: String,
                required: true,
            },
            vacancies: {
                type: Number
            },
            closeDiary: {
                type: Boolean,
                default: false
            },
            periodicity: {
                type: String,
                enum: [
                    'semester', 'yearly', 'monthly', 'bimonthly', 'quarterly'
                ],
                default: 'semester'
            },

            course: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                    index: true,
                },
                name: {  // nome do curso vinculado
                    type: String,
                    required: true,
                }
            },

            shift: {
                type: String,
                enum: ['morning', 'afternoon', 'night'],
                default: 'night'//turno de funcionamento
            },

            evaluationMethod: {
                type: String,
                enum: [
                    'average',             // média
                    'bimonthly_average',   // média bimestral
                    'modular_sum',         // soma modular
                    'period_last_average', // período da última média
                    'quarterly_sum'        // soma trimestral
                ],
                default: 'average'
            },
            maxGrade: {
                type: Number,

                allowNull: true,
                default: null
            },
            minimumGradeApproval: {     // Nota minima para aprovação do aluno
                type: Number,
                required: true,
            },
            minimumGradeRecuperation: { // Nota minima para fazer a recuperação
                type: Number,
                required: true,
            },
            classDuration: {            // Duração das aulas
                type: Number,

            },
            minimalHoursComplementaryActivity: { // Quantidade minima de horas para atividades complementares
                type: Number,
                required: true,
            },
            maximumAbsences: {          // Numero máximo de faltas
                type: Number,
                required: true,
            },
            typeCalculateAbsences: {   // Formula de calcular as faltas
                type: String,
                enum: ['percentage', 'number'],
                default: 'number' // De que forma vai calcular as faltas
            },
            resolution: {
                type: String,

            },
            isActive: {
                type: Boolean,
                required: true,
            },
            moduleNames: [
                {
                    module: {
                        type: Number,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                }
            ],
            disciplines: [
                {
                    name: {
                        type: String,
                        required: true,
                    },
                    sagahDiscipline: {
                        type: String,

                    },
                    type: {
                        type: String,
                        default: 'required'
                    },
                    discId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true,
                    },
                    module: {
                        type: Number,

                    },
                    workload: {
                        type: Number,

                    },
                    ignoreOffersCalendar: {
                        type: Boolean,
                        default: false
                    },
                    approvedByTeacher: {
                        type: Boolean,
                        default: false
                    },
                    teacherApprovedAt: Date,
                    teacherReproveReason: String,
                    internalObservations: [
                        {
                            date: Date,
                            text: String,
                            userName: String,
                        }
                    ],
                    teacher: {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,
                            index: true,
                        },
                        name: {  // nome do curso vinculado
                            type: String,

                        }
                    },
                    tutor: {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        name: {  // nome do curso vinculado
                            type: String,

                        }
                    },
                    pathknowledges: {
                        type: [{
                            _id: mongoose.SchemaTypes.ObjectId,
                            name: String,
                            courseware: {
                                type: {
                                    _id: mongoose.SchemaTypes.ObjectId,
                                    title: String
                                }
                            },
                            pieces: [{
                                useGrade: Boolean,
                                grade: Number,
                                name: String,
                                _pieceId: mongoose.SchemaTypes.ObjectId,
                            }],
                            grade: Number,
                            useGrade: Boolean,
                            dateStart: Date,
                            dateEnd: Date
                        }],
                    },
                    dateStart: {
                        type: Date,
                    },
                    dateEnd: {
                        type: Date,
                    },
                    code: { // Sigla para disciplina
                        type: String,
                    },
                    grouping: {
                        type: {
                            _id: { //ID DO agrupmanento
                                type: mongoose.SchemaTypes.ObjectId
                            },
                            groupId: { //ID DO agrupmanento
                                type: String,
                            },
                            name: { // nome do agrupamento
                                type: String
                            },
                            description: { //Descrição do agrupamento
                                type: String
                            },
                            teacher: {
                                _id: {
                                    type: mongoose.SchemaTypes.ObjectId
                                },
                                name: {
                                    type: String
                                }
                            }
                        },
                    },
                    forums: [{
                        forumId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        title: {
                            type: String,

                        },
                        type: {
                            type: String,

                        },
                    }],
                    directedStudies: [{
                        directedStudyId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        title: {
                            type: String,

                        },
                        type: {
                            type: String,

                        },
                    }],
                    coursewareType: {
                        type: String
                    },
                    coursewares: [{
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        coursewareId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        name: {
                            type: String,

                        },
                    }],
                    activities: [{
                        activityId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        activityType: {
                            _id: mongoose.SchemaTypes.ObjectId,
                            title: String,
                            selectedActivity: String
                        },
                        type: {
                            type: String,

                        },
                        maxDuration: {
                            type: String,

                        },
                        isFinalTest: {
                            type: Boolean,

                        },
                        chapter: {
                            type: [{
                                _id: false,
                                coursewareId: mongoose.SchemaTypes.ObjectId,
                                coursewareName: String,
                                number: Number
                            }],
                        },
                        modality: {
                            type: String,

                        },
                        model: {
                            type: String,

                        },
                        modelMeta: {
                            type: SchemaTypes.Mixed,
                        },
                        maxGrade: {
                            type: Number,
                            required: true,
                            default: 10
                        },
                        dateStart: {
                            type: Date,

                        },
                        dateEnd: {
                            type: Date,

                        },
                    }],
                    extraCoursewares: {
                        type: [mongoose.SchemaTypes.ObjectId],
                        default: []
                    },
                    chapter: {
                        type: [{
                            _id: false,
                            coursewareId: mongoose.SchemaTypes.ObjectId,
                            coursewareName: String,
                            number: Number
                        }],
                    },
                    dependecyConfig: {
                        type: {
                            useDependecy: {
                                type: Boolean,
                                default: false
                            },
                            activities: [
                                {
                                    type: {
                                        type: String,
                                        enum: [
                                            'regular',
                                            'recuperation'
                                        ],
                                        // required: false
                                    },
                                    modality: {
                                        type: String,
                                        enum: [
                                            'online',
                                            'presential'
                                        ],
                                        // required: false
                                    },
                                    maxDuration: {
                                        type: Number,
                                        // required: false
                                    },
                                    model: {
                                        type: String,
                                        enum: [
                                            'form',
                                            'evaluation',
                                            'upload',
                                            'participation',
                                            'sagah',
                                            'forum'
                                        ],
                                        // required: false
                                    },
                                    isFinalTest: {
                                        type: Boolean,
                                        // required: false
                                    },
                                    chapter: {
                                        type: [{
                                            _id: false,
                                            coursewareId: mongoose.SchemaTypes.ObjectId,
                                            coursewareName: String,
                                            number: Number
                                        }],
                                    },
                                    modelMeta: {
                                        _id: {
                                            type: mongoose.SchemaTypes.ObjectId
                                        },
                                        numQuestions: {
                                            type: Number,
                                            // required: false
                                        },
                                        enunciation: {
                                            type: String,
                                            // required: false
                                        },
                                        ltiUrl: {
                                            type: String,
                                            // required: false
                                        },
                                        ltiTitle: {
                                            type: String,
                                            // required: false
                                        }
                                    },
                                    maxGrade: {
                                        type: Number,
                                        // required: false,
                                        default: 10
                                    }
                                }
                            ],
                            useRate: {
                                type: Boolean,
                                default: false
                            },
                            rateAmount: Number,
                            period: Number
                        }
                    }
                }
            ],
            optionalDisciplines: [
                {
                    name: {
                        type: String,
                        required: true,
                    },
                    sagahDiscipline: {
                        type: String,

                    },
                    type: {
                        type: String,
                        default: 'required'
                    },
                    discId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true,
                    },
                    module: {
                        type: Number,

                    },
                    workload: {
                        type: Number,

                    },
                    teacher: {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,
                            index: true,
                        },
                        name: {  // nome do curso vinculado
                            type: String,

                        }
                    },
                    tutor: {
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        name: {  // nome do curso vinculado
                            type: String,

                        }
                    },
                    pathknowledges: {
                        type: [{
                            _id: mongoose.SchemaTypes.ObjectId,
                            name: String,
                            courseware: {
                                type: {
                                    _id: mongoose.SchemaTypes.ObjectId,
                                    title: String
                                }
                            },
                            pieces: [{
                                useGrade: Boolean,
                                grade: Number,
                                name: String,
                                _pieceId: mongoose.SchemaTypes.ObjectId,
                            }],
                            grade: Number,
                            useGrade: Boolean,
                            dateStart: Date,
                            dateEnd: Date
                        }]
                    },
                    dateStart: {
                        type: Date,

                    },
                    dateEnd: {
                        type: Date,

                    },
                    code: { // Sigla para disciplina
                        type: String,

                    },
                    grouping: {
                        type: {
                            _id: { //ID DO agrupmanento
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true,
                            },
                            groupId: { //ID DO agrupmanento
                                type: String,

                            },
                            name: { // nome do agrupamento
                                type: String,
                                required: true
                            },
                            description: { //Descrição do agrupamento
                                type: String,
                                required: true,
                            },
                            teacher: {
                                _id: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    required: true,
                                },
                                name: {
                                    type: String,
                                    required: true
                                }
                            }
                        },

                    },
                    forums: [{
                        forumId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        title: {
                            type: String,

                        },
                        type: {
                            type: String,

                        },
                    }],
                    directedStudies: [{
                        directedStudyId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        title: {
                            type: String,

                        },
                        type: {
                            type: String,

                        },
                    }],
                    coursewareType: {
                        type: String
                    },
                    coursewares: [{
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        coursewareId: {
                            type: mongoose.SchemaTypes.ObjectId,

                        },
                        name: {
                            type: String,

                        },
                    }],
                    activities: [{
                        activityId: {
                            type: String,

                        },
                        activityType: {
                            _id: mongoose.SchemaTypes.ObjectId,
                            title: String,
                            selectedActivity: String
                        },
                        type: {
                            type: String,

                        },
                        maxDuration: {
                            type: String,

                        },
                        isFinalTest: {
                            type: Boolean,

                        },
                        chapter: {
                            type: [{
                                _id: false,
                                coursewareId: mongoose.SchemaTypes.ObjectId,
                                coursewareName: String,
                                number: Number
                            }],
                        },
                        modality: {
                            type: String,

                        },
                        model: {
                            type: String,

                        },
                        modelMeta: {
                            type: SchemaTypes.Mixed,
                        },
                        maxGrade: {
                            type: Number,
                            required: true,
                            default: 10
                        },
                        dateStart: {
                            type: Date,

                        },
                        dateEnd: {
                            type: Date,

                        },
                    }],
                    extraCoursewares: {
                        type: [mongoose.SchemaTypes.ObjectId]
                    },
                    chapter: {
                        type: [{
                            _id: false,
                            coursewareId: mongoose.SchemaTypes.ObjectId,
                            coursewareName: String,
                            number: Number
                        }],
                    },
                    dependecyConfig: {
                        type: {
                            useDependecy: Boolean,
                            activities: [
                                {
                                    type: {
                                        type: String,
                                        enum: [
                                            'regular',
                                            'recuperation'
                                        ],
                                        // required: false
                                    },
                                    modality: {
                                        type: String,
                                        enum: [
                                            'online',
                                            'presential'
                                        ],
                                        // required: false
                                    },
                                    maxDuration: {
                                        type: Number,
                                        // required: false
                                    },
                                    model: {
                                        type: String,
                                        enum: [
                                            'form',
                                            'evaluation',
                                            'upload',
                                            'participation',
                                            'sagah',
                                            'forum'
                                        ],
                                        // required: false
                                    },
                                    isFinalTest: {
                                        type: Boolean,
                                        // required: false
                                    },
                                    chapter: {
                                        type: [{
                                            _id: false,
                                            coursewareId: mongoose.SchemaTypes.ObjectId,
                                            coursewareName: String,
                                            number: Number
                                        }],
                                    },
                                    modelMeta: {
                                        _id: {
                                            type: mongoose.SchemaTypes.ObjectId
                                        },
                                        numQuestions: {
                                            type: Number,
                                            // required: false
                                        },
                                        enunciation: {
                                            type: String,
                                            // required: false
                                        },
                                        ltiUrl: {
                                            type: String,
                                            // required: false
                                        },
                                        ltiTitle: {
                                            type: String,
                                            // required: false
                                        }
                                    },
                                    maxGrade: {
                                        type: Number,
                                        // required: false,
                                        default: 10
                                    }
                                }
                            ],
                            useRate: {
                                type: Boolean,
                                default: false
                            },
                            rateAmount: Number,
                            period: Number
                        }
                    },
                }
            ],
            reEnrolments: [
                {
                    acceptanceTerm: {
                        type: Boolean,
                        required: true,
                    },
                    blockDefault: {
                        type: Boolean,
                        required: true,
                    },
                    generateBilling: {
                        type: Boolean,
                        required: true,
                    },
                    termReEnrolment: {
                        type: String,

                    },
                    dateStart: {
                        type: Date,
                        required: true,
                    },
                    dateEnd: {
                        type: Date,
                        required: true,
                    },
                    chargeType: {
                        type: String,

                    },
                    rateEnrolmentValue: {
                        type: Number,

                    },
                    havePaymentPlan: {
                        type: Boolean,
                        default: false,
                    },
                    paymentPlan: reducedPaymentPlanSchema,
                    applyMonetaryCorrection: {
                        type: Boolean,
                        default: false,
                    }
                }
            ],
            curriculumMatrix: {
                _id: mongoose.SchemaTypes.ObjectId,
                code: String,
                hoursComplementaryActivity: Number,
                internshipWorkload: Number,
                quantityOptionalDisciplines: Number,
                workloadOptionalDisciplines: Number,
                internships: [
                    {
                        schedulingSent: {
                            type: Boolean
                        },
                        shipmentType: {
                            type: String
                        },
                        shipmentTypeQuantity: {
                            type: mongoose.SchemaTypes.Mixed
                        },
                        shipmentQuantity: {
                            type: Number
                        },
                        workload: {
                            type: Number
                        },
                        helpFile: {
                            type: String
                        },
                        fileModelsInternshipRequired: {
                            type: [{
                                url: String,
                                name: String,
                                typeDocument: String,
                            }]
                        },
                        stepsInternshipRequired: {
                            type: [{
                                isRequired: Boolean,
                                typeInternship: String,
                                title: String,
                                action: String,
                                description: String,
                                nextStatus: String,
                                reviewBy: String,
                                visibleIn: String,
                                originalFile: String,
                                order: Number,
                                internalAnalisy: Boolean,
                                typeLimitCorrect: String,
                                dateLimit: Date,
                                limitCorrect: Number,
                                multiFile: Boolean
                            }]
                        },
                        area: {
                            type: [{
                                _id: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    required: true
                                },
                                name: {
                                    type: String,
                                    required: true
                                }
                            }]
                        },
                        tutor: {
                            type:{
                                _id: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    required: true
                                },
                                name: {
                                    type: String,
                                    required: true
                                }
                            }
                        },
                        title: String
                    }
                ],
            },
            hasCurriculumMatrix: {
                type: Boolean,
                default: false
            },
            clonedOnDate: {
                type: Date,
                required: false,
            },
            clonedByUser: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                    },
                    name: {
                        type: String,
                    }
                },
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    alias: 1,
                    groupId: 1,
                    poloId: 1,
                    code: 1,
                    isActive: 1
                },
                options: {}
            }
        ]
    }
};

Classes.database.pre = {
    save: [
        Classes.functions.parseVacancies,
    ],
    findOneAndUpdate: [
        Classes.functions.parseVacancies,
    ]
};

module.exports = Classes;
