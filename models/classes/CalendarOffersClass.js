const { SchemaTypes } = require('mongoose')

let CalendarOffersClass = {
  functions: {},
  database: {
    connection: 'database_piaget',
    collection: 'CalendarOffersClass',
    fields: {
      isActive: {
        type: SchemaTypes.Boolean,
        required: false,
        default: false,
      },
      startDate: {
        type: SchemaTypes.Date,
        required: true
      },
      endDate: {
        type: SchemaTypes.Date,
        required: true
      }
    }
  }
}

module.exports = CalendarOffersClass;