const mongoose = require('mongoose');

let Grouping = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Grouping',
        fields: {
            name: { // nome do agrupamento
                type: String,
                required: true
            },
            description: { // descrição do agrupamento
                type: String,
                required: true
            },
            evaluationMethod: {
                type: String,
                enum: [
                    'average',             // média
                    'bimonthly_average',   // média bimestral
                    'modular_sum',         // soma modular
                    'period_last_average', // período da última média
                    'quarterly_sum'        // soma trimestral
                ],
                default: 'average'
            },

            dateStartDisc: {
                type: Date,
                required: true
            },
            dateEndDisc: {
                type: Date,
                required: true
            },
            teacher: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
            },
            approvedByTeacher: {
                type: Boolean,
                default: false
            },
            teacherApprovedAt: Date,
            teacherReproveReason: Date,
            tutor: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
            },
            teachingPlanByDiscipline: {
                type: Boolean,
                required: true
            },
            pathKnowledge: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
            },
            coursewares: [
                {
                    coursewareId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                }
            ],
            forums: [
                {
                    forumId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        index: true
                    },
                    title: {
                        type: String,
                    },
                    type: {
                        type: String,
                    }

                }
            ],
            activities: [
                {
                    type: {
                        type: String,
                        required: true
                    },
                    maxDuration: {
                        type: String,
                        required: true
                    },
                    isFinalTest: {
                        type: Boolean,
                        required: true
                    },
                    model: {
                        type: String,
                        required: true
                    },
                    modality: {
                        type: String,
                        default: 'online'
                    },
                    modelMeta: {
                        type: Object,
                    },
                    dateStart: {
                        type: Date,
                    },
                    dateEnd: {
                        type: Date,
                    },
                    chapter: {
                        type: [{
                            _id: false,
                            coursewareId: mongoose.SchemaTypes.ObjectId,
                            coursewareName: String,
                            number: Number
                        }],
                    },
                }
            ],
            // Disciplinas que estão sendo agrupadas
            disciplines: [
                {
                    courseId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    courseName: {
                        type: String,
                        required: true
                    },
                    classId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    className: {
                        type: String,
                        required: true
                    },
                    discId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    discClassId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                        index: true
                    },
                    discCode: {
                        type: String,
                    },
                    discName: {
                        type: String,
                        required: true
                    },
                    teacherId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        index: true
                    },
                    teacherName: {
                        type: String
                    },
                    chapter: {
                        type: [{
                            _id: false,
                            coursewareId: mongoose.SchemaTypes.ObjectId,
                            coursewareName: String,
                            number: Number
                        }],
                    },
                    internalObservations: [
                        {
                            date: Date,
                            text: String,
                            userName: String,
                        }
                    ],
                }
            ],
            chapter: {
                type: [{
                    _id: false,
                    coursewareId: mongoose.SchemaTypes.ObjectId,
                    coursewareName: String,
                    number: Number
                }],
            },
            isActive: {
                type: Boolean,
                required: true,
            },
            pathknowledges: {
                type: [{
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                    courseware: {
                        _id: mongoose.SchemaTypes.ObjectId,
                        title: String
                    },
                    pieces: [{
                        useGrade: Boolean,
                        grade: Number,
                        name: String,
                        _pieceId: mongoose.SchemaTypes.ObjectId,
                    }],
                    grade: Number,
                    useGrade: Boolean,
                    dateStart: Date,
                    dateEnd: Date
                }],
            },
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    name: 1,
                    description: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = Grouping;
