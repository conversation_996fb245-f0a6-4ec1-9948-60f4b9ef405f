const mongoose = require('mongoose');

let ClassLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ClassLogs',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            _enrolmentClassEntryId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            action: {
                type: String,
                required: true
            },
            body: {
                type: mongoose.SchemaTypes.Mixed
            },
            user: {
              type: {
                 _userName: String,
                 _userId: mongoose.SchemaTypes.ObjectId,
                 _userType: String
              },
              required: true
            },
            toClass:{
                type: mongoose.SchemaTypes.Mixed,
                default: null
            },
            fromClass:{
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            date: {
                type: Date,
                required: true
            }
        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    name: 1,
                    description: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = ClassLogs;
