const mongoose = require('mongoose');

const daySchema = {
  start: {
    type: String,
    required: true,
  },
  end: {
    type: String,
    required: true,
  },
};

const Polos = {
  functions: {},
  database: {
    collection: 'Polos',
    connection: 'database_piaget',
    fields: {
      name: {
        type: String,
        required: true,
      },
      socialReason: {
        type: String,
        required: true,
      },
      _cnpj: {
        length: 14,
        required: true,
        type: String,
      },
      certifier: [
        {
          _certifierId: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
          alias: {
            type: String,
            required: true,
          },
        },
      ],
      typeNames: [
        {
          _typeNameId: {
            type: mongoose.SchemaTypes.ObjectId,
            // required: false
          },
          name: {
            type: String,
            // required: false
          },
          alias: {
            type: String,
            // required: false
          },
        },
      ],
      hasSuportToHealth: {
        type: Boolean,
        default: false,
      },
      cellPhone: {
        type: String,
      },
      phone: {
        type: String,
      },
      email: {
        type: String,
      },
      socialMedia: {
        type: {
          linkedin: String,
          site: String,
          instagram: String,
        },
      },
      addresses: {
        street: {
          type: String,
          required: true,
        },
        number: {
          type: Number,
          required: true,
        },
        zone: {
          type: String,
          required: true,
        },
        city: {
          type: String,
          required: true,
        },
        zip: {
          type: String,
          required: true,
          length: 8,
        },
        complement: {
          type: String,
          lowercase: true,
        },
        state: {
          type: String,
          required: true,
          length: 2,
        },
      },
      status: {
        type: String,
        enum: [
          'contract-pending',
          'contract-signed',
        ],
        default: 'contract-pending',
      },
      isActive: {
        type: Boolean,
        default: false,
      },
      partner: {
        _partnerId: {
          type: mongoose.SchemaTypes.ObjectId,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        _cpf: {
          type: String,
          required: true,
          index: true,
          length: 11,
        },
      },
      mecCode: {
        type: String,
        // required: false,
      },
      observation: {
        type: String,
      },
      images: {
        type: [
          {
            name: String,
            file: String,
          },
        ],
      },
      othersFiles: {
        type: [
          {
            name: String,
            file: String,
          },
        ],
      },
      contracts: {
        type: [
          {
            name: String,
            file: String,
            typeContract: String,
            templateContractId: mongoose.SchemaTypes.ObjectId,
            _poloContractId: mongoose.SchemaTypes.ObjectId,
            signDate: Date,
            userName: String,
            userId: mongoose.SchemaTypes.ObjectId,
            certifier: {
              _id: mongoose.SchemaTypes.ObjectId,
              name: String,
              alias: String,
            },
          },
        ],
      },
      archives: {
        type: [
          {
            file: String,
            fileName: String,
          },
        ],
      },
      turns: {
        type: [String],
        // required: false,
        default: [],
      },
      operation: {
        type: {
          monday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          tuesday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          wednesday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          thursday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          friday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          saturday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
          sunday: {
            type: [daySchema],
            // required: false,
            default: [],
          },
        },
        // required: false,
        default: {},
      },
    },
  },
};

module.exports = Polos;
