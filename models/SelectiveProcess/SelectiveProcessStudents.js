const mongoose = require('mongoose');
let SelectiveProcessStudents = {
    functions: {},
    database: {
        collection: 'SelectiveProcessStudents',
        connection: 'database_piaget',
        fields: {
            certifier: {
                // Id da certificadora
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                // Nome da certificadora
                name: {
                    type: String,
                    required: true
                }
            },

            selectiveProcess: {
                // Id do processo seletivo
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                // Nome do processo seletivo
                name: {
                    type: String,
                    required: true
                }
            },

            email: {
                type: String,
                required: true
            },

            autoCorrection: {
                type: Boolean,
                default: false
            },

            percentageApproval: {
                type: Number,
                required: true,
            },

            cellPhone: {
                type: String,
                required: true,
                allowNull: false
            },

            student: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                cpf: {
                    type: String,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                birthdate: {
                    type: Date,
                    required: true
                },
                specialNeeds: [
                    {
                        name: {
                            type: Date,
                            // required: false
                        }
                    }
                ]
            },

            // Os cursos que se inscreveu
            courses: [
                {
                    maximumDuration: {
                        type: Number,
                        required: true,
                    },

                    descriptionForProof: {
                        type: String,
                        required: true,
                    },

                    // Id do curso
                    courseId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },

                    // Nome do curso
                    name: {
                        type: String,
                        required: true
                    },

                    partner: {
                        type: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            name: {
                                type: String,
                                required: true
                            }
                        },
                        // required: false
                    },

                    polo: {
                        // Id do polo
                        _id: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        // Nome do polo
                        name: {
                            type: String,
                            required: true
                        }
                    },

                    enrolmentId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },

                    //campo para salvar o usuario que realizou alteracoes
                    changes: [{
                        type: {
                            type: String
                        },
                        date: {
                            type: Date,
                            required: true
                        },
                        user: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },

                            name: {
                                type: String,
                                required: true
                            },
                        },
                    }],
                    inputMethod: {
                        // Se já foi corrigido
                        rated: {
                            type: Boolean,
                            default: false
                        },

                        // Se ja terminou
                        finished: {
                            type: Boolean,
                            default: false
                        },

                        spendingTime: {
                            type: Boolean,
                            default: false
                        },

                        method: {
                            type: String,
                            required: true,
                            enum: ['enem', 'transfer', 'proof', 'new_title'],
                        },

                        approved: {
                            type: Boolean,
                            default: false
                        }
                    },

                    class: {
                        type: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true,
                                index: true
                            },

                            name: {
                                type: String,
                                required: true
                            }
                        },
                        // required: false
                    },

                    enemScore: {
                        type: Number,
                        // required: false
                    },

                    // Fala se já foi enviado
                    sent_email_registration: {
                        type: Boolean,
                        default: false
                    },

                    // Fala se já foi enviado
                    sent_email_enrollment: {
                        type: Boolean,
                        default: false
                    },

                    // Fala se a aprovação foi forçada
                    forcedApproved: {
                        type: Boolean,
                        default: false
                    },

                    // Situação do aluno no processo seletivo
                    status: {
                        type: String,
                        enum: [
                            // Aguardando aprovação
                            'waiting',
                            // Cancelado
                            'canceled',
                            // Pagou e passou no processe seletivo
                            'approved',
                            // Reprovado
                            'disapproved'
                        ],
                        default: 'waiting',
                        required: true
                    },

                    cancel: {
                        date: Date,
                        reason: String,
                        userName: String,
                        userId: mongoose.SchemaTypes.ObjectId,
                    },

                    charge: {
                        type: {
                            _id: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true,
                                index: true
                            },
                            checkoutLink: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            // Multiplicar por 100 quando for moeda
                            selectiveProcessFeeAmount: {
                                type: Number,
                                required: true
                            },
                            isPayment: {
                                type: Boolean,
                                default: false
                            }
                        },
                        // required: false
                    },

                    quiz: {
                        type: {
                            type: String,
                            enum: [
                                'Prova Agendada',
                                'Prova Online'
                            ],
                            // required: false
                        },

                        // Se foi o estudante que finalizou a prova
                        finishedByStudent: {
                            type: Boolean,
                            default: false
                        },

                        // Data de quando iniciou e quando terminou para finalizar a prova
                        dateStart: {
                            type: Date,
                            // required: false
                        },

                        dateEnd: {
                            type: Date,
                            // required: false
                        },

                        // Se já foi corrigido automaticamente
                        wasFixedAutomatically: {
                            type: Boolean,
                            default: false
                        },

                        // Percentual de acerto
                        hitPercentage: {
                            type: Number,
                            // required: false
                        },

                        // Questoes do aluno
                        questions: [
                            {
                                questionId: {
                                    type: mongoose.SchemaTypes.ObjectId,
                                    // required: false
                                },

                                name: {
                                    type: String,
                                    // required: false
                                },

                                utterance: {
                                    type: String,
                                    // required: false
                                },

                                typeOfAnswer: {
                                    type: String,
                                    // required: false
                                },

                                files: [],

                                alternatives: [{
                                    alternativeId: {
                                        type: mongoose.SchemaTypes.ObjectId,
                                        // required: false
                                    },
                                    utteranceAlternative: {
                                        type: String,
                                        // required: false
                                    },
                                    correctAlternative: {
                                        type: Boolean,
                                        // required: false
                                    },
                                }],

                                responseOption: {
                                    type: mongoose.SchemaTypes.Mixed,
                                    // required: false
                                },

                                responseText: {
                                    type: String,
                                    // required: false
                                },

                                correct: {
                                    type: Boolean,
                                    // required: false
                                },
                            }
                        ],
                    },

                    documentsSent: {
                        message: {
                            type: String,
                            // required: false
                        },

                        files: [
                            {
                                url: {
                                    type: String,
                                    required: true
                                },
                                type: {
                                    type: String
                                },
                                name: {
                                    type: String,
                                    required: true
                                }
                            }
                        ]
                    }
                }
            ],
            // Data inicial para inscrição
            dateStart: {
                type: Date,
                required: true
            },

            // Data final para inscrição
            dateEnd: {
                type: Date,
                required: true
            }

        },
        options: {
            timestamps: true
        },
        indexes: [
            {
                fields: {
                    alias: 1,
                    unitId: 1,
                    studentCpf: 1,
                    chargeSelectiveProcessId: 1,
                    statusExam: 1
                },
                options: {}
            }
        ]
    }
};

module.exports = SelectiveProcessStudents;
