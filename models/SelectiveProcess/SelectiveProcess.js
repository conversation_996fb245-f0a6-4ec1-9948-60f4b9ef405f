const mongoose = require('mongoose');
let SelectiveProcess = {
    functions: {},
    database: {
        collection: 'SelectiveProcess',
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true,
            },

            cover: {
                type: String,
                default: 'assets/img/bolsas.png'
            },

            certifier: {
                _id: { // id da certificadora
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                },
                name: {// nome da certificadora
                    type: String,
                    required: true,
                },
            },

            polos: [{
                poloId: { // id do polo
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                },
                name: {// nome do polo
                    type: String,
                    required: true,
                },
            }],

            courses: [
                {
                    courseTypeName: { // id do curso
                        type: String,
                        required: true,
                    },

                    courseId: { // id do curso
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                    },

                    name: {// nome da unidade
                        type: String,
                        required: true,
                    },

                    vacancies: {// Quantidade de vagas
                        type: Number,
                        required: true,
                    },

                    modalityEnrolments: {
                        type: [String],
                        enum: ['enem', 'transfer', 'proof', 'new_title'],
                        required: true,
                    },

                    // Descrição para exibir no momento da avaliação, por exemplo se se for um envio de documento, descrever quais documentos tem que ser enviado. Vai funcionar como uma forma de instrução para o aluno.
                    descriptionForProof: {
                        type: String,
                        required: true
                    },

                    types: {
                        type: [String],
                        enum: ['Prova Agendada', 'Prova Online', 'Envio de Documento'],
                        required: true,
                    },

                    maximumDuration: { //tempo maximo de duracao da avaliação do vestibular
                        type: Number,
                        required: true,
                    },

                    chargeEnrolmentsfee: { //se vai cobrar taxa de matricula do curso ou não
                        type: Boolean,
                        required: true,
                    },

                    rateEnrolmentFeeAmount: { //valor da taxa de matricula
                        type: Number,
                        required: true,
                        default: 0
                    },

                    selectiveProcessFeeType: { //tipo de taxa do vestibular
                        type: String,
                        enum: ['noCharge', 'enrolmentFee', 'personalized'],
                        required: true,
                    },

                    selectiveProcessFeeAmount: { //valor da taxa do vestibular
                        type: Number,
                        required: true,
                    },

                    chargeType: {
                      type: String
                    },

                    // Optei por não salvar todo o quiz e fazer uma busca devido ao tamanho maximo do documento bson e a quantidade de aninhamentos que pode existir https://docs.mongodb.com/manual/reference/limits/#operationshttps://docs.mongodb.org/manual/reference/%20operador/agrega%C3%A7%C3%A3o/classifica%C3%A7%C3%A3o/#%20sort-memory-limit dessa forma será necessário uma consulta separada para retornar a avaliação.
                    groupQuestion: {
                        _id: {
                            type: String,
                            required: true,
                        },
                        name: {
                            type: String,
                            required: true,
                        },
                        openQuestion: {
                            type: Number,
                            // required: false,
                        },
                        archiveQuestion: {
                            type: Number,
                            required: true,
                        },
                        closeQuestion: {
                            type: Number,
                            required: true,
                        },
                    },

                    classes: [
                        {
                            classId: { // id da turma
                                type: mongoose.SchemaTypes.ObjectId,
                                // required: false,
                            },
                            name: {// nome da turma
                                type: String,
                                // required: false,
                            },
                        }
                    ]
                }

            ],

            description: { // Descrição para exibir para o aluno sobre o processo seletivo
                type: String,
                required: true,
            },

            dateStart: { // Data inicial para inscrição
                type: Date,
                required: true,
            },

            dateEnd: {  // Data final para inscrição
                type: Date,
                required: true,
            },

            autoCorrection: {
                type: Boolean,
                default: false
            },
            //Percentual para aprovação
            percentageApproval: {
                type: Number,
            },

            visibility: { //Se esta disponivel para acesso seu estar logado.
                type: String,
                enum: ['public', 'internal'],
                default: 'public'
            },

            situationAfterEnrollment: { // Situação após pagamento da taxa de inscrição do processo seletivo
                type: String,
                enum: ['pre_register', 'matriculate'],
                required: true,
            },

            email_registration: {// Email enviado após a inscrição do candidato
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            },

            email_enrollment: { // Email enviado após a inscrição do candidato
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            },

            isActive: { // Informa se o processe seletivo esta ativo
                type: Boolean,
                required: true,
            },
        },
        indexes: [
            {
                fields: {
                    poloId: 1,
                    isActive: 1,
                    visibility: 1,
                },
                options: {}
            }
        ]
    }
};

module.exports = SelectiveProcess;
