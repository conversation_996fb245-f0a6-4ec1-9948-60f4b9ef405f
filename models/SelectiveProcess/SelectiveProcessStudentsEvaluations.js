const mongoose = require('mongoose');

let SelectiveProcessStudents = {
  functions: {},
  database: {
    collection: 'SelectiveProcessStudentsEvaluations',
    connection: 'database_piaget',
    fields: {
      _processSelectiveId: mongoose.SchemaTypes.ObjectId,
      _studentId: mongoose.SchemaTypes.ObjectId,
      _courseId: mongoose.SchemaTypes.ObjectId,
      maximumDuration: Number,
      descriptionForProof: String,
      closedQuestions: [mongoose.SchemaTypes.Mixed],
      openQuestions: [mongoose.SchemaTypes.Mixed],
      archiveQuestions: [mongoose.SchemaTypes.Mixed],
      startedAt: Date,
      finishedAt: Date,
      answers: {
        closedQuestions: [mongoose.SchemaTypes.Mixed],
        openQuestions: [mongoose.SchemaTypes.Mixed],
        archiveQuestions: [mongoose.SchemaTypes.Mixed],
      }
    },
    options: {
      timestamps: true
    }
  }
};

module.exports = SelectiveProcessStudents;
