const mongoose = require('mongoose');

let Teachers = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Teachers',
        fields    : {
            roles: {
                type: [{
                    type: String,
                    enum: [
                        'teacher',
                        'tutor',
                    ],
                    required: true,
                }],
                required: true,
            },
            _userId         : {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            email: {
                type: String,
                // required: false
            },
            _certifiersAlias: {
                type    : [String],
                required: true,
                index   : true
            },
            name            : {
                type    : String,
                required: true,
                index   : true
            },
            cpf             : {
                type    : String,
                required: true
            },
            rg              : String,
            dispatcher      : String,
            phone           : String,
            cellPhone       : String,
            birthDate       : Date,
            nationality     : String,
            civilStatus     : {
                type: String,
                enum: [
                    'married',
                    'single',
                    'divorced'
                ]
            },
            _formationAlias : {
                type    : String,
                required: true
            },
            courseName      : String,
            formations: {
                type: [{
                    _id: false,
                    _formationAlias: String,
                    courseName: String,
                }],
            },
            specialty       : {
                type : [String],
                index: true
            },
            specialties: {
                type: [{
                    _id: false,
                    _formationAlias: String,
                    courseName: String,
                }],
            },
            address         : {
                street    : String,
                number    : String,
                complement: String,
                zone      : String,
                zip       : String,
                city      : String,
                uf        : String
            },
            _disciplinesName: {
                type: [String],
                required: true,
                index: true
            },
            _disciplines: {
                type: [{
                    _id: mongoose.Types.ObjectId,
                    name: String
                }],
                required: true,
                index: true
            },
            note            : String,
            isActive        : {
                type    : Boolean,
                // required: false,
                default : false
            },
            files: {
                type: [{
                    _id: false,
                    file: String,
                    description: String,
                    fileType: String
                }],
            },
            lattesLink : {
                type: String
            },
        },
        indexes   : [
            {
                fields : {
                    name: 'text'
                },
                options: {}
            },
            {
                fields : {
                    cpf: 1
                },
                options: {
                    unique: 'Já existe um professor com este cpf'
                }
            }
        ],
        options   : {
            timestamps: true
        }
    }
};

module.exports = Teachers;
