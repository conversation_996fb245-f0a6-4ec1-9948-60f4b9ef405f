const mongoose = require('mongoose');
const {SchemaTypes} = require("mongoose");

let TeacherPortalLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'TeacherPortalLogs',
        fields    : {
            ip: {
                type: String,
                required: true,
            },
            _userId         : {
              type    : mongoose.SchemaTypes.ObjectId,
              required: true
            },
            _teacherId         : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            teacherName: {
                type: String,
                required: true
            },
            action: {
              type: String,
              required: true
            },
            metadata: {
              type: SchemaTypes.Mixed,
              // required: false
            },
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = TeacherPortalLogs;
