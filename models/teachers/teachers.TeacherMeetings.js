const mongoose = require("mongoose");
const {SchemaTypes} = require("mongoose");

let PresentialEvaluations = {
    database: {
        connection: 'database_piaget',
        collection: 'TeacherMeetings',
        fields: {
            name: {
                type: String,
                required: true,
            },
            status: {
                type: String,
                enum: ['not_started', 'running', 'finished', 'canceled'],
                default: 'not_started'
            },
            finishedAt: Date,
            conferenceProvider: {
                type: String,
            },
            conferenceHostUrl: {
                type: String,
            },
            conferenceId: {
                type: String,
            },
            recordChecked: {
                type: Boolean,
                default: false
            },
            conferenceRecordings: [
                {
                    name: {
                        type: String,
                        required: true,
                    },
                    url: {
                        type: String,
                        required: true,
                    }
                }
            ],
            _teacherId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            },
            _disciplineId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            },
            _groupingId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            _classId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            date: {
                type: Date,
                required: true,
            },
            students: {
                type: [{
                    _id: SchemaTypes.ObjectId,
                    _enrolmentId: SchemaTypes.ObjectId,
                    name: String,
                    url: String,
                    clicked: {
                        type: Boolean,
                        default: false
                    },
                }],
                required: true,
            },
            isActive: {
                type: Boolean,
                default: true
            }
        },
    }
};

module.exports = PresentialEvaluations;
