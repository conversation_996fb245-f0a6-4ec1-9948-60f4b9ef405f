const mongoose = require('mongoose');
const  AuthService  = require('../../services/Auth.service');
const { _getModels }  = require('../../services/Utils');
const request = require('request');

let Users = {
    functions: {
        hashPasswordCreate: function (next) {
            try {
                if (this.password){
                    AuthService.hash(this.password)
                        .then(hashedPassword => {
                            this.password = hashedPassword;

                            return next();
                        })
                        .catch(err => {
                            return next(Error(err));
                        });
                } else return next();
            } catch (e) {console.error(e);
                 request({
                    method: 'POST',
                    json: true,
                    uri: "https://discord.com/api/webhooks/1184599402250244248/ePZ5keP37v3paMi3qkY78gcnRnuXf_3ia0UUykVvHaD-Z6JbIhptOUjjq8Dr2ghoxeer",
                    body: {
                        username: 'cronBot',
                        avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
                        content: "Erro na modelagem Users hashPasswordCreate" + e.toString(),
                    }
                });

                return next();
            }
        },
        hashPasswordFindUpdate: function (next) {
           try {
               if (((this._update || {}).$set || {}).password)
                   AuthService.hash(this._update.$set.password)
                       .then(hashedPassword => {
                           this._update.$set.password = hashedPassword;

                           return next();
                       })
                       .catch(err => {
                           return next(Error(err));
                       });
               else return next();
           } catch (e) {
              console.error(e);
               request({
                   method: 'POST',
                   json: true,
                   uri: "https://discord.com/api/webhooks/1184599402250244248/ePZ5keP37v3paMi3qkY78gcnRnuXf_3ia0UUykVvHaD-Z6JbIhptOUjjq8Dr2ghoxeer",
                   body: {
                       username: 'cronBot',
                       avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
                       content: "Erro na modelagem Users hashPasswordFindUpdate" + e.toString(),
                   }
               });

               return next();
           }
        },
        deleteAclIdIfAdminCreate: function (next) {
            if (this.isAdmin && this._aclId)
                this._aclId = [];

            return next();
        },
        deleteAclIdIfAdminFindUpdate: function (next) {
            if (((this._update || {}).$set || {}).isAdmin)
                this._update.$set._aclId = [];

            return next();
        },
        propagateFieldChanges: async function(user, next) {
            try {
                const models = _getModels.call(this, 'Users');
                const {Partners} = models;

                await Partners.findOneAndUpdate(
                    {
                        _userId: user._id,
                    },
                    {
                        $set: {
                            email: user.email,
                        }
                    }
                );

            } catch(err) {
                request({
                    method: 'POST',
                    json: true,
                    uri: "https://discord.com/api/webhooks/1184599402250244248/ePZ5keP37v3paMi3qkY78gcnRnuXf_3ia0UUykVvHaD-Z6JbIhptOUjjq8Dr2ghoxeer",
                    body: {
                        username: 'cronBot',
                        avatar_url: 'https://storage.googleapis.com/prominaserp/slack_notifications/lyrinha.png',
                        content: "Erro na modelagem Users propagateFieldChanges:101  " + err.toString(),
                    }
                });

                return next();
            } finally {
                next();
            }
        }
    },
    database: {
        collection: 'Users',
        connection: 'database_piaget',
        fields: {
            name          : {
                type    : String,
                required: true
            },
            email         : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Já existe uma credencial com este email'
            },
            password      : {
                type     : String,
                minlength: 5,
                required : true
            },
            externalLogins: [
                {
                    _id   : false,
                    origin: {
                        type     : String,
                        required : true,
                        lowercase: true
                    },
                    id    : {
                        type    : String,
                        required: true
                    }
                }
            ],
            _aclId        : {
                type    : [mongoose.SchemaTypes.ObjectId],
                // required: false,
                default : []
            },
            meta          : {
                type    : Object,
                // required: false,
                default : {}
            },
            isVerified    : {
                type    : Boolean,
                // required: false,
                default : false
            },
            isAdmin       : {
                type    : Boolean,
                // required: false,
                default : false
            },
            isAdminProtocol: {
                type    : Boolean,
                // required: false,
                default : false
            },
            isEnabled     : {
                type    : Boolean,
                // required: false,
                default : true
            },
            lastChangePassword: {
                type    : Date,
                // required: false,
            },
            avatar: {
                type: String,
                // required: false,
                default: null
            },
            hasLoginByCode: {
                type: Boolean,
                // required: false
            }
        }
    }
};

Users.database.pre = {
    save: [Users.functions.hashPasswordCreate, Users.functions.deleteAclIdIfAdminCreate],
    update: [Users.functions.hashPasswordFindUpdate, Users.functions.deleteAclIdIfAdminFindUpdate],
    findOneAndUpdate: [Users.functions.hashPasswordFindUpdate, Users.functions.deleteAclIdIfAdminFindUpdate]
};



Users.database.post = {
    save: [
        Users.functions.propagateFieldChanges,
    ],
    update: [],
    findOneAndUpdate: [
        Users.functions.propagateFieldChanges,
    ],
};

module.exports = Users;
