const mongoose = require('mongoose');

let Acls = {
    functions: {},
    database: {
        collection: 'Acls',
        fields    : {
            name      : {
                type    : String,
                required: true
            },
            alias     : {
                type     : String,
                required : true,
                lowercase: true,
                unique   : 'Acl já existe'
            },
            _endpoints: [mongoose.Schema.Types.ObjectId],
            _modules  : [mongoose.Schema.Types.ObjectId],
        },
        indexes   : [
            {
                fields : {
                    name     : 1,
                    _endpoint: 1,
                    _modules : 1
                },
                options: {
                    unique: 'Já existe uma ACL com esta configuração'
                }
            }
        ]
    }
};

module.exports = Acls;
