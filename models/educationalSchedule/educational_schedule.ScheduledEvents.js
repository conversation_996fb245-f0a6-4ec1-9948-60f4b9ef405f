const mongoose = require('mongoose');

let ScheduledEvents = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ScheduledEvents',
        fields    : {
            _eventId                   : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            inscriptionsClosedAt       : {
                type    : Date,
                required: true
            },
            date                       : {
                type    : Date,
                required: true
            },
            vacancies                  : {
                type    : Number,
                required: true
            },
            tags                       : {
                type    : [String],
                required: true
            },
            location                   : {
                street    : {
                    type    : String,
                    required: true
                },
                number    : {
                    type    : Number,
                    required: true
                },
                complement: {
                    type    : String,
                    // required: false,
                    default : null
                },
                zone      : {
                    type    : String,
                    required: true
                },
                reference : {
                    type    : String,
                    // required: false,
                    default : null
                },
                state     : {
                    type    : String,
                    required: true
                },
                city      : {
                    type    : String,
                    required: true
                },
                zip       : {
                    type    : Number,
                    required: true
                },
                lat       : {
                    type    : Number,
                    required: true
                },
                lng       : {
                    type    : Number,
                    required: true
                }
            },
            _partner                   : {
                cpf: {
                    type    : String,
                    required: true
                }
            },
            _teacher                   : {
                cpf: {
                    type    : String,
                    // required: false,
                    default : null
                }
            },
            _disciplines               : {
                type    : [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            budget                     : [
                {
                    _id     : false,
                    _itemId : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    quantity: {
                        type    : Number,
                        required: true
                    },
                    amount  : {
                        type    : Number,
                        required: true
                    }
                }
            ],
            studentCanPerformEvaluation: {
                type    : Boolean,
                required: true
            },
            isPresenceListLaunched     : {
                type    : Boolean,
                // required: false,
                default : false
            },
            presenceListArchives       : {
                type    : [String],
                // required: false,
                default : null
            },
            status                     : [
                {
                    _id        : false,
                    name       : {
                        type    : String,
                        enum    : [
                            'in_creation',
                            'waiting_approval',
                            'approved',
                            'canceled',
                            'finalized'
                        ],
                        required: true
                    },
                    description: {
                        type    : String,
                        required: true
                    },
                    _userId    : {
                        type    : mongoose.SchemaTypes.ObjectId,
                        // required: false,
                        default : null
                    },
                    _userName  : {
                        type    : String,
                        // required: false,
                        default : null
                    },
                    _userType  : {
                        type    : String,
                        // required: false,
                        default : null
                    },
                    _department: {
                        type    : String,
                        // required: false,
                        default : null
                    },
                    changedAt  : {
                        type    : Date,
                        required: true
                    }
                }
            ]
        }
    }
};

module.exports = ScheduledEvents;
