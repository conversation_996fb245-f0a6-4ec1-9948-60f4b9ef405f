const mongoose = require('mongoose');

let ScheduledEvaluations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ScheduledEvaluations',
        fields    : {
            _enrolmentId : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _evaluationId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            scheduleType : {
                type    : String,
                enum    : ['scheduled_event', 'pole_availability', 'partner_availability'],
                required: true
            },
            _referenceId : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            date         : {
                type    : Date,
                required: true
            },
            checkIn      : {
                type    : Boolean,
                // required: false,
                default : null
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = ScheduledEvaluations;
