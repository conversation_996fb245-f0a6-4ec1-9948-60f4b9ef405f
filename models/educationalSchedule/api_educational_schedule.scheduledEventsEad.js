const mongoose = require('mongoose');

let ScheduledEventsEaD = {
    functions: {},
    database: {
        collection: 'ScheduledEventsEaD',
        connection: 'database_piaget',
        fields: {
            event: {
                type: {
                    _id:  mongoose.SchemaTypes.ObjectId,
                    name: String
                },
                required: true
            },
            courses: {
                type: [String],
                required: true
            },
            typeCourses: {
                type: [String],
                required: true
            },
            certifier: {
                type: [String],
                required: true
            },
            disciplineNames: {
                type: [String],
                // required: false
            },
            date: {
                type: Date,
                required: true
            },
            notification: {
                type: {
                    notifyBeforeEvent: Boolean,
                    notification_types: [String],
                    notificationTypes: [String],
                    textNotify       : String,
                    notifyStudents   : Boolean
                }
            },
            platform: {
                type: {
                    link: String,
                    platformName: String
                },
                required: true
            },
            forceNotify: {
                type: Boolean
            },
            status: {
                type: String,
                enum: [
                    'in_progress',
                    'canceled',
                    'finished'
                ],
                default: 'in_progress'
            },
            description: {
                type: String,
                required: true
            },
            history: {
                type: [
                    {
                        _id         : false,
                        name        : {
                            type    : String
                        },
                        description : {
                            type    : String,
                            // required: false
                        },
                        _userId     : {
                            type    : mongoose.SchemaTypes.ObjectId,
                            required: true,
                            default : null
                        },
                        _userName   : {
                            type    : String,
                            required: true,
                            default : null
                        },
                        _userType   : {
                            type    : String,
                            required: true,
                            default : null
                        },
                        _department : {
                            type    : String,
                            // required: false,
                            default : null
                        },
                        changedAt   : {
                            type    : Date,
                            required: true
                        }
                    }
                ]
            }
        }
    }
}

module.exports = ScheduledEventsEaD;
