const mongoose = require('mongoose');

let ScheduledEventParticipants = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ScheduledEventParticipants',
        fields    : {
            _scheduledEventId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            hasNotification: {
                type: Boolean,
                default: false
            },
            _enrolmentId     : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            isActive:       {
                type    : Boolean,
                required: true,
                default : true
            },
            checkIn          : {
                type    : Boolean,
                // required: false,
                default : null
            },
            studentData: {
                type: {
                   _id         : false,
                  _enrolmentId : mongoose.SchemaTypes.ObjectId,
                  name         : String,
                  cpf          : String,
                  dateStart    : Date,
                  createdAt    : Date,
                  course       : String,
                  typeCourse   : String,
                  email        : String,
                  cellPhone    : String,
                  whatsApp     : String,
                  phone        : String,
                  _userId      : mongoose.SchemaTypes.ObjectId
                }
            },
            pendingPayment: {
                type    : Boolean,
                required: true,
                default : false
            },
            configNotify: {
               type: {
                    notifyStudents    : Boolean,
                    notifyBeforeEvent : Boolean,
                    textNotify        : Boolean,
                    notificationTypes : [String]
               }
            },
            eventData: {
                name               : String,
                _id                : mongoose.SchemaTypes.ObjectId,
                typeCourses        : [String],
                courses            : [String],
                disciplineNames    : [String],
                enrolmentCreatedAt : Date,
                enrolmentDateStart : Date
            },
            eventType: {
                type   : String,
                default: 'presential',
                enum   : [
                    'presential',
                    'ead'
                ]
            },
            resendNotification: {
                type  : Boolean,
                default: false
            }
        },
        indexes   : [
            {
                fields : {
                    _scheduledEventId: 1,
                    _enrolmentId     : 1
                },
                options: {
                    unique: 'already_participating'
                }
            }
        ]
    }
};

module.exports = ScheduledEventParticipants;
