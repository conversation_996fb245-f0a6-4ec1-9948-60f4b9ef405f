const mongoose = require('mongoose');

let ScheduledIndicationTeachers = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ScheduledIndicationTeachers',
        fields    : {
            _enrolmentId     : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            name          : {
                type    : String,
                required: true
            },
            email          : {
                type     : String,
                required : true,
                lowerCase: true
            },
            phone          : {
                type    : String,
                required: true
            },
            status: {
                enum: [
                    'waiting_analysis',
                    'create_event',
                    'canceled'
                ],
                type: String,
                default: 'waiting_analysis'
            },
            formation     : {
                type    : String,
                // required: false
            },
            history: [
                {
                    status: {
                        enum: [
                            'waiting_analysis',
                            'create_event',
                            'canceled'
                        ],
                        type: String,
                        required: true
                    },
                    _userName: {
                        type: String,
                        required: true
                    },
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    launchedAt: {
                        type: Date,
                        required: true
                    },
                    description: String
                }
            ]
        }
    }
};

module.exports = ScheduledIndicationTeachers;
