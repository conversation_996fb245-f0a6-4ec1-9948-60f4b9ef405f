const mongoose = require('mongoose');

let Disciplines = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Disciplines',
        fields    : {
            name      : {
                type    : String,
                required: true
            },
            sagahDiscipline: {
                type: String,
                // required: false
            },
            teacher: {
                type:{
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            tutor: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                // required: false
            },
            description: {
                type: String,
                // required: false
            },
            objective: {
                type: String,
                // required: false,
                default: null
            },
            program: {
                type: String,
                // required: false,
                default: null
            },
            workload: {
                type: Number,
                // required: false
            },
            tags: {
                type: Array,
                // required: false
            },
            activities: [
                {
                    type: {
                        type: String,
                        enum: [
                            'regular',
                            'recuperation'
                        ],
                        required: true
                    },
                    modality: {
                        type: String,
                        enum: [
                            'online',
                            'presential'
                        ],
                        required: true
                    },
                    maxDuration: {
                        type: Number,
                        // required: false
                    },
                    model: {
                        type: String,
                        enum: [
                            'form',
                            'evaluation',
                            'upload',
                            'participation',
                            'sagah'
                        ],
                        required: true
                    },
                    isFinalTest: {
                        type: Boolean,
                        required: true
                    },
                    modelMeta: {
                        numQuestions: {
                            type: Number,
                            // required: false
                        },
                        enunciation: {
                            type: String,
                            // required: false
                        },
                        ltiUrl: {
                            type: String,
                            // required: false
                        },
                        ltiTitle: {
                            type: String,
                            // required: false
                        }
                    }
    
                }
            ],
    
            _forums: {
                type: [
                    {
                        forumId: mongoose.SchemaTypes.ObjectId,
                        title: {
                            type: String,
                            required: true
                        },
                        description: {
                            type: String,
                            default: ''
                        },
                        files: [
                            {
                                url: {
                                    type: String,
                                    required: true
                                },
                                name: {
                                    type: String,
                                    required: true
                                },
                                type: {
                                    type: String,
                                    required: true
                                }
                            }
                        ],
                        allPostVisibleToStudent: {
                            // Estudante poderam ver mensagens de outros estudantes
                            type: Boolean,
                            required: true
                        },
                        type: {
                            // Informativo ou avaliativo
                            type: String,
                            required: true
                        },
                        grade: {
                            // Nota máxima que pode ser alcançada
                            type: Number,
                            default: null
                        }
                    }
                ],
                default: null
            },
    
            coursewareType: {
                type: String
            },
            _coursewares: {
                type: [mongoose.SchemaTypes.ObjectId],
                // required: false
            }
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = Disciplines;
