const {SchemaTypes} = require("mongoose");
const {disciplineNamesSchema} = require("../coursewares/DisciplineNames");

let Disciplines = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'DisciplinesDeleted',
        fields    : {
            deletedAt: {
                type: Date,
                default: Date.now
            },
            deletedBy: {
                _userId: SchemaTypes.ObjectId,
                _userName: String,
            },
            ...disciplineNamesSchema
        },
        options   : {
            timestamps: true
        }
    }
};

module.exports = Disciplines;
