const mongoose = require('mongoose');

let SettingsPartner = {
    functions: {},
    database: {
        collection: 'SettingsPartner',
        connection: 'database_leads_partner',
        fields    : {
            _cpf    : {
                type    : String,
                required: true,
                unique  : 'duplicate_settings'
            },
            isActive: {
                type: Boolean,
                default: true
            },
            data    : {
                type    : mongoose.Schema.Types.Mixed,
                required: true
            }
        }
    }
};

module.exports = SettingsPartner;
