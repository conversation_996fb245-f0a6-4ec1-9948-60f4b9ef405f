const mongoose = require('mongoose');

let LogsDistribution = {
    functions: {},
    database: {
        collection: 'LogsDistribution',
        connection: 'database_leads_partner',
        fields: {
            quantityPerCollaborator: {
                type: Number,
                required: true
            },
            leadsBeforeDistribution: {
                type: Number,
                required: true
            },
            leadsAfterDistribution: {
                type: Number,
            },
            finished: {
                type: Boolean,
                default: false
            },
            sender: {
                _brokerId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _brokerName: {
                    type: String
                }
            },
            receiver: [{
                _brokerId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _brokerName: {
                    type: String
                },
                leads: [
                    {
                        _leadId: {
                            type: mongoose.SchemaTypes.ObjectId,
                            required: true
                        },
                        _leadName: {
                            type: String
                        }
                    }
                ]
            }],
        }
    }
};

module.exports = LogsDistribution;
