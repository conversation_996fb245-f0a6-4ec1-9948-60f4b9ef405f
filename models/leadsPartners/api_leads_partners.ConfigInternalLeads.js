const mongoose = require('mongoose');

let ConfigInternalLeads = {
    functions: {},
    database: {
        collection: 'ConfigInternalLeads',
        connection: 'database_leads_partner',
        fields: {
            name: {
                type: String,
                required: true
            },
            _cpf: {
                type: String,
                required: true,
                unique: 'duplicate_broker'
            },
            _partnerId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            certifiers: [String],
            modalities: [String],
            location: {
                state: {
                    type: String,
                    required: true
                },
                city: {
                    type: String,
                    required: true
                }
            },
            typeOfLead: {
                type: String,
                enum: [
                    'others',
                    'daily',
                    'all'
                ],
                required: true
            },
            score: {
                type: Number,
                // required: false,
                default: 1
            },
            startingScore: {
                type: Number,
                // required: false,
                default: 0
            },
            isActive: {
                type: Boolean,
                // required: false,
                default: true
            },
            validity: {
                type: Date,
                // required: false
            },
            limitLeadsOfOther: {
                type: Number,
                required: true
            },
            limitLeadsOfDay: {
                type: Number,
                required: true
            },
            dailyLeadsOther: {
                type: Number,
                // required: false,
                default: 0
            },
            dailyLeadsDay: {
                type: Number,
                // required: false,
                default: 0
            }
        }
    }
};

module.exports = ConfigInternalLeads;
