let Activities = {
    functions: {},
    database: {
        collection: 'Activities',
        connection: 'database_leads_partner',
        fields    : {
            name    : {
                type    : String,
                required: true,
                trim: true
            },
            onLaunch: {
                isObservationRequired: {
                    type    : Boolean,
                    required: true
                },
                changeStatusTo       : {
                    type: String,
                    enum: [
                        'lead',
                        'waiting_broker',
                        'waiting_rate_enrolment',
                        'in_progress',
                        'finished'
                    ]
                }
            },
            isActive: {
                type: Boolean,
                default: true
            },
            _cpf: {
                type: String,
                // required: false
            },
            isVisibleToPartner:{
                type: Boolean,
                default: true
            }
        },
        indexes   : [
            {
                fields : {
                    'name'      : 1,
                    '_cpf': 1
                },
                options: {
                    unique: 'Atividade já existe.'
                }
            }
        ]
    }
};

module.exports = Activities;
