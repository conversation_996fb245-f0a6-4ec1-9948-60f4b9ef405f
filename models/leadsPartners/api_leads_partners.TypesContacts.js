let TypesContacts = {
    functions: {},
    database: {
        collection: 'TypesContacts',
        connection: 'database_leads_partner',
        fields    : {
            name    : {
                type    : String,
                required: true,
                trim: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            _cpf: {
                type: String,
                // required: false
            },
            isVisibleToPartner:{
                type: Boolean,
                default: true
            }
        },
        indexes   : [
            {
                fields : {
                    'name'      : 1,
                    '_cpf': 1
                },
                options: {
                    unique: 'Tipo de contato já existe.'
                }
            }
        ]
    }
};

module.exports = TypesContacts;
