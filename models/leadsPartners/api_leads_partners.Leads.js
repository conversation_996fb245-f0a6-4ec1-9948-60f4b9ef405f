const mongoose = require('mongoose');

let Leads = {
    functions: {
        checkStatus: async function (next) {
            const status = {
                'Em Progresso': 'in_progress',
                'Aguardando atendimento': 'waiting_broker',
                Finalizado: 'finished'
            };
            try {
                if (this?.status) {
                    this.status = status[status] || status
                }else if(this?._update?.$set?.status) {
                    this._update.$set.status = status[this._update.$set.status] || this._update.$set.status
                }

            } catch (e) {
                console.log(e.toString());
            }
            return next();
        }
    },
    database: {
        collection: 'Leads',
        connection: 'database_leads_partner',
        fields: {
            origins: [
                {
                    _id: false,
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    name: {
                        type: String,
                        required: true
                    },
                    // Cursos gratuitos, site ucam, site unica, landing page coronga, landing page black friday, etc
                    utm: {
                        source: String,
                        campaign: String,
                        medium: String,
                        term: String,
                        content: String,
                        gclid: String,
                        fbclid: String
                    }
                }
            ],
            originReceiver: [
                {
                    _id: false,
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    type: {
                        type: String,
                        required: true
                    },
                    sentBy: {
                        _brokerId: mongoose.SchemaTypes.ObjectId,
                        _brokerName: String,
                    },
                    relationshipId: mongoose.SchemaTypes.ObjectId,
                }
            ],
            name: String,
            email: {
                type: String
            },
            cellPhone: String,
            partner: {
                _cpf: {
                    type: String,
                    required: true
                },
                name: {
                    type: String
                },
                _partnerId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                }
            },
            whatsApp: String,
            location: {
                state: String,
                city: String
            },
            ranking: {
                type: String,
                enum: [
                    'checkout',
                    'lead'
                ],
                default: 'lead'
            },
            _idInternalLead: mongoose.SchemaTypes.ObjectId,
            deletedAt:Date,
            _brokerId: mongoose.SchemaTypes.ObjectId,
            _brokerName: String,
            scheduledTo: Date,
            activities: [
                {
                    _id: false,
                    type: {
                        type: String,
                        required: true
                    },
                    _activityName: {
                        type: String,
                        required: true
                    },
                    date: {
                        type: Date,
                        // required: false,
                        default: Date.now()
                    },
                    _brokerId: mongoose.SchemaTypes.ObjectId,
                    observation: String,
                    observationImage: String,
                    metadata: mongoose.SchemaTypes.Mixed,
                    _brokerName: String
                }
            ],
            interests: {
                type: [
                    {
                        _id: false,
                        date: {
                            type: Date,
                            // required: false,
                            default: Date.now()
                        },
                        course: {
                            system: {
                                type: String,
                                required: true
                            },
                            certifier: {
                                type: String,
                                required: true
                            },
                            type: {
                                type: String,
                                required: true
                            },
                            area: String,
                            name: String,
                            workload: Number,
                            _courseId: String
                        },
                        payment: {
                            method: String,
                            paid: {
                                type: Boolean,
                                // required: false,
                                default: false
                            },
                            enrolment: {
                                installments: Number,
                                dueDate: Date
                            },
                            course: {
                                installments: Number,
                                monthliesDay: Number,
                                value: Number
                            }
                        },
                        enrolmentId: String,
                        isDone: {
                            type: Boolean,
                            // required: false,
                            default: false
                        }
                    }
                ],
                // required: false,
                default: []
            },
            status: {
                type: String,
                enum: [
                    'waiting_broker',
                    'in_progress',
                    'finished'
                ],
                // required: false,
                default: 'waiting_broker'
            },
            // Data que o lead foi selecionado
            linkingDate: {
                type: Date,
                // required: false
            },
            address: {
                street: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                number: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                complement: {
                    type: String,
                    // required: false,
                    allowNull: true
                },
                zone: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                zip: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                city: {
                    type: String,
                    // required: false,
                    allowNull: false
                },
                uf: {
                    type: String,
                    // required: false,
                    allowNull: false
                }
            },
            birthDate: {
                type: Date,
                // required: false,
                allowNull: false
            },
            metadata: mongoose.SchemaTypes.Mixed

            /*
            Dados do metadata (todos opcionais):
            -parametros utm
            -parametros geolocation
            *parametros indicação premiada
            -qualquer dado extra relevante
            */
        },
    }
};


Leads.database.pre = {
    save: [
        Leads.functions.checkStatus,
    ],
    findOneAndUpdate: [
        Leads.functions.checkStatus,
    ],
};

module.exports = Leads;
