const mongoose = require('mongoose');

let SystemSettings = {
    functions: {},
    database: {
        collection: 'SystemSettings',
        connection: 'database_leads_partner',
        fields    : {
            name    : {
                type    : String,
                required: true,
                unique  : 'duplicate_settings'
            },
            description    : {
                type    : String,
                required: true
            },
            isActive: {
                type: Boolean,
                required: true
            },
            data    : {
                type    : mongoose.Schema.Types.Mixed,
                required: true
            }
        }
    }
};

module.exports = SystemSettings;
