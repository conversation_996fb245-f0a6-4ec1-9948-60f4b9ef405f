const mongoose = require('mongoose');

let LeadsLogs = {
    functions: {},
    database: {
        collection: 'LeadsLogs',
        connection: 'database_leads_partner',
        fields: {
            _leadId         : mongoose.SchemaTypes.ObjectId,
            status          : String,
            _brokerId       : mongoose.SchemaTypes.ObjectId,
            scheduledTo     : Date,
            distributedAt   : Date,
            partner : {
                _cpf: {
                    type: String,
                    required: true
                },
                name: {
                    type: String
                },
                _partnerId:{
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                }
            },
            origin          : {
                _id             : false,
                date            : Date,
                name            : String,
                utm             : {
                    source          : String,
                    campaign        : String,
                    medium          : String,
                    term            : String,
                    content         : String,
                    gclid           : String,
                    fbclid          : String
                }
            },
            interest        : {
                date            : Date,
                isDone          : Boolean,
                payment         : {
                    paid            : Boolean
                },
                course          : {
                    system          : String,
                    certifier       : String,
                    area            : String,
                    name            : String,
                    type            : {
                        type            : String
                    }
                }
            },
            activity          : {
                _id                 : mongoose.SchemaTypes.ObjectId,
                _activityName       : String,
                observation         : String,
                observationImage    : String,
                type                : {
                type                : String
                }
            }
        }
    }
};

module.exports = LeadsLogs;
