const mongoose = require("mongoose");
let LogApiKeys = {
    database: {
        collection: "LogApiKeys",
        connection: 'database_piaget',
        fields: {
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
            },
            apiKeyId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            action: String,
            _userName: String,
            _userId: mongoose.SchemaTypes.ObjectId
        },
    },
};

module.exports = LogApiKeys;