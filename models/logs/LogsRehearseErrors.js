const mongoose = require('mongoose');
let LogsRehearseErrors = {
    database: {
        collection: 'LogsRehearseErrors',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _chargeId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            _chargeOrderId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false
            },
            _indication: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                index: true
            },
            _enrolment: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                index: true
            },
            _charge: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            _chargeOrder: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            rehearse: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            method: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            errorInRehearse: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            parcels: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            rates: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            status: {
                type: String,
                default: 'pending'
            }
        }
    }
};

module.exports = LogsRehearseErrors;