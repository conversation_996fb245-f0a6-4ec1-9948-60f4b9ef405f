const mongoose = require('mongoose');

let LogEmails = {
    database: {
        collection: 'LogEmails',
        fields: {
            status: {
                type: String,
                enum: [
                    'waiting_send',
                    'processing',
                    'success',
                    'error',
                ],
                default: 'processing',
                required: true
            },
            body: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            response: {
                type: mongoose.SchemaTypes.Mixed,
            },
            enrolment: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                cpf: {
                    type: String,
                    required: true
                },
                registryCourse: {
                    type: {
                        course: {
                            type: {
                                _name: {
                                    type: String,
                                    required: true
                                },
                                _typeName: {
                                    type: String,
                                    required: true
                                },
                                _certifierName: {
                                    type: String,
                                    required: true
                                },
                                acronym: {
                                    type: String,
                                    required: true
                                }
                            },
                            required: true
                        }
                    },
                    required: true,
                },
            },
            student: {
                _id: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                cpf: {
                    type: String,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                },
                email: {
                    type: String,
                    required: true
                },
                _userId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true
                },
            }
        }
    }
};

module.exports = LogEmails;
