const mongoose = require('mongoose');
let LogSendSms = {
    database: {
        collection: 'LogSendSms',
        connection: 'database_piaget',
        fields: {
            phones: {
                type: [String],
                // required: false
            },
            content: mongoose.SchemaTypes.Mixed,
            user: {
                type: mongoose.SchemaTypes.Mixed
            },
            enrolment: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId
                    },
                    name: {
                        type: String,
                        // required: false
                    },
                    typeName: {
                        type: String,
                        // required: false
                    },
                    certifier: { 
                        type: String,
                        // required: false
                    },
                    acronym: { 
                        type: String,
                        // required: false
                    },
                }
            },
            dateSend: {
                type: Date,
                // required: false,
            },
            status: {
                type: String,
                default: 'processing'
            },
            responseApi : {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            error: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            success: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            tries: {
                type: Number,
                // required: false
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            userType: {
                type: String,
                // required: false
            },
            notificationType: {
                type: String,
                // required: false,
                default: 'user'
            }
        },
        options: {
            timestamp: true
        }
    }
};
module.exports = LogSendSms;
