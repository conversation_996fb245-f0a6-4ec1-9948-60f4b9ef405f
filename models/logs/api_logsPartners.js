const mongoose = require('mongoose');

let LogPartners = {
    database: {
        collection: 'LogPartners',
        fields: {
            action: String,
            collection: String,
            body: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            before: {
                type: mongoose.SchemaTypes.Mixed,
            },
            after: {
                type: mongoose.SchemaTypes.Mixed,
            },
            user: {
                type: mongoose.SchemaTypes.Mixed,
            }
        }
    }
};

module.exports = LogPartners;
