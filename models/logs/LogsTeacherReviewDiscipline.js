const mongoose = require('mongoose');
let LogsTeacherReviewDiscipline = {
    database: {
        collection: 'LogsTeacherReviewDiscipline',
        connection: 'database_piaget',
        fields: {
            _disciplineIdinClass: {
                type: mongoose.SchemaTypes.ObjectId,
                index: true
            },
            _classId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            _groupingId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type: String,
                required: true
            }
        }
    }
};
module.exports = LogsTeacherReviewDiscipline;
