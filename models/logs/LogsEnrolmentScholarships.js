const mongoose = require('mongoose');
let LogEnrolmentsScholarships = {
    database: {
        collection: 'LogEnrolmentsScholarships',
        fields: {
            _enrolmentScholarshipId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type: String,
                required: true
            },
            body: {
                type: mongoose.SchemaTypes.Mixed
            }
        }
    }
};
module.exports = LogEnrolmentsScholarships;
