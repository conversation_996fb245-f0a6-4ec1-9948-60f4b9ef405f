const mongoose = require('mongoose');
let LogsSelectedOptionalDisciplines = {
    database: {
        collection: 'LogsSelectedOptionalDisciplines',
        connection: 'database_piaget',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            before: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            after: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};
module.exports = LogsSelectedOptionalDisciplines;
