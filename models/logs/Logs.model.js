const mongoose = require('mongoose');

let Logs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Logs',
        fields    : {
            _endpointId     : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            endpoint  : {
                _apiAlias: {
                    type     : String,
                    required : false,
                    lowercase: true,
                    default  : null
                },
                path: {
                    type     : String,
                    required : false,
                    lowercase: true,
                    default  : null
                }
            },
            _userId         : {
                type    : mongoose.SchemaTypes.ObjectId,
                // required: false,
                default : null
            },
            requestOrigin  : {
                type     : String,
                required : false,
                lowercase: true,
                default  : null
            },
            isCachedResponse: {
                type    : Boolean,
                // required: false,
                default : false
            },
            request         : {
                query  : {
                    type     : String,
                    required : false,
                    lowercase: true,
                    default  : null
                },
                body   : {
                    type    : String,
                    // required: false,
                    default : null
                },
                headers: {
                    type    : mongoose.SchemaTypes.Mixed,
                    // required: false,
                    default : null
                }
            },
            response        : {
                status : {
                    type    : Number,
                    required: true
                },
                body   : {
                    type    : String,
                    // required: false,
                    default : null
                },
                headers: {
                    type    : mongoose.SchemaTypes.Mixed,
                    // required: false,
                    default : null
                }
            }
        }
    }
};

module.exports = Logs;
