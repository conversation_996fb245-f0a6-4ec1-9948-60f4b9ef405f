const mongoose = require('mongoose');

let LogChangeDisciplineEnrolment = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogChangeDisciplineEnrolment',
        fields: {
            enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            course: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                required: true
            },
            user: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                required: true
            },
            oldDiscipline: mongoose.SchemaTypes.Mixed,
            newDiscipline: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = LogChangeDisciplineEnrolment;
