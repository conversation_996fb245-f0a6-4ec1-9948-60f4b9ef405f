const mongoose = require('mongoose');

let LogsTemplateCertificates = {
    functions: {},
    database: {
        collection: 'LogsTemplateCertificates',
        fields    : {
            before: {
                type    : mongoose.SchemaTypes.Mixed,
            },
            after: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type    : String,
                required: true
            },
            _userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            }
        }
    }
};

module.exports = LogsTemplateCertificates;
