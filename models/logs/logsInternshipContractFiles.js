const mongoose = require('mongoose');

let LogsInternshipContractFiles = {
    functions: {},
    database: {
        collection: 'LogsInternshipContractFiles',
        fields    : {
            before: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            after: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type    : String,
                required: true
            },
            _userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _internshipCompanies: {
                type    : mongoose.SchemaTypes.ObjectId,
            },
            _internshipCompaniesContractId: {
                type    : mongoose.SchemaTypes.ObjectId,
            }
        }
    }
};

module.exports = LogsInternshipContractFiles;