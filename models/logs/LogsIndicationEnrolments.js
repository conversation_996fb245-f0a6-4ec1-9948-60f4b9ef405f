const mongoose = require('mongoose');

let LogsIndicationEnrolments = {
    database: {
        functions: {},
        collection: 'LogsIndicationEnrolments',
        connection: 'database_piaget',
        fields: {
            user: {
                _userId: {
                    type: String,
                    required: true
                },
                _userName: {
                    type: String,
                    required: true
                },
                _userType: {
                    type: String,
                    required: true
                },
                launchedAt: {
                    type: Date,
                    default: Date.now
                }
            },
            oldIndication: {
                type: Array
            },
            newIndication: {
                type: Array
            },
            enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            studentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            createdAt: {
                type: Date,
                default: Date.now
            }
        }
    }
};

module.exports = LogsIndicationEnrolments;