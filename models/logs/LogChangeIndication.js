const mongoose = require('mongoose');

let LogChangeIndication = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogChangeIndication',
        fields: {
            enrolmentId: {
                type: [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            user: {
                type: {
                    _id: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true
                    },
                    name: {
                        type: String,
                        required: true
                    }
                },
                required: true
            },
            before: mongoose.SchemaTypes.Mixed,
            after: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = LogChangeIndication;