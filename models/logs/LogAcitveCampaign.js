const mongoose = require('mongoose');

let LogActiveCampaign = {
    functions: {
        parseEnrolmentId: function (next) {
            if (Array.isArray(this.enrolmentId)) {
                this.enrolmentId = this.enrolmentId.map(id => mongoose.Types.ObjectId(id));
            } else {
                this.enrolmentId = mongoose.Types.ObjectId(this.enrolmentId);
            }
            next();
        },
    },
    database: {
        connection: 'database_piaget',
        collection: 'LogActiveCampaign',
        fields: {
            enrolmentId: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
                index: true
            },
            response: mongoose.SchemaTypes.Mixed,
            error: mongoose.SchemaTypes.Mixed,
            sendFields: mongoose.SchemaTypes.Mixed,
            isSuccess: Boolean,
            dealId: String,
            tag: {
                name: String,
                id: String
            },
            activeId: String,
        },
        options: {
            timestamp: true
        },
        pre: {}
    }
};

LogActiveCampaign.database.pre = {
    save: [
        LogActiveCampaign.functions.parseEnrolmentId,
    ],
    findOneAndUpdate: [
        LogActiveCampaign.functions.parseEnrolmentId,
    ]
};

module.exports = LogActiveCampaign;
