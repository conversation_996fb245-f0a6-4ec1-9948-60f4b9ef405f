const mongoose = require('mongoose');

let LogsCommunicationModels = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogsCommunicationModels',
        fields: {
            old: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            current: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
            },
            user: {
                _userId: {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                },
                _userName: {
                    type: String,
                    required: true,
                },
                _userType: {
                    type: String,
                    required: true,
                }
            }
        }
    }
};

module.exports = LogsCommunicationModels;
