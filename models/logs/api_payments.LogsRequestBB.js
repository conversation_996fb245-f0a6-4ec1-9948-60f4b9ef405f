const mongoose = require('mongoose');
let LogsRequestBB = {
    functions: {},
    database: {
        collection: 'LogsRequestBB',
        connection: 'database_payments',
        fields: {
            _billingId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            ourNumber: {
                type: String,
                default: null
            },
            headers: {
                type: mongoose.SchemaTypes.Mixed,
            },
            path: {
                type: String,
            },
            body: {
                type: mongoose.SchemaTypes.Mixed,
            },
            response: {
                type: String,
                default: '-'
            },
            isSucess: {
                type: Boolean,
                default: false
            },
            method: {
                type: String,
            },
            query: {
                type: mongoose.SchemaTypes.Mixed,
            },
            assignor: {
                type: mongoose.SchemaTypes.Mixed
            }
        },
        indexes: [
        ]
    }
}

module.exports = LogsRequestBB;