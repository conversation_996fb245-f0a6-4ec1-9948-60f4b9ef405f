const mongoose = require('mongoose');

let Messages = {
    database: {
        collection: 'Messages',
        connection: 'database_piaget',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userType: {
                type: String,
                enum: [
                    'student',
                    'partner',
                    'teacher',
                    'employer'
                ],
                required: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _disciplineId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _certifierName: {
                type: String,
                // required: false
            },
            icon: {
                type: String,
                // required: false
            },
            title: {
                type: String,
                required: true
            },
            message: {
                type: String,
                required: true
            },
            sender: {
                _userId: mongoose.SchemaTypes.ObjectId,
                _userName: {
                    type: String,
                    // required: false
                },
                _departamentName: {
                    type: String,
                    // required: false
                }
            },
            metadata: mongoose.SchemaTypes.Mixed,
            isRead: {
                type: Boolean,
                // required: false,
                default: false
            }
        },
        options: {
            timestamp: true
        }
    }
};
module.exports = Messages;
