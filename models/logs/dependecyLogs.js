const mongoose = require('mongoose');
let DependecyLogs = {
    database: {
        collection: 'DependecyLogs',
        fields: {
            action: String,
            disciplineId: {
                type: mongoose.SchemaTypes.ObjectId,
                index: true
            },
            dependecyConfig: {
                type: mongoose.SchemaTypes.Mixed,
            },
            enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};
module.exports = DependecyLogs;
