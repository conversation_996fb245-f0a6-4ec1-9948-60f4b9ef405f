const mongoose = require('mongoose');
const LogsTemplateContents = {
  database: {
    collection: 'LogsTemplateContents',
    connection: 'database_piaget',
    fields: {
      _templateId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
        index: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        allowNull: true,
        index: true,
      },
      _userName: {
        type: String,
      },
      _userType: {
        type: String,
      },
      beforeUpdate: {
        type: mongoose.SchemaTypes.Mixed,
      },
      afterUpdate: {
        type: mongoose.SchemaTypes.Mixed,
      },
      requestBody: {
        type: mongoose.SchemaTypes.Mixed,
        required: true,
      },
      action: {
        type: String,
        required: true,
      },
    },
  },
};
module.exports = LogsTemplateContents;
