const mongoose = require('mongoose');
let LogDiscounts = {
    database: {
        collection: 'LogDiscounts',
        fields: {
            _discountId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type: String,
                required: true
            }
        }
    }
};
module.exports = LogDiscounts;
