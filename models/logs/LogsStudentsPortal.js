const mongoose = require("mongoose");
let LogsStudentPortal = {
  database: {
    collection: "LogsStudentPortal",
    fields: {
      student: {
        type: {
          ip: {
            type: String,
            // required: false,
          },
          name: {
            type: String,
            required: true,
          },
          cpf: {
            type: String,
            required: true,
          },
        },
        required: true,
      },
      beforeUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      afterUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      requestBody: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      token: {
        type: String,
        required: true,
      },
      action: {
        type: [
          {
            date: Date,
            title: String,
            path: String,
            description: String
          }
        ],
        required: true,
      },
      origin: {
        type: String,
        required: true,
      },
    },
  },
};
module.exports = LogsStudentPortal;
