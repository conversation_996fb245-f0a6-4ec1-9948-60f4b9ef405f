const mongoose = require('mongoose');

let LogsCertificatesSolicitations = {
    functions: {},
    database: {
        collection: 'LogsCertificatesSolicitations',
        fields: {
            before: {
                type: mongoose.SchemaTypes.Mixed,
                required: function() {
                    return !this.action.includes('create');
                },
            },
            after: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
            },
            action: {
                type: [{
                    type: String,
                    required: true,
                    enum: [
                        'create',
                        'update',
                        'approve',
                        'canceled',
                        'change-charge',
                        'change-shipping',
                    ]
                }],
                required: true,
            },
            _userName: {
                type: String,
                required: true,
            },
            _userType: {
                type: String,
                required: true,
                enum: [
                    'student',
                    'employer',
                    'computer',
                ],
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            }
        }
    }
};

module.exports = LogsCertificatesSolicitations;
