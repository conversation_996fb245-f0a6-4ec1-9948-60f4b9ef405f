const mongoose = require('mongoose');
let LogCollections = {
    database: {
        collection: 'LogCollections',
        fields: {
            _collectionName: {
                type: String,
                required: true,
                index: true
            },
            _itemId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};
module.exports = LogCollections;
