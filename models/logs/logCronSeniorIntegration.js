const mongoose = require('mongoose');
let LogCronSeniorIntegration = {
    database: {
        collection: 'LogCronSeniorIntegration',
        connection: 'database_piaget',
        fields: {
            in: {
                type: {
                    boleto: [mongoose.SchemaTypes.ObjectId],
                    pix: [mongoose.SchemaTypes.ObjectId],
                    creditCard: [mongoose.SchemaTypes.ObjectId],
                    debitCard: [mongoose.SchemaTypes.ObjectId]
                },
                default: {
                    boleto: [],
                    pix: [],
                    creditCard: [],
                    debitCard: []
                }
            },
            out: {
                type: [mongoose.SchemaTypes.ObjectId],
                default: []
            },
            cancel: {
                type: {
                    boleto: [mongoose.SchemaTypes.ObjectId],
                    pix: [mongoose.SchemaTypes.ObjectId],
                    creditCard: [mongoose.SchemaTypes.ObjectId],
                    debitCard: [mongoose.SchemaTypes.ObjectId]
                },
                default: {
                    boleto: [],
                    pix: [],
                    creditCard: [],
                    debitCard: []
                }
            }
        }
    }
};
module.exports = LogCronSeniorIntegration;
