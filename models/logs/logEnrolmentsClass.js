const mongoose = require('mongoose');
let LogEnrolmentsClass = {
    database: {
        collection: 'LogEnrolmentsClass',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            }
        }
    }
};
module.exports = LogEnrolmentsClass;
