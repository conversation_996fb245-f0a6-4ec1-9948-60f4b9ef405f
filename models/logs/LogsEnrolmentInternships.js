const mongoose = require('mongoose');

let LogsEnrolmentInternships = {
    database: {
        functions: {},
        collection: 'LogsEnrolmentInternships',
        connection: 'database_piaget',
        fields: {
            user: {
                _userId: {
                    type: String,
                    required: true
                },
                _userName: {
                    type: String,
                    required: true
                },
                _userType: {
                    type: String,
                    required: true
                },
            },
            before: {
                type: mongoose.SchemaTypes.Mixed
            },
            after: {
                type: mongoose.SchemaTypes.Mixed
            },
            enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            enrolmentInternshipId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            action: String
        }
    }
};

module.exports = LogsEnrolmentInternships;