const mongoose = require("mongoose");
let LogsAva = {
  database: {
    collection: "LogsAva",
    fields: {
      student: {
        type: {
          ip: {
            type: String,
            required: true,
          },
          name: {
            type: String,
            required: true,
          },
          cpf: {
            type: String,
            required: true,
          },
        },
        required: true,
      },
      enrolment: {
        type: {
          _id: {
            type: mongoose.SchemaTypes.ObjectId,
            required: true,
          },
          course: {
            type: {
              _name: {
                type: String,
                required: true,
              },
              _certifierName: {
                type: String,
                required: true,
              },
              _typeName: {
                type: String,
                required: true,
              },
              acronym: {
                type: String,
                required: true,
              },
              workload: {
                type: Number,
                required: true,
              }
            },
            required: true,
          },
        },
        required: true,
      },
      beforeUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      afterUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      requestBody: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      token: {
        type: String,
        required: true,
      },
      action: {
        type: [
          {
            date: Date,
            title: String,
            path: String,
            description: String
          }
        ],
        required: true,
      },
      origin: {
        type: String,
        required: true,
      },
    },
  },
};
module.exports = LogsAva;
