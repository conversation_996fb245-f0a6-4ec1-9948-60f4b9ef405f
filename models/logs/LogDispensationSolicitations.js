const mongoose = require('mongoose');
let LogDispensationSolicitations = {
  database: {
    collection: "LogDispensationSolicitations",
    fields: {
      beforeUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      afterUpdate: {
        type: mongoose.SchemaTypes.Mixed,
        // required: false,
      },
      action: {
        type: String,
        required: true,
      },
      _userId: {
        type: mongoose.SchemaTypes.ObjectId,
        required: true,
      },
      _userName: {
        type: String,
        required: true,
      },
      _userType: {
        type: String,
        required: true,
      },
    },
  },
};
module.exports = LogDispensationSolicitations;
