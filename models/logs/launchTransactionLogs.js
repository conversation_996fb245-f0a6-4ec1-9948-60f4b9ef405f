const mongoose = require('mongoose');

let LaunchTransactionLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LaunchTransactionLogs',
        fields    : {
            cpf     : {
                type    : String,
                required: true
            },
            value   : {
                type    : Number,
                required: true
            },
            reason  : String,
            user    : {
                _userId  : {
                    type    : mongoose.SchemaTypes.ObjectId,
                    required: true
                },
                _userType: {
                    type    : String,
                    required: true,
                    enum    : [
                        'student',
                        'partner',
                        'teacher',
                        'employer',
                        'computer'
                    ]
                },
                _userName: {
                    type    : String,
                    required: true
                }
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = LaunchTransactionLogs;
