const mongoose = require("mongoose");
let LogsPoloContract = {
    database: {
        collection: "LogsPoloContract",
        fields: {
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            poloContractId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
            },
            action: String,
            _userName: String,
            _userId: String
        },
    },
};
module.exports = LogsPoloContract;
