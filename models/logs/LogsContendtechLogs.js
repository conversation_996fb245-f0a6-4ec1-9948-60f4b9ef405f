const mongoose = require('mongoose');
let LogsContendtech = {
    database: {
        collection: 'LogsContendtech',
        connection: 'database_piaget',
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            responseApi: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            data: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            sendBody: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type: String,
                required: true
            },
            send: {
                type: Boolean,
                required: true
            }
        }
    }
};
module.exports = LogsContendtech;
