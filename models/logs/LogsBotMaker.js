const mongoose = require('mongoose');

let LogsBotMaker = {
    database: {
        connection: 'database_piaget',
        collection: 'LogsBotMaker',
        fields: {
            response : mongoose.SchemaTypes.Mixed,
            error    : mongoose.SchemaTypes.Mixed,
            body     : mongoose.SchemaTypes.Mixed,
            contacts : mongoose.SchemaTypes.Mixed,
            isSuccess: Boolean,
            headers  : mongoose.SchemaTypes.Mixed,
            url      : String,
        },
        options: {
            timestamp: true
        },
        pre: {}
    }
};

LogsBotMaker.database.pre = {
};

module.exports = LogsBotMaker;
