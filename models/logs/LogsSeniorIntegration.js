const mongoose = require('mongoose');

let  LogsSeniorIntegration = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogsSeniorIntegration',
        fields: {
            financialMoviment: mongoose.SchemaTypes.Mixed,
            uuid: {
                type: Number,
                required: true
            },
            isProduction: {
                type: Boolean,
                required: true
            },
            dataSend: {
                type: String,
                required: true
            },
            CodRot: {
                type: String,
                required: false
            },
        }
    }
};

module.exports = LogsSeniorIntegration;
