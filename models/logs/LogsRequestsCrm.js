const mongoose = require("mongoose");
let LogsRequestsCrm = {
    database: {
        collection: "LogsRequestsCrm",
        fields: {
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
            },
            headers: {
                type: mongoose.SchemaTypes.Mixed,
            },
            path: {
                type: String,
            },
            body: {
                type: mongoose.SchemaTypes.Mixed,
            },
            response: {
                type: String,
                default: '-'
            },
            isSucess: {
                type: Boolean,
                default: false
            },
            method: {
                type: String,
            }
        },
    },
};

module.exports = LogsRequestsCrm;