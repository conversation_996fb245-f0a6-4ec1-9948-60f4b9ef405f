const mongoose = require("mongoose");
let LogsTeacherPortal = {
    database: {
        collection: "LogsTeacherPortal",
        fields: {
            user: {
                type: {
                    ip: {
                        type: String,
                        required: true,
                    },
                    name: {
                        type: String,
                        required: true,
                    },
                    cpf: {
                        type: String,
                        required: true,
                    },
                    _userId: {
                        type: mongoose.SchemaTypes.ObjectId,
                        required: true,
                    },
                    roles: {
                        type: [String]
                    }
                },
                required: true,
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
            },
            token: {
                type: String,
                required: true,
            },
            action: {
                type: [
                    {
                        date: Date,
                        title: String,
                        path: String,
                        description: String
                    }
                ],
                required: true,
            },
            origin: {
                type: String,
                required: true,
            },
        },
    },
};
module.exports = LogsTeacherPortal;
