const mongoose = require('mongoose');
let LogsConfigurations = {
    database: {
        collection: 'LogsConfigurations',
        connection: 'database_piaget',
        fields: {
            _configId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            action: {
                type: String,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};

module.exports = LogsConfigurations;