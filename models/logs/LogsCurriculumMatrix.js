const mongoose = require('mongoose');
let LogsCurriculumMatrix = {
    database: {
        collection: 'LogsCurriculumMatrix',
        connection: 'database_piaget',
        fields: {
            _curriculumMatrixId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            action: {
                type: String,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            }
        }
    }
};
module.exports = LogsCurriculumMatrix;
