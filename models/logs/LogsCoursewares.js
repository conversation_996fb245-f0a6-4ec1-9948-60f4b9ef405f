const mongoose = require('mongoose');
let LogsCoursewares = {
    database: {
        collection: 'LogsCoursewares',
        connection: 'database_piaget',
        fields: {
            _coursewareId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
                index: true
            },
            action: {
                type: String,
                required: true,
                index: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String
            },
            _userType: {
                type: String
            },
            beforeUpdate: {
                type: mongoose.SchemaTypes.Mixed
            },
            requestBody: {
                type: mongoose.SchemaTypes.Mixed
            },
            afterUpdate: {
                type: mongoose.SchemaTypes.Mixed,
            }
        }
    }
};
module.exports = LogsCoursewares;
