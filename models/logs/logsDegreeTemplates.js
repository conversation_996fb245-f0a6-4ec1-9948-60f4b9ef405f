const mongoose = require('mongoose');

let LogsDegreeTemplates = {
    functions: {},
    database: {
        collection: 'LogsDegreeTemplates',
        fields    : {
            before: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            after: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type    : String,
                required: true
            },
            _userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            }
        }
    }
};

module.exports = LogsDegreeTemplates;
