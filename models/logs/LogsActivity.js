const mongoose = require('mongoose');
let LogsActivity = {
    database: {
        collection: 'LogsActivity',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true,
            },
            _userName: {
                type: String,
                required: true,
            },
            activity: {
                type: mongoose.SchemaTypes.Mixed,
                required: true,
            },
            modifiedEnrolmentsIds:[
                {
                    type: mongoose.SchemaTypes.ObjectId,
                    required: true,
                }
            ],
            action: {
                type: String,
                required: true,
            }
        }
    }
};
module.exports = LogsActivity;
 
