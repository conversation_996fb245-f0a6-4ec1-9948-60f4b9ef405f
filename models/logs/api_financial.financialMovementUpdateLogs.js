const mongoose = require('mongoose');

let FinancialMovementUpdateLogs = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'FinancialMovementUpdateLogs',
        fields: {
            before:  {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            after : {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _userName: {
                type: String,
                required: true
            },
            body: {
                type: mongoose.SchemaTypes.Mixed,
                required: true
            },
            _financialMovementId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            }
        }
    }
};

module.exports = FinancialMovementUpdateLogs;
