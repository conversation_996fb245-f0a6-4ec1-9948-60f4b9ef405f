const mongoose = require('mongoose');

let LogsInternshipCompanies = {
    functions: {},
    database: {
        collection: 'LogsInternshipCompanies',
        connection: 'database_piaget',
        fields    : {
            before: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            after: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type    : String,
                required: true
            },
            _userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _internshipCompanies: {
                type    : mongoose.SchemaTypes.ObjectId,
            }
        }
    }
};

module.exports = LogsInternshipCompanies;