const mongoose = require('mongoose');

let LogsInternshipsDocuments = {
    functions: {},
    database: {
        collection: 'LogsInternshipsDocuments',
        fields    : {
            before: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            after: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            body: {
                type    : mongoose.SchemaTypes.Mixed,
                required: true
            },
            action: {
                type    : String,
                required: true
            },
            _userName     : {
                type    : String,
                required: true
            },
            _userId      : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _internshipDocumentId: {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            }
        }
    }
};

module.exports = LogsInternshipsDocuments;
