const mongoose = require('mongoose');

let LogsCommission = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'LogsCommission',
        fields    : {
            _partnerId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _partnerName: {
                type    : String,
                // required: false,
                allowNull: true
            },
            _partnerCpf: {
                type    : String,
                // required: false,
                allowNull: true
            },
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true
            },
            _userName: {
                type    : String,
                // required: false,
                allowNull: true
            },
            _userCpf: {
                type    : String,
                // required: false,
                allowNull: true
            },
            ip: {
                type: String,
                // required: false,
                allowNull: true
            },
            origin: {
                type: String,
                // required: false,
                allowNull: true
            },
            startDate: {
                type: {
                    old: {
                        type: Date,
                        // required: false,
                        allowNull: null
                    },
                    new: {
                        type: Date,
                        // required: false,
                        allowNull: null
                    }
                },
                // required: false
            },
            validateMonth: {
                type: {
                    old: {
                        type: Number,
                        // required: false,
                        allowNull: null
                    },
                    new: {
                        type: Number,
                        // required: false,
                        allowNull: null
                    }
                },
                // required: false
            },
            commissionType: {
                type: {
                    old: {
                        type: String,
                        enum: ['priceRange', 'courseSubcategory', 'commission_by_modality'],
                        // required: false
                    },
                    new: {
                        type: String,
                        enum: ['priceRange', 'courseSubcategory', 'commission_by_modality'],
                        // required: false
                    }
                },
                // required: false
            },
            minRateEnrolment: {
                type: {
                    old: {
                        type: Number,
                        // required: false
                    },
                    new: {
                        type: Number,
                        // required: false
                    }
                },
                // required: false
            },
            subcategoryCommissions: {
                type: {
                    old: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                },
                                _courseSubcategoryName: {
                                    type: String
                                },
                                exceptions: {
                                    type: [
                                        {
                                            installment: {
                                                type: Number
                                            },
                                            commission: {
                                                type: Number
                                            }
                                        }
                                    ],
                                    // required: false,
                                    default: undefined
                                }
                            }
                        ],
                        // required: false
                    },
                    new: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                },
                                _courseSubcategoryName: {
                                    type: String
                                },
                                exceptions: {
                                    type: [
                                        {
                                            installment: {
                                                type: Number
                                            },
                                            commission: {
                                                type: Number
                                            }
                                        }
                                    ],
                                    // required: false,
                                    default: undefined
                                }
                            }
                        ],
                        // required: false
                    }
                },
                // required: false
            },
            bonus: {
                type: {
                    old: {
                        type: {
                            creditCard: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            boleto: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            debitCard: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            cardRecurrence: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            pix: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            }
                        },
                        // required: false
                    },
                    new: {
                        type: {
                            creditCard: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            boleto: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            debitCard: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            cardRecurrence: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            },
                            pix: {
                                maxInstallment: {
                                    type: Number,
                                    default: 0
                                },
                                value: {
                                    type: Number,
                                    default: 0
                                }
                            }
                        },
                        // required: false
                    }
                },
                // required: false
            },
            commissions: {
                type: {
                    old: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                },
                                exceptions: {
                                    type: [
                                        {
                                            installment: {
                                                type: Number
                                            },
                                            commission: {
                                                type: Number
                                            },
                                        }
                                    ],
                                    // required: false,
                                    default: undefined,
                                }
                            }
                        ],
                        // required: false
                    },
                    new: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                },
                                exceptions: {
                                    type: [
                                        {
                                            installment: {
                                                type: Number
                                            },
                                            commission: {
                                                type: Number
                                            },
                                        }
                                    ],
                                    // required: false,
                                    default: undefined,
                                }
                            }
                        ],
                        // required: false
                    }
                },
                // required: false
            },
            commissionMedium: {
                type: {
                    old: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    },
                    new: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    }
                },
                // required: false
            },
            commissionMinimum: {
                type: {
                    old: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    },
                    new: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    }
                },
                // required: false
            },
            taxEnrolments: {
                type: {
                    old: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    },
                    new: {
                        type: [
                            {
                                percentage: {
                                    type: Number
                                },
                                percentageToLinkPolo: {
                                    type: Number
                                },
                                percentageToLinkThirdPartyPolo: {
                                    type: Number
                                },
                                _certifierName: {
                                    type: String
                                },
                                _courseTypeName: {
                                    type: String
                                }
                            }
                        ],
                        // required: false
                    }
                },
                // required: false
            },
            rates: {
                type: {
                    old: {
                        type: {
                            loot: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            boletoLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            pixLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            creditCardLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            debitCardLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            }
                        },
                        // required: false
                    },
                    new: {
                        type: {
                            loot: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            boletoLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            pixLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            creditCardLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            },
                            debitCardLiquidation: {
                                type: {
                                    type: String,
                                    enum: [
                                        'value',
                                        'percentage'
                                    ],
                                    // required: false
                                },
                                value: {
                                    type: Number,
                                    // required: false
                                }
                            }
                        },
                        // required: false
                    }
                },
                // required: false
            },
            advanceInstallmentsContract : {
                type: {
                    old: {
                        type: {
                            loot: {
                                type: Boolean,
                                // required: false
                            }
                        },
                        // required: false
                    },
                    new: {
                        type: {
                            loot: {
                                type: Boolean,
                                // required: false
                            }
                        },
                        // required: false
                    }
                },
                // required: false
            },
    
            advanceInstallments : {
                type: {
                    old: {
                        type: {
                            loot: {
                                type: Boolean,
                                // required: false
                            }
                        },
                        // required: false
                    },
                    new: {
                        type: {
                            loot: {
                                type: Boolean,
                                // required: false
                            }
                        },
                        // required: false
                    }
                },
                // required: false
            },
            advanceInstallmentsConfig: {
                type: {
                    old: {
                        type: {
                            _userCreationId: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            percentageAmount: [
                                {
                                    installment: Number,
                                    value: Number
                                }
                            ],
                            maxAmountMonthly: {
                                type: Number,
                                required: true
                            }
                        },
                        // required: false
                    },
                    new: {
                        type: {
                            _userCreationId: {
                                type: mongoose.SchemaTypes.ObjectId,
                                required: true
                            },
                            percentageAmount: [
                                {
                                    installment: Number,
                                    value: Number
                                }
                            ],
                            maxAmountMonthly: {
                                type: Number,
                                required: true
                            }
                        },
                        // required: false
                    }
                },
                // required: false
            },
            _contractsId: {
                type: {
                    old: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false
                    },
                    new: {
                        type: mongoose.SchemaTypes.ObjectId,
                        // required: false
                    }
                },
                // required: false
            },
            archive: {
                type: {
                    old: {
                        type: mongoose.SchemaTypes.Mixed,
                        // required: false,
                        allowNull: true
                    },
                    new: {
                        type: mongoose.SchemaTypes.Mixed,
                        // required: false,
                        allowNull: true
                    }
                },
                // required: false
            }
        },
        indexes   : [
            {
                fields : {
                    name: 'text'
                },
                options: {}
            }
        ]
    }
};

module.exports = LogsCommission;
