const mongoose = require('mongoose');
let RateLimitLog = {
    database: {
        collection: 'RateLimitLog',
        connection: 'database_payments',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            history: [
                {
                    date: {
                        type: Date,
                        default: Date.now,
                    },
                    headers: {
                        type: mongoose.SchemaTypes.Mixed,
                        // required: false
                    },
                    origin: {
                        type: String,
                        enum: ['paymentCreditCard', 'zeroAuth'],
                    }
                }
            ],
            destiny: {
                type: String,
                enum: ['creditCard'],
            }
        }
    }
};

module.exports = RateLimitLog;