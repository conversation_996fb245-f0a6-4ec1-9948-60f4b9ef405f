const mongoose = require('mongoose');
let LogNotification = {
    database: {
        collection: 'LogNotification',
        connection: 'database_piaget',
        fields: {
            body: {
                type: Object,
                required: true
            },
            retry: {
                type: Boolean,
                // required: false,
                default: true
            },
            tries: {
                type: [
                    {
                        _id: false,
                        date: {
                            type: Date,
                            // required: false,
                            default: new Date()
                        },
                        success: {
                            type: Boolean,
                            // required: false
                        },
                        response: {
                            type: mongoose.SchemaTypes.Mixed,
                            // required: false
                        }
                    }
                ],
                // required: false,
                default: []
            }
        },
        options: {
            timestamp: true
        }
    }
};
module.exports = LogNotification;