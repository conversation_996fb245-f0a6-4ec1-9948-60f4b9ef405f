const mongoose = require('mongoose');

let CronLogs = {
    functions: {},
    database: {
        collection: 'CronLogs',
        connection: 'database_piaget',
        fields    : {
            cron       : {
                type    : String,
                required: true
            },
            name       : {
                type    : String,
                required: true
            },
            description: {
                type    : String,
                required: true
            },
            success    : {
                type    : Boolean,
                required: true
            },
            error      : {
                type    : String,
                // required: false,
                default : null
            },
            log        : {
                type    : mongoose.SchemaTypes.Mixed,
                // required: false,
                default : null
            },
            duration   : {
                type    : Number,
                required: true
            }
        }
    }
};

module.exports = CronLogs;
