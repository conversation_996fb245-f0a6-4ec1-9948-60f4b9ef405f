const mongoose = require('mongoose');
let ZeroAuthLogs = {
    database: {
        collection: 'ZeroAuthLogs',
        connection: 'database_payments',
        fields: {
            _userId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                allowNull: true,
                index: true
            },
            _userName: {
                type: String,
                // required: false
            },
            _userType: {
                type: String,
                // required: false
            },
            bodyRequest: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            headers: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false
            },
            isSuccess: {
                type: Boolean,
                // required: false
            },
            error: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false 
            },
            response: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false 
            },
            url: {
                type: String,
                // required: false
            }
        }
    }
};

module.exports = ZeroAuthLogs;