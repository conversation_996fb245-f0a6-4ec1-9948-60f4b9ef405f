const {SchemaTypes} = require("mongoose");
let TemplateDeclarations = {
    database: {
        collection: 'TemplateDeclarationsManual',
        connection: 'database_piaget',
        fields: {
            _certifierName: {
                type: String,
                required: true
            },
            _courseTypeName: {
                type: String,
                required: true
            },
            name: {
                type: String,
                required: true
            },
            content: {
                type: String,
                required: true
            },
            contentBack: {
                type: String,
                // required: false
            },
            header: {
                type: String,
                // required: false
            },
            headerSignaturePage: {
                type: String,
                // required: false
            },
            imageOfSignature: {
                type: String,
                // required: false
            },
            footer: {
                type: String,
                // required: false
            },
            signature: {
                type: String
            },
            _templateKeysId: {
              type: SchemaTypes.ObjectId,
              default: false
            },
            isActive: {
                type: Boolean,
                default: false
            }
        }
    }
};

module.exports = TemplateDeclarations;
