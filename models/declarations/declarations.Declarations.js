const mongoose = require('mongoose');
const {SchemaTypes} = require("mongoose");

let Declarations = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'Declarations',
        fields: {
            linkDeclaration: {
                type: String,
                // required: false
            },
            cpf: {
                type: String,
                required: true
            },
            name: {
                type: String,
                required: true
            },
            _enrolmentId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            _solicitationId: {
                type: mongoose.SchemaTypes.ObjectId
            },
            courseName: {
                type: String,
                required: true
            },
            certifier: {
                type: String,
                required: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            type: {
                type: String,
                required: true
            },
            extraData: SchemaTypes.Mixed
        }
    }
};

module.exports = Declarations;
