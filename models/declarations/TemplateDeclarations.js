let TemplateDeclarations = {
    database: {
        collection: 'TemplateDeclarations',
        connection: 'database_piaget',
        fields: {
            _certifierName: {
                type: String,
                required: true
            },
            _courseTypeName: {
                type: String,
                required: true
            },
            _solicitationTypeName: {
                type: String,
                required: true
            },
            content: {
                type: String,
                required: true
            },
            contentBack: {
                type: String,
                // required: false
            },
            header: {
                type: String,
                // required: false
            },
            headerSignaturePage: {
                type: String,
                // required: false
            },
            imageOfSignature: {
                type: String,
                // required: false
            },
            footer: {
                type: String,
                // required: false
            },
            signature: {
                type: String
            },
            isActive: {
                type: Boolean,
                default: false
            }
        }
    }
};

module.exports = TemplateDeclarations;
