const mongoose = require('mongoose');

let Departments = {
  functions: {},
  database: {
    collection: 'Departments',
    fields: {
      name: {
        type: String,
        required: true,
        unique: 'Este nome de departamento já existe'
      },
      alias: {
        type: String,
        required : true,
        lowercase: true,
        unique: 'Já existe um departamento com este alias'
      },
      isActive: {
        type: Boolean,
        default: false
      },
      protocolMatters: [
        {
          subject: {
            type: String,
            required: true,
          },
          description: {
            type: String,
            // required: false,
          },
          certifier: {
            type: [String],
            // required: false,
            default: []
          },
          courseType: {
            type: [String],
            // required: false,
            default: []
          },
          requireCertificateEmit: {
            type: String,
            // required: false,
          },
          validatePillars: {
            type: String,
            // required: false,
          },
          status: {
            type: String,
            // required: false,
          },
          price: {
            _chageTypeName: {
              type: mongoose.SchemaTypes.String,
              // required: false
            },
            _chageTypeAlias: {
              type: mongoose.SchemaTypes.String,
              // required: false
            },
            amount: {
              type: mongoose.SchemaTypes.Number,
              // required: false
            },
            daysValidate: {
              type: mongoose.SchemaTypes.Number,
              // required: false
            },
          },
          maxTime: {
            type: Number,
            // required: false
          },
          maxTimeWaitingStudentReturn: {
            type: Number,
            default: 7,
          },
          requestCanBeReopenedByStudent: {
            type: Boolean,
            default: false
          },
          maxTimeToReopenRequestByStudent: {
            unit: {
              type: String,
            },
            value: {
              type: Number,
            }
          },
          useDaysElapsedOfEnrolment: {
            type: Boolean,
          },
          daysElapsedOfEnrolment: {
            type: Number,
          },
          hasCreateCharges: {
            type: Boolean,
            // required: false
          }
        }
      ],
      responsible: {
        _id: mongoose.SchemaTypes.ObjectId,
        name: String
      }
    }
  }
};

module.exports = Departments;
