const mongoose = require('mongoose');
let Employers = {
    database: {
        collection: 'Employers',
        connection: 'database_piaget',
        fields    : {
            _userId         : {
                type    : mongoose.SchemaTypes.ObjectId,
                required: true
            },
            code            : {
                type    : String,
                unique  : 'Código já cadastrado',
                required: true
            },
            name            : {
                type    : String,
                required: true
            },
            cpf             : {
                type    : String,
                unique  : 'Já existe um cadastro com o mesmo CPF',
                required: true
            },
            cnpj            : {
                type    : String,
                // required: false
            },
            _departmentAlias: {
                type    : [String],
                required: true
            },
            birthDate       : {
                type    : Date,
                required: true,
                index   : true
            },
            corporate       : {
                branchLine: {
                    type    : Number,
                    // required: false,
                    index   : true,
                    default : null
                },
                email     : {
                    type    : String,
                    required: true,
                    index   : true,
                    unique  : 'Já existe um cadastro com este email'
                }
            },
            address         : {
                street    : {
                    type    : String,
                    // required: false
                },
                number    : {
                    type    : Number,
                    // required: false
                },
                complement: {
                    type: String
                },
                zone      : {
                    type    : String,
                    // required: false
                },
                zip       : {
                    type    : Number,
                    // required: false
                },
                city      : {
                    type    : String,
                    // required: false
                },
                uf        : {
                    type    : String,
                    // required: false
                }
            },
            isCommission    : {
                type    : Boolean,
                required: true,
                default : false
            },
            commissions     : [
                {
                    percentage     : {
                        type: Number
                    },
                    _certifierName : {
                        type: String
                    },
                    _courseTypeName: {
                        type: String
                    }
                }
            ],
            certifiers      : {
                type    : [String],
                // required: false,
                default : []
            },
            defer:{
                type: Boolean,
                default: false,
                required:false
            },
            configDefer: [
                {
                    certifierDefer: String,
                    courseTypesDefer: String,
                    priceDefer: Number
                }
            ],
            con:{
                type: Number,
                required:false
            },
            courseTypes    : {
                type    : [String],
                // required: false,
                default : []
            },
            isEnabled       : {
                type    : Boolean,
                // required: false,
                default : true
            },
            blipTeams: [
                {
                    name: String,
                    receiptTransfer: Boolean,
                    alias: String,
                    blipDepartment: String
                }
            ],
            metadata: mongoose.SchemaTypes.Mixed,
            renegociation: {
                type     : Boolean,
                allowNull: true,
                required : false,
                default  : false
            },
            renegociationValue: {
                type     : Number,
                allowNull: true,
                required : false,
                default  : 0
            },
            mutedSound: {
                type: Boolean,
                default: false
            }
        }
    }
};
module.exports = Employers;
