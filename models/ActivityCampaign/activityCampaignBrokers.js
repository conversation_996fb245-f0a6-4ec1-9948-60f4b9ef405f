const mongoose = require('mongoose');

let ActivityCampaignBrokers = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ActivityCampaignBrokers',
        fields    : {
            _employerId: mongoose.SchemaTypes.ObjectId,
            email: String,
            name: String,
            activityCampaignUserId: String,
            cpf: String,
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = ActivityCampaignBrokers;