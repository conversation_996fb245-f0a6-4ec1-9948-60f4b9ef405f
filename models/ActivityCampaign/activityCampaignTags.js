const mongoose = require('mongoose');

let ActivityCampaignTags = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ActivityCampaignTags',
        fields    : {
            name: {
                type    : String,
                required: true
            },
            certifier: {
                type: {
                    name: String,
                    alias: String
                },
                default: {
                    name: 'all',
                    alias: 'all'
                }
            },
            typeName: {
                type: {
                    name: String,
                    alias: String
                },
                default: {
                    name: 'all',
                    alias: 'all'
                }
            },
            activityCampaignId: {
                type    : String,
                required: true            
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed
            },
            action: {
                type: String
            },
            changeStatus: {
                type: Boolean,
                default: false
            },
            nextStatus: {
                type: Number
            },
            groupData: {
                type: {
                    name: String,
                    activeCampaignId: String
                }
            },
            stageData: {
                type: {
                    name: String,
                    activeCampaignId: String
                }
            },
        }
    }
};

module.exports = ActivityCampaignTags;
