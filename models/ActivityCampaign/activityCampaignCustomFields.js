const mongoose = require('mongoose');

let ActivityCampaignCustomFields = {
    functions: {},
    database: {
        connection: 'database_piaget',
        collection: 'ActivityCampaignCustomFields',
        fields    : {
            path: String,
            name: String,
            typeData: String,
            activityCampaignFieldId: String,
            _tagAction: [String],
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            compareValue: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = ActivityCampaignCustomFields;