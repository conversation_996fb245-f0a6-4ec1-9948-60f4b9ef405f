const mongoose = require('mongoose');

let Postbacks = {
    functions: {},
    database: {
        collection: 'Postbacks',
        connection: 'database_payments',
        fields: {
            uri: {
                type: String,
                required: true
            },
            body: {
                type: Object,
                required: true
            },
            headers: {
                type: Object,
                // required: false,
                default: {}
            },
            retry: {
                type: Boolean,
                // required: false,
                default: true
            },
            triesNumber: {
                type: Number,
                // required: false,
                default: 0
            },
            lastTry: {
                type: Date,
                // required: false,
                default: null
            },
            tries: {
                type: [
                    {
                        _id: false,
                        date: {
                            type: Date,
                            // required: false,
                            default: new Date()
                        },
                        success: {
                            type: Boolean,
                            required: true
                        },
                        response: {
                            type: mongoose.SchemaTypes.Mixed,
                            required: true
                        }
                    }
                ],
                // required: false,
                default: []
            },
            metadata: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                default: {}
            }
        }
    }
};

module.exports = Postbacks;
