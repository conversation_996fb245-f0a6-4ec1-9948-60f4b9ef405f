const mongoose = require('mongoose');

let ManualLaunchs = {
    functions: {},
    database: {
        collection: 'ManualLaunchs',
        connection: 'database_payments',
        fields: {
            charges: {
                type: [mongoose.SchemaTypes.ObjectId],
                required: true
            },
            installments: {
                type: Number,
                required: true
            },
            value: {
                type: Number,
                // required: false,
                default: 0
            },
            paymentDate: {
                type: Date,
                required: true
            },
            processRehearse: {
                type: Boolean,
                required: true
            },
            sentToFinancialErp: {
                type: Boolean,
                required: true
            },
            observation: {
                type: String,
                required: true
            },
            paymentMethod: {
                type: String,
                required: true
            },
            operator: {
                type: String,
                required: true
            },
            boleto: {
                type: {
                    ourNumber: {
                        type: String,
                        // required: false,
                        default: null
                    },
                    digitableLine: {
                        type: String,
                        // required: false,
                        default: null
                    }
                },
                // required: false
            },
            card: {
                type: {
                    tid: String,
                    authorization: String
                },
                // required: false
            },
            studentCredit: {
                type: {
                    cpf: String
                },
                // required: false
            },
            payment: {
                type: {
                    receiptCode: String
                },
                // required: false
            },
            isInvoice: {
                type: Boolean,
                required: true
            },
            invoice: {
                type: {
                    number: {
                        type: String,
                        required: true
                    },
                    cnpj: {
                        type: String,
                        required: true
                    },
                    netValue: {
                        type: Number,
                        required: true
                    }
                },
                required: function () {
                    return this.isInvoice;
                }
            },
            success: {
                type: Boolean,
                // required: false,
                default: false
            },
            chargeback: {
                type: Boolean,
                // required: false,
                default: false
            },
            user: {
                _userId: mongoose.SchemaTypes.ObjectId,
                _userName: String,
                _departamentName: String
            },
            lyrapay: {
                ID_INVOICE: String,
            },
            metadata: mongoose.SchemaTypes.Mixed,
        }
    }
};

module.exports = ManualLaunchs;
