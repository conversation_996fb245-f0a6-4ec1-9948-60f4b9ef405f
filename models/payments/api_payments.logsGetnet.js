const mongoose = require('mongoose');

let LogsGetnet = {
    functions: {},
    database: {
        collection: 'LogsGetnet',
        connection: 'database_payments',
        fields: {
            PAGAMENTO_ID: {
                type: String,
                // required: false,
                default: null
            },
            TID: {
                type: String,
                // required: false,
                default: null
            },
            card: {
                type: String,
                default: null
            },
            brand: {
                type: String,
                default: null
            },
            cpf: {
                type: String,
                default: null
            },
            value: {
                type: String,
                default: null
            },
            code: {
                type: mongoose.SchemaTypes.Mixed,
                // required: false,
                default: '-',
                index: true
            },
            response: {
                type: String,
                // required: false,
                default: '-'
            },
            isBad: {
                type: Boolean,
                default: false
            },
            res: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = LogsGetnet;
