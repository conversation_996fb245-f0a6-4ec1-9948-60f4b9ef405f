const mongoose = require('mongoose');

let LogsChargebacks = {
    functions: {},
    database: {
        collection: 'LogsChargebacks',
        connection: 'database_payments',
        fields: {
            _billingId: {
                type: mongoose.SchemaTypes.ObjectId,
                required: true
            },
            operator: {
                type: String,
                required: true,
                index: true
            },
            reason: {
                type: String,
                required: true,
                index: true
            },
            changeStatusTo: {
                type: String,
                required: true,
                index: true
            },
            user: {
                _userId: mongoose.SchemaTypes.ObjectId,
                _userName: String,
                _departamentName: String
            },
            _chargeOrderId: {
                type: mongoose.SchemaTypes.ObjectId,
                // required: false,
                default: null
            },
            _chargesIds: [mongoose.SchemaTypes.ObjectId],
            _futureChargesIds: [mongoose.SchemaTypes.ObjectId],
            _transactionsIds: [mongoose.SchemaTypes.ObjectId],
            _createdTransactionsIds: [mongoose.SchemaTypes.ObjectId],
        }
    }
};

module.exports = LogsChargebacks;
