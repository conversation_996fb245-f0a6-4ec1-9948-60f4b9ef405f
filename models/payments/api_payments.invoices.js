const mongoose = require('mongoose');

let Invoices = {
    functions: {},
    database: {
        collection: 'Invoices',
        connection: 'database_payments',
        fields: {
            _referenceId: {
                type: String,
                required: true
            },
            serviceProvider: {
                type: String,
                enum: [
                    'cursos_gratuitos_bb',
                    'cursos_gratuitos_caixa'
                ],
                required: true
            },
            items: [
                {
                    _id: false,
                    name: {
                        type: String,
                        required: true
                    },
                    description: {
                        type: String,
                        required: true
                    },
                    amount: {
                        type: Number,
                        required: true
                    }
                }
            ],
            paymentMethods: [
                {
                    _id: false,
                    method: {
                        type: String,
                        enum: [
                            'boleto',
                            'creditCard'
                        ],
                        required: true
                    },
                    maxInstallments: {
                        type: Number,
                        // required: false,
                        default: 1
                    },
                    interest: {
                        type: [
                            {
                                _id: false,
                                installment: Number,
                                percentage: Number
                            }
                        ],
                        // required: false,
                        default: []
                    }
                }
            ],
            dueDate: {
                type: Date,
                required: true
            },
            interest: {
                mulct: {
                    type: Number,
                    required: true
                },
                mulctPerMonth: {
                    type: Number,
                    required: true
                }
            },
            postBack: {
                uri: {
                    type: String,
                    // required: false,
                    default: null
                },
                headers: {
                    type: Object,
                    // required: false,
                    default: {}
                }
            },
            status: {
                type: String,
                required: true,
                enum: [
                    'waiting_payment',
                    'paid',
                    'canceled'
                ],
                default: 'waiting_payment'
            },
            payer: {
                name: String,
                email: String,
                cpf: String,
                address: {
                    zip: String,
                    state: String,
                    city: String,
                    zone: String,
                    street: String,
                    number: String,
                    complement: String
                }
            },
            payment: {
                type: {
                    date: Date,
                    amount: Number,
                    method: String,
                    installments: Number,
                    _billingId: mongoose.SchemaTypes.ObjectId,
                    metadata: mongoose.SchemaTypes.Mixed
                },
                // required: false,
                default: {}
            },
            metadata: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = Invoices;
