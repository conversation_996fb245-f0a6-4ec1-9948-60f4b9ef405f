let ServiceProviders = {
    functions: {},
    database: {
        collection: 'ServiceProviders',
        connection: 'database_payments',
        fields: {
            alias: {
                type: String,
                required: true
            },
            assignor: {
                type: {
                    operator: {
                        type: String,
                        required: true
                    },
                    code: {
                        type: String,
                        required: true
                    },
                    agency: {
                        type: String,
                        required: true
                    },
                    bank: {
                        type: String,
                        required: true
                    },
                    company: {
                        type: {
                            name: {
                                type: String,
                                required: true
                            },
                            cnpj: {
                                type: String,
                                required: true
                            }
                        },
                        required: true
                    },
                    clientId: String,
                    clientSecret: String,
                    walletVariationNumber: String,
                    appKey: String,
                    key: String
                },
                required: true
            }
        }
    }
};

module.exports = ServiceProviders;
