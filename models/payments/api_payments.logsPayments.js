let LogsPayments = {
    functions: {},
    database: {
        collection: 'LogsPayments',
        connection: 'database_payments',
        fields    : {
            operator: {
                type    : String,
                // required: false,
                default : 'caixa'
            },
            ourNumber     : {
                type    : String,
                required: true,
                index   : true
            },
            documentNumber: {
                type    : String,
                required: true,
                index   : true
            },
            payedValue    : {
                type    : String,
                required: true,
                index   : true
            },
            shouldValue   : {
                type    : String,
                required: true
            },
            message       : {
                type     : String,
                required : true,
                lowercase: true
            },
            isBad         : {
                type   : Boolean,
                default: false
            }
    
        }
    }
};

module.exports = LogsPayments;
