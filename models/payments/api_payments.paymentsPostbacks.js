const mongoose = require('mongoose');

let PaymentsPostbacks = {
    functions: {},
    database: {
        collection: 'PaymentsPostbacks',
        connection: 'database_payments',
        fields: {
            method: mongoose.SchemaTypes.Mixed,
            body: mongoose.SchemaTypes.Mixed,
            params: mongoose.SchemaTypes.Mixed,
            query: mongoose.SchemaTypes.Mixed,
            headers: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = PaymentsPostbacks;
