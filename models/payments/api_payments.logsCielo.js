const mongoose = require('mongoose');

let LogsCielo = {
    functions: {},
    database: {
        collection: 'LogsCielo',
        connection: 'database_payments',
        fields: {
            TID: {
                type: String,
                // required: false,
                default: null
            },
            card: {
                type: String,
                default: null
            },
            brand: {
                type: String,
                default: null
            },
            cpf: {
                type: String,
                default: null
            },
            value: {
                type: String,
                default: null
            },
            code: {
                type: String,
                // required: false,
                default: '-',
                index: true
            },
            response: {
                type: String,
                // required: false,
                default: '-'
            },
            isBad: {
                type: Boolean,
                default: false
            },
            res: mongoose.SchemaTypes.Mixed
        }
    }
};

module.exports = LogsCielo;
