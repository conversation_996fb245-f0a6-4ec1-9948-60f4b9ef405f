let Payments = {
    functions: {},
    database: {
        collection: 'Payments',
        connection: 'database_cnab',
        fields: {
            bank: {
                type: String,
                required: true
            },
            assignor: {
                type: String,
                required: true
            },
            filename: {
                type: String,
                required: true
            },
            fileDate: {
                type: String,
                required: true
            },
            fileHour: {
                type: String,
                required: true
            },
            payer: {
                name: String,
                cpf: String
            },
            ourNumber: {
                type: String,
                required: true
            },
            value: {
                type: Number,
                required: true
            },
            payedValue: {
                type: Number,
                required: true
            },
            paymentDate: {
                type: Date,
                required: true
            },
            inProcess: {
                type: Boolean,
                // required: false,
                default: false
            }
        },
        indexes: [
            {
                fields: {
                    bank: 1,
                    assignor: 1,
                    filename: 1,
                    ourNumber: 1,
                    paymentDate: 1
                },
                options: {
                    unique: 'Pagamento já importado'
                }
            }
        ]
    }
};

module.exports = Payments;
