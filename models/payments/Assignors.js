const mongoose = require('mongoose');
let Assignors = {
    database: {
        collection: 'Assignors',
        connection: 'database_piaget',
        fields: {
            assignor: {
                type: String,
                // required: false
            },
            account: {
                type: String,
                // required: false
            },
            accountDv: {
                type: String,
                // required: false
            },
            accountOp: {
                type: String,
                // required: false
            },
            agency: {
                type: String,
                // required: false
            },
            agencyDv: {
                type: String,
                // required: false
            },
            bank: {
                type: String,
                // required: false
            },
            operator: {
                type: String,
                // required: false,
                lowerCase: true
            },
            company: {
                cnpj: {
                    type: String,
                    required: true
                },
                name: {
                    type: String,
                    required: true
                }
            },
            interest: {
                interestType: {
                    type: String,
                    // required: false,
                    enum: [
                        'VALOR_POR_DIA',
                        'TAXA_MENSAL',
                        'ISENTO'
                    ]
                },
                amount: {
                    type: String,
                    // required: false
                }
            },
            mulct: {
                percentage: {
                    type: String,
                    // required: false
                }
            },
            afterExpiration: {
                action: {
                    type: String,
                    // required: false,
                    enum: [
                        'PROTESTAR',
                        'DEVOLVER'
                    ]
                },
                numberOfDays: {
                    type: String,
                    // required: false
                }
            },
            payment: {
                paymentType: {
                    type: String,
                    // required: false,
                    enum: [
                        'ACEITA_QUALQUER_VALOR',
                        'ACEITA_VALORES_ENTRE_MINIMO_MAXIMO',
                        'NAO_ACEITA_VALOR_DIVERGENTE',
                        'SOMENTE_VALOR_MINIMO'
                    ]
                },
                quantityAllowed: {
                    type: String,
                    // required: false,
                    min: '1',
                    max: '99',
                    default: '1'
                }
            },
            dueDateDays: {
                type: Number,
                // required: false,
                default: 5
            },
            config: {
                certifiers: {
                    type: [String],
                    required: true
                },
                _chargeTypeAlias: {
                    type: [String],
                    required: true
                },
                indication: {
                    userType: {
                        type: [String],
                        required: true
                    }
                },
                invoiceFor: {
                    type: [
                        {
                            name: {
                                type: String,
                                required: true
                            },
                            cnpj: {
                                type: String,
                                required: true
                            },
                            courseTypes: {
                                type: [String],
                                // required: false,
                                allowNull: true
                            }
                        }
                    ],
                    // required: false,
                    allowNull: true
                }
            },
            isActive: {
                type: Boolean,
                default: true
            },
            metadata: mongoose.SchemaTypes.Mixed
        },
        indexes: [
            {
                fields: {
                    operator: 1,
                    assignor: 1,
                    config: 1
                },
                options: {
                    unique: 'assignor_already_exists'
                }
            }
        ]
    }
};
/*Assignors.database.post = {
    findOneAndUpdate: Assignors.functions.testFunctionAssignor
}*/
module.exports = Assignors;
