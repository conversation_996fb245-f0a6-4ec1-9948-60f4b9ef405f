const mongoose = require('mongoose');

let PromotionCourses = {
    functions: {
        sortPaymentPlans: function(promotion) {

            const sortFn = (a, b) => a.installment === b.installment ? 0 : (a.installment > b.installment);

            if((promotion.paymentPlan.boleto || []).length){
                promotion.paymentPlan.boleto = promotion.paymentPlan.boleto.sort(sortFn);
            }
            if((promotion.paymentPlan.creditCard || []).length){
                promotion.paymentPlan.creditCard = promotion.paymentPlan.creditCard.sort(sortFn);
            }
            if((promotion.paymentPlan.debitCard || []).length){
                promotion.paymentPlan.debitCard = promotion.paymentPlan.debitCard.sort(sortFn);
            }
            if((promotion.paymentPlan.cardRecurrence || []).length){
                promotion.paymentPlan.cardRecurrence = promotion.paymentPlan.cardRecurrence.sort(sortFn);
            }
            if((promotion.paymentPlan.pix || []).length){
                promotion.paymentPlan.pix = promotion.paymentPlan.pix.sort(sortFn);
            }

            return promotion.save();
        },
        formatTagsAndBlackListTags: function(next) {
            if (!this.tags) this.tags = []
            if (!this.blackListedTags) this.blackListedTags = []
            if (!this._coursesId) this._coursesId = []

            return next()
        },
        parseCourseId: function(next) {
            console.log(this._coursesId, typeof this._coursesId);
            try{
                if (this._coursesId && Array.isArray(this._coursesId) && this._coursesId.length) {
                    this._coursesId = this._coursesId.map(id => new mongoose.Types.ObjectId(id));
                } else if (this._coursesId && !Array.isArray(this._coursesId)) {
                    this._coursesId = new mongoose.Types.ObjectId(this._coursesId);
                } else {
                    this._coursesId = []
                }
                next();
            } catch (e) {
                console.error(e);
                next(e);
            }
        }
    },
    database: {
        connection: 'database_piaget',
        collection: 'PromotionCourses',
        fields    : {
            name      : {
                type    : String,
                required: true
            },
            regulation: {
                type     : String,
                allowNull: false
            },
            tags: mongoose.SchemaTypes.Mixed,
            blackListedTags: mongoose.SchemaTypes.Mixed,
            _coursesId: mongoose.SchemaTypes.Mixed,
            paymentPlan         : {
                creditCard: [
                    {
                        _id: false,
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                ],
                debitCard: [
                    {
                        _id: false,
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                ],
                cardRecurrence: [
                    {
                        _id: false,
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                ],
                boleto: [
                    {
                        _id: false,
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                ],
                pix: [
                    {
                        _id: false,
                        installment: {
                            type: Number,
                            required: true
                        },
                        value: {
                            type: Number,
                            required: true
                        }
                    }
                ]
            },
            dateStart : {
                type     : Date,
                allowNull: false
            },
            dateEnd   : {
                type     : Date,
                allowNull: false
            },
            isActive  : {
                type     : Boolean,
                default  : true,
                allowNull: false
            }
        },
        options   : {
            timestamps: true
        }
    }
};

PromotionCourses.database.pre = {
    save: [
        PromotionCourses.functions.formatTagsAndBlackListTags,
        PromotionCourses.functions.parseCourseId
    ],
    findOneAndUpdate: [
        PromotionCourses.functions.parseCourseId
    ],
    updateOne: [
        PromotionCourses.functions.parseCourseId
    ],
    updateMany: [
        PromotionCourses.functions.parseCourseId
    ]
}

module.exports = PromotionCourses;
