let RateEnrolments = {
    functions: {
        sortPaymentPlans: function (rate) {

            const sortFn = (a, b) => a.installment === b.installment ? 0 : (a.installment > b.installment);

            if ((rate.paymentPlan.boleto || []).length) {
                rate.paymentPlan.boleto = rate.paymentPlan.boleto.sort(sortFn);
            }

            if ((rate.paymentPlan.creditCard || []).length) {
                rate.paymentPlan.creditCard = rate.paymentPlan.creditCard.sort(sortFn);
            }

            if ((rate.paymentPlan.debitCard || []).length) {
                rate.paymentPlan.debitCard = rate.paymentPlan.debitCard.sort(sortFn);
            }

            if ((rate.paymentPlan.cardRecurrence || []).length > 0) {
                rate.paymentPlan.cardRecurrence = rate.paymentPlan.cardRecurrence.sort(sortFn);
            }

            return rate.save();
        },
    },
    database: {
        connection: 'database_piaget',
        collection: 'RateEnrolments',
        fields: {
            _certifierName: {
                type: String,
                required: true
            },
            _typeName: {
                type: String,
                required: true
            },
            paymentPlan: {
                creditCard: [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                debitCard : [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ],
                boleto    : [
                    {
                        _id        : false,
                        installment: {
                            type    : Number,
                            required: true
                        },
                        value      : {
                            type    : Number,
                            required: true
                        }
                    }
                ]
            },
            isActive: {
                type: Boolean,
                required    : true,
                default     : true,
                index       : true
            }
        }
    }
};

module.exports = RateEnrolments;
