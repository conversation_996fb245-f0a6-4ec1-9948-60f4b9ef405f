const mongoose = require('mongoose');

let InternshipsCompanies = {
    functions: {
    },
    database: {
        collection: 'InternshipsCompanies',
        connection: 'database_piaget',
        fields: {
            fantasyName: {
                type: String,
                required: true,
            },
            name: {
                type: String,
                // required: false,
            },
            cnpj: {
                type: String,
                required: false
            },
            phone: {
                type: String,
                required: true,
            },
            email: {
                type: String,
                required: true,
                unique: true,
                index: true
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            areas: {
                type: [String],
            },
            letter: {
                type: String
            },
            hasSendFile: {
                type: Boolean,
                default: false
            },
            certifiers: {
                type: [String],
            },
            companyBranches: {
                type: [
                    {
                        nameLegalRepresentative: String,
                        phone: String,
                        email: String,
                        address: {
                            zip: String,
                            street: String,
                            complement: String,
                            number: String,
                            zone: String,
                            uf: String,
                            city: String,
                            country: String
                        }
                    }
                ],
                // required: false
            },
            address: {
                zip: String,
                street: String,
                complement: String,
                number: String,
                zone: String,
                uf: String,
                city: String,
                country: String
            },
            legalRepresentative: {
               name: String,
               job: String,
               cpf: String,
               function: String,
               legalRepresentativeAddress: {
                    zip: String,
                    street: String,
                    complement: String,
                    number: String,
                    zone: String,
                    uf: String,
                    city: String,
                    country: String
                }
            },
            observation: {
                type: String,
                // required: false
            },
            status: {
                type: String,
                default: 'pre-register'
            },
            term: {
                type: String
            },
            contractDateStart: {
                type: Date
            },
            contractDateEnd: {
                type: Date
            },
            contractsFiles: {
                type: [
                    {
                        name: String,
                        url: String
                    }
                ]
            },
            othersFiles: {
                type: [
                    {
                        name: String,
                        url: String
                    }
                ]
            },
            expiredDate: {
                type: Date
            },
            indicatedStudent: {
                type: {
                    _id: mongoose.SchemaTypes.ObjectId,
                    name: String,
                    cpf: String,
                    email: String
                }
            }
        }
    }
};

module.exports = InternshipsCompanies;
