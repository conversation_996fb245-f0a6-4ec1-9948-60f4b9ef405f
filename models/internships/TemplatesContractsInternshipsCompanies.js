let TemplatesContractsInternshipsCompanies = {
    functions: {
    },
    database: {
        collection: 'TemplatesContractsInternshipsCompanies',
        connection: 'database_piaget',
        fields: {
            name: {
                type: String,
                required: true,
            },
            description: {
                type: String,
                // required: false,
            },
            template: {
                type: String,
                required: true,
            },
            certifiers: {
                type: String,
                required: true,
            },
            typeModels: {
                type: [String],
                required: true,
            },
            areas: {
                type: [String],
                required: true,
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = TemplatesContractsInternshipsCompanies;
