let InternshipsDocuments = {
    functions: {
    },
    database: {
        collection: 'InternshipsDocuments',
        connection: 'database_piaget',
        fields: {
            title: {
                type: String,
                required: true,
            },
            content: {
                type: String,
                // required: false,
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            certifiers: {
                type: [String],
            },
            typeNames: {
                type: [String],
            },
            area: {
                type: [String],
            },
            typeDocument: {
                type: String,
                required: true,
            },
            blankModel: {
                type: Boolean,
                default: false
            },
            typeInternship: {
                type: String,
                enum: [
                    'required',
                    'no-required'
                ]
            },
            file: {
                type: String
            },
            useTemplate: {
                type: Boolean,
                default: true
            }
        }
    }
};

module.exports = InternshipsDocuments;
