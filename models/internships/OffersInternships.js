let OffersInternships = {
    functions: {
    },
    database: {
        collection: 'OffersInternships',
        fields: {
            dateStart: {
                type: Date,
                required: true,
            },
            dateEnd: {
                type: Date,
                required: true,
            },
            vacancies: {
                type: Number,
                required: true,
            },
            title: {
                type: String,
                required: true,
                index: true
            },
            offerType: {
                type: String,
                required: false
            },
            description: {
                type: String,
                // required: false
            },
            isActive: {
                type: Boolean,
                default: true
            },
            isVisible: {
                type: Boolean,
                default: true
            },
            image: {
                type: String,
                // required: false
            },
            certifier: {
                type: [String],
                // required: false
            },
            typeName: {
                type: [String],
                // required: false
            }
        }
    }
};

module.exports = OffersInternships;

// Prazo de vigencia;

// Quantidade de vagas;

// Anexar imagem;

// Titulo;

// Descrição;
