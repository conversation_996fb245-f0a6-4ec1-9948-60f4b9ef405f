steps:
  # Criar pasta caso nao existir
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'create-tmp-folder'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        IFS=',' read -ra VMS <<< "${_VM}"

        for vm in "${VMS[@]}"; do
          REGION=$(bash build_scripts/vm_region.sh "$vm")
          gcloud compute ssh services@$$vm --command "sudo rm -rf /tmp/${_FOLDER} && mkdir -p /tmp/${_FOLDER}" --zone $$REGION;
        done

  # Transferir arquivos diretamente para a VM usando gcloud compute scp
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'copy-files'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        IFS=',' read -ra VMS <<< "${_VM}"

        for vm in "${VMS[@]}"; do
          REGION=$(bash build_scripts/vm_region.sh "$vm")
          gcloud compute scp --recurse --compress --quiet /workspace services@$$vm:/tmp/${_FOLDER} --zone $$REGION;
        done

  # Preparar package.json
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'prepare-package-json'
    entrypoint: 'gcloud'
    args: [
      'compute', 'ssh',
      'services@${_VM}',
      '--command', 'cd /tmp/${_FOLDER}/workspace && export CMD="bash build_scripts/prepare_package_json.sh" && if [ "${_ROOT}" = "true" ]; then sudo $$CMD; else $$CMD; fi',
      '--zone', 'us-west1-a', '--internal-ip'
    ]

  # Instalar dependências
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-dependencies'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        IFS=',' read -ra VMS <<< "${_VM}"

        for vm in "${VMS[@]}"; do
          REGION=$(bash build_scripts/vm_region.sh "$vm")
          ROOT=$(bash build_scripts/vm_root.sh "$vm")
          gcloud compute ssh services@$$vm --command "cd /tmp/${_FOLDER}/workspace && bash build_scripts/npm.sh ${_NODE_VERSION} i" --zone $$REGION;

          STATUS=$?

          if [ $$STATUS -ne 0 ]; then
            echo "Erro ao executar o comando na VM $$vm (status $$STATUS)"
            exit 1
          fi
        done

  # Instalar rsync (se nao houver)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'install-rsync'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        IFS=',' read -ra VMS <<< "${_VM}"

        for vm in "${VMS[@]}"; do
          REGION=$(bash build_scripts/vm_region.sh "$vm")
          gcloud compute ssh services@$$vm --command "sudo apt-get install -y rsync" --zone $$REGION;
        done

  # Mover arquivos para o diretório final usando um script remoto
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:alpine'
    id: 'move-files'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        IFS=',' read -ra VMS <<< "${_VM}"

        for vm in "${VMS[@]}"; do
          FINAL_PATH=$(bash build_scripts/vm_path.sh "$vm")
          REGION=$(bash build_scripts/vm_region.sh "$vm")
          ROOT=$(bash build_scripts/vm_root.sh "$vm")

          if [ -z "$$FINAL_PATH" ]; then
            echo "Erro ao obter o caminho final para a VM $vm"
            exit 1
          else
            echo "Movendo para o caminho final `$$FINAL_PATH` para a VM `$vm`"
          fi

          if [ "$$ROOT" = "true" ]; then
            REMOTE_COMMAND="sudo rsync -av --remove-source-files /tmp/${_FOLDER}/workspace/* $$FINAL_PATH"
          else
            REMOTE_COMMAND="rsync -av --remove-source-files /tmp/${_FOLDER}/workspace/* $$FINAL_PATH"
          fi

          gcloud compute ssh services@$$vm --zone $$REGION --command "$$REMOTE_COMMAND"
        done


  # Notificar sucesso no Discord
  - name: 'curlimages/curl'
    id: 'notify-success'
    entrypoint: 'curl'
    args: [
      '-X', 'POST', '$_DISCORD_WEBHOOK_URL',
      '-H', 'Content-Type: application/json',
      '--data', '{"content": ":white_check_mark: Build concluído com sucesso! Build: $TRIGGER_NAME ($REF_NAME). Veja os logs: https://console.cloud.google.com/cloud-build/builds;region=$LOCATION/$BUILD_ID?project=$PROJECT_ID"}'
    ]
    waitFor: [ 'move-files' ]

timeout: '1200s'

options:
  logging: 'CLOUD_LOGGING_ONLY'
  pool:
    name: projects/erp-prominas/locations/us-west1/workerPools/builds

# Variaveis de substituição
substitutions:
  _VM: 'api-gateway-auth-2024,api-gateway-auth2-2024,api-leads-2024,api-partner-portal-2024,api-student-portal-ava-2024,crons-runner-2024,piaget-public-api-2024,piaget-public-api-2,services-erp-1-2024,services-erp-2-2024-2,services-erp-3-2024'
  _NODE_VERSION: '20.16.0'
  _FOLDER: 'piaget_models_20'
  _DISCORD_WEBHOOK_URL: 'https://discord.com/api/webhooks/1279076153616896080/nkdczu_0_sNdwRnxGyFLMrXawXOWysbWazcH7Kdf01YgxkwgK-wt2yoCM7pr3k5tL60V'
