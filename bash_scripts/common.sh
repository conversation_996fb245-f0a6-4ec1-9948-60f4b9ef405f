#!/usr/bin/env bash

# Color codes for output formatting
red='\033[0;31m'
green='\033[0;32m'
yellow='\033[1;33m'
reset='\033[0m'

# Function to set output for GitHub Actions (if running in CI)
set_output() {
    local name="$1"
    local value="$2"
    
    if [[ -n "${GITHUB_OUTPUT}" ]]; then
        echo "${name}=${value}" >> "${GITHUB_OUTPUT}"
    else
        echo "Output: ${name}=${value}"
    fi
}

# Function to display success messages
success() {
    local message="$1"
    echo -e "${green}✓ ${message}${reset}"
}

# Function to display warning messages
warn() {
    local message="$1"
    echo -e "${yellow}⚠ ${message}${reset}"
}

# Function to display error messages
fail() {
    local message="$1"
    echo -e "${red}✗ ${message}${reset}"
    exit 1
}
