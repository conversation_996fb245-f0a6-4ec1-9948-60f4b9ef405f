const mongoose = require('mongoose');

class UtilsFunctionToObjectId {
  convert(input) {
    if (!mongoose.Types.ObjectId.isValid(input)) return null;
    return new mongoose.Types.ObjectId(String(input));
  }

  compareObjectId(element1, element2) {
    return (element1 || 'a').toString() === (element2 || 'b').toString();
  }
}

// Export a singleton instance instead of the class
const utilsFunctionToObjectId = new UtilsFunctionToObjectId();

module.exports = utilsFunctionToObjectId;
